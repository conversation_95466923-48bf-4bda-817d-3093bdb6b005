// ignore_for_file: avoid_print

import 'dart:io';

/// To create a new test file from a specific widget:
/// - First, copy the path to that widget file (e.g., lib/widgets/general/general_blog_category_widget.dart).
/// - Then, open the terminal from the root project and type the following command:
/// ```
///     dart test/script_create_test.dart <path-file-need-test.dart>
/// ```
/// The test file will be created in the test folder and will have a similar path as in lib.
/// For example, creating a file for lib/widgets/general/general_blog_category_widget.dart
/// will result in the test file being test/widgets/general/general_blog_category_widget_test.dart.
void main(List<String> args) {
  final pathFile = args[0];

  final newFile =
      pathFile.replaceAll('.dart', '_test.dart').replaceAll('lib', 'test');
  final fileTest = File(newFile);
  if (fileTest.existsSync() == false) {
    fileTest.createSync(recursive: true);
    fileTest.writeAsStringSync(
        'import \'package:flutter_test/flutter_test.dart\';\n\nvoid main() {\n  test(\'\', () {\n    expect(1, 1);\n  });\n}');

    print('✅ File created: $newFile');
  } else {
    print('⛔️ File already exists: $newFile');
  }
}
