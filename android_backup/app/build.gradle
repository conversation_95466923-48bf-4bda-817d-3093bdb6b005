plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

apply plugin: 'com.google.gms.google-services'



def flavorActions = [
    assembleProdRelease: "prod",
    assembleDevRelease: "dev",
    assembleProdDebug: "prod",
    assembleDevDebug: "dev",
]
def argsBuild = gradle.getStartParameter().getTaskRequests()['args'][0][0].toString()
def flavorTemp = flavorActions.get(argsBuild);
def flavor = flavorTemp ? flavorTemp : 'prod'
def fileName = "key${flavor}.properties"

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file(fileName)
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}


android {
    compileSdkVersion 35

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
    }

    compileOptions {
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled true

        // Sets Java compatibility to Java 8
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.inapps.medita"
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        multiDexEnabled true
//        ndk.abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
    }

//     signingConfigs {
//         release {
//             keyAlias keystoreProperties['keyAlias']
//             keyPassword keystoreProperties['keyPassword']
//             storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
//             storePassword keystoreProperties['storePassword']
//         }
//     }

//     buildTypes {
//         release {
//             // TODO: Add your own signing config for the release build.
//             // Signing with the debug keys for now, so `flutter run --release` works.
//             signingConfig signingConfigs.release
//             shrinkResources false
// //            signingConfig signingConfigs.debug
//         }
//     }

    signingConfigs {
        release {
            if (System.getenv()["CI"]) {
                keyAlias = "$System.env.SIGNING_KEY_ALIAS"
                keyPassword = "$System.env.SIGNING_KEY_PASSWORD"
                storeFile file("dev.keystore")
                storePassword = "$System.env.SIGNING_STORE_PASSWORD"
            }
            else {
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
                storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
                storePassword keystoreProperties['storePassword']
            }
        }

        debug {
            if (System.getenv()["CI"]) {
                keyAlias = "$System.env.SIGNING_KEY_ALIAS"
                keyPassword = "$System.env.SIGNING_KEY_PASSWORD"
                storeFile file("dev.keystore")
                storePassword = "$System.env.SIGNING_STORE_PASSWORD"
            }
            else {
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
                storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
                storePassword keystoreProperties['storePassword']
            }
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.debug
        }
    }
    namespace 'com.inapps.medita'
    flavorDimensions 'mode'
    productFlavors {
        dev {
            dimension "mode"
            applicationId "com.inapps.medita" 
            applicationIdSuffix ".dev"
            resValue "string", "app_name", "MeditaDev"
        }
        prod {
            dimension "mode"
            applicationId "com.inapps.medita" 
            resValue "string", "app_name", "Thiền Thức Tỉnh"
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    // Eliminate this line (or the entire dependencies block)
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.20"
    implementation "androidx.multidex:multidex:2.0.1"
    implementation 'com.google.android.material:material:1.12.0'
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.3")
}
