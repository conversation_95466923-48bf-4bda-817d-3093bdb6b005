## 2.2.7
- fix issue incorrect cupertino icon

## 2.2.6
- 🚀 Compatible with Flutter v3.29

## 2.2.5
- feat: add LifecycleMixin and LifecycleEventHandler
- feat: add constructor and fromShopify method to Coupon class

## 2.2.4
- 🚀 Compatible with Flutter v3.27

## 2.2.3
- Update latest library

## 2.2.2
- feat(extension): add timeAgo extension for DateTime

## 2.2.1
- 🚀 Compatible with Flutter v3.24

## 2.2.0
- feat(KeepAliveWidget): ✨ add KeepAliveWidget
- feat(Exception):✨ add clearExceptionKey extension
- feat(ErroPage): ✨ add ErrorPage
- feat(translate): ✨ add: translate minutes text
- refactor(EvenBus): remove unused class and add new event for appbar, tabbar
- update(HttpClient): return header response when use post dio

## 2.1.0
- Support Flutter 3.22.x
- Update HttpClient: enhance use proxy for web
- fix video mp4 page not found
- Add new widget LimitTextScaleWidget
- Update Text color in Circle button text
- Update text color based on backggroundColor

## 2.0.0
- Update Flutter 3.19.0
- Remove <PERSON>InsetsDirectional copyWith (already in fluter 3.19)

## 1.7.1
- update(StringExt): replace toTitleCase to toUpperAllFirstLetter
- fix(Coupon): don't work on Prestashop
- update(coupon) quantity coupon presta
- feat(BuildContextExt): add textScaleFactor

## 1.7.0
- Update flutter 3.16.0
- Add widget WillPopScopeWidget
- ScreenUtil: Replaces `textScaleFactor` with `textScaler` 

## 1.6.2
- Update flutter 3.13.9
- Add textStyle for Coupon
- Fix upper case for empty string
- Add edge insets directional extension
- Add color extension (getColorBasedOnBackground)
- Refactor isEmptyOrNull function

## 1.6.1
- Update flutter 3.13.6
- Support customizing Size and Padding of Coupon Icon

## 1.6.0
- Update flutter 3.13.0

## 1.5.1
- Update latest library
- Update circle button text

## 1.5.0
- Update library compatible with Flutter 3.10

## 1.4.2
- Update latest library

## 1.4.1
- Update latest library
- Fix coupon icon

## 1.4.0
- Update latest library
- Update No Internet Connection message

## 1.3.5
- Fix email regex for case sensitive

## 1.3.4
- Update latest libraries
- Add string ext proxy

## 1.2.0
- Add SafeArea for platform error screen
- Add EventAddNewTabBar event bus
- Add datetime copyWith extension
- Refactor print methods
- Update the lib compatible with FluxStore 3.3

## 1.1.8
- Update Flutter 3.3 compatible

## 1.1.7
- Update Instagram link

## 1.1.6
- Update latest lib

## 1.1.4
- Update kVoidwidget

## 1.1.3
- Add RegexUtils & extensions
- Add TextEditingController extension
- Update flutter_svg

## 1.1.2
- Update expansion widget

## 1.1.1
- Update latest flutter_lints

## 1.1.0
- Update latest Flutter 3

## 1.0.17
- Update latest library

## 1.0.15
- Fix Flutter 2.10.3 missing icon
- Add function to encode & decode base64

## 1.0.15
- Fix Flutter 2.10.2 missing icon

## 1.0.14
- Fix icons issues

## 1.0.13
- Support Flutter 2.10.x
- Update the latest libraries

## 1.0.12
- Update new Story feature
- Update the latest libraries

## 1.0.11
- Update latest library

## 1.0.10
- Support Flutter 2.8.x

## 1.0.9
- Update latest library

## 1.0.8
- Update latest library

## 1.0.5
- Update latest library

## 1.0.4
- Support Caching

## 1.0.3
- Support Flutter 2.5.x

## 1.0.2
- Update compatible Web library

## 1.0.1
- Update latest library

## 1.0.0
- Release first version