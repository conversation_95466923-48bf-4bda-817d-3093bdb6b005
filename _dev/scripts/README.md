

## Use script fastlane
Usage: bash fastlane.sh <options>

Script build and upload to Firebase or Store with Fastlane.
Options: 

```
-h,--help       display this usage message and exit
--all           Build android and ios, then upload them to Firebase and Store (Anroid is alpla test, Ios is Testflight)
--all-dev       Build android and ios, then upload them to Firebase
--all-prod      Build android and ios, then upload them to Store (Anroid is alpla test, Ios is Testflight)
--ios           Only build iOS, then upload them to Firebase and TestFlight
--ios-dev       Only build iOS, then upload them to Firebase
--ios-prod      Only build iOS, then upload them to TestFlight
--android       Only build Android, then upload them to Firebase and Play Store (alpha test)
--android-dev   Only build Android, then upload them to Firebase  
--android-prod  Only build Android, then upload them to Play Store (alpha test)
```


## Use script remove-OneSignal.rb

### Run
- Run the script with terminal at the root of the project 

```
ruby scripts/remove-OneSignal.rb
```

- Run the script by entering the directory path of the project

```
ruby remove-OneSignal.rb <path_project_folder>
```

### The effects of the script on the project:
- Update file `ios/Runner.xcodeproj/project.pbxproj` (delete all things related to OneSignal)
- Remove folder `ios/OneSignalNotificationServiceExtension`
- Remove config OneSignal group in `ios/Runner/Runner.entitlements`
- Remove code config OneSginal in `ios/Podfile`
- Run `pod deintegrate` to clean Pod file and `ios/Runner.xcodeproj/project.pbxproj` file

## Use script remove_notification_service_extension_ios.rb

### Run
- Run the script with terminal at the root of the project 

```
ruby remove_notification_service_extension_ios.rb <service_extension_name>
```
Ex: 

```
# Remove FirebaseNotificationServiceExtension
ruby _dev/scripts/remove_notification_service_extension_ios.rb FirebaseNotificationServiceExtension

# Remove OneSignalNotificationServiceExtension
ruby _dev/scripts/remove_notification_service_extension_ios.rb OneSignalNotificationServiceExtension
```