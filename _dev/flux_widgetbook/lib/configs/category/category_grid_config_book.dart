import 'package:collection/collection.dart';
import 'package:flux_ui/flux_ui.dart';
import 'package:widgetbook/widgetbook.dart';

import '../extension/flux_grid_view_config_knob.dart';

CardGridLayoutConfig createCardGridLayoutConfig(KnobsBuilder knobs) {
  return CardGridLayoutConfig(
    gridConfig: knobs.fluxGridViewConfig(
      label: 'Grid',
      crossAxisCount: 2,
      crossAxisSpacing: 8.0,
      mainAxisSpacing: 8.0,
      childAspectRatio: 16 / 9,
      paddingVertical: 16.0,
    ),
    borderRadius: knobs.double.slider(
      label: 'Border Radius',
      min: 0,
      max: 50,
      initialValue: 8.0,
    ),
    items: _dummyData,
  );
}

final _images = List.generate(
  10,
  (index) => 'https://picsum.photos/640/360?random=${index + 1}',
);

final _dummyData = const <CardGridItemConfig>[
  CardGridItemConfig(
    id: '1',
    name: 'Indoor Plants',
    imageUrl: '',
  ),
  CardGridItemConfig(
    id: '2',
    name: 'Outdoor Plants',
    imageUrl: '',
  ),
  CardGridItemConfig(
    id: '3',
    name: 'Herb',
    imageUrl: '',
  ),
  CardGridItemConfig(
    id: '4',
    name: 'Succulent',
    imageUrl: '',
  ),
  CardGridItemConfig(
    id: '5',
    name: 'Flowering Plants',
    imageUrl: '',
  ),
  CardGridItemConfig(
    id: '6',
    name: 'Cacti fadsf fdsf sdfsd fds',
    imageUrl: '',
  ),
].mapIndexed((index, item) => item.copyWith(imageUrl: _images[index])).toList();
