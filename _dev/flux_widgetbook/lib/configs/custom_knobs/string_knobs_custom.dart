import 'package:flutter/material.dart';
import 'package:widgetbook/widgetbook.dart';

extension StringKnobsCustom on KnobsBuilder {
  /// Creates a textfield that can be typed in
  String stringCustom({
    required String label,
    String? description,
    String initialValue = '',
    $int? maxLines = 1,
    $int? minLine = 1,
  }) {
    return onKnobAdded(
      StringKnobCustom(
        label: label,
        initialValue: initialValue,
        description: description,
        maxLines: maxLines,
        minLine: minLine,
      ),
    )!;
  }
}

class StringKnobCustom extends Knob<String?> {
  StringKnobCustom({
    required super.label,
    required super.initialValue,
    super.description,
    this.maxLines,
    this.minLine,
  });

  StringKnobCustom.nullable({
    required super.label,
    required super.initialValue,
    super.description,
    this.maxLines,
    this.minLine,
  }) : super(isNullable: true);

  final int? maxLines;
  final int? minLine;

  @override
  List<Field> get fields {
    return [
      StringFieldCustom(
        name: label,
        minLine: minLine,
        initialValue: initialValue,
        maxLines: maxLines,
      ),
    ];
  }

  @override
  String? valueFromQueryGroup(Map<String, String> group) {
    return valueOf(label, group);
  }
}

/// [Field] that builds [TextFormField] for [String] values.
class StringFieldCustom extends Field<String> {
  StringFieldCustom({
    required super.name,
    super.initialValue = '',
    this.maxLines,
    this.minLine,
    super.onChanged,
  }) : super(
          type: FieldType.string,
          codec: FieldCodec(
            toParam: (value) => Uri.encodeComponent(value),
            toValue: (param) => param != null
                ? Uri.decodeComponent(param) //
                : null,
          ),
        );

  final int? minLine;
  final int? maxLines;

  @override
  Widget toWidget(BuildContext context, String group, String? value) {
    return TextFormField(
      maxLines: maxLines,
      minLines: minLine,
      initialValue: value ?? initialValue,
      onChanged: (value) => updateField(context, group, value),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'maxLines': maxLines,
    };
  }
}
