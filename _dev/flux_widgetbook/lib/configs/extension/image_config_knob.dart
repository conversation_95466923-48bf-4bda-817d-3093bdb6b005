import 'package:flutter/material.dart';
import 'package:flux_ui/flux_ui.dart';
import 'package:widgetbook/widgetbook.dart';

import 'box_fit_knob.dart';

extension ImageConfigKnobExt on KnobsBuilder {
  ImageConfig imageConfigKnob({
    String label = '',
    double aspectRatio = 1.0,
    Color? color,
    BoxFit? fit,
    double borderRadius = 8,
  }) {
    return ImageConfig(
      aspectRatio: this.double.slider(
            label: '$label Image Aspect Ratio',
            min: 0.3,
            max: 4,
            initialValue: 1,
          ),
      borderRadius: BorderRadiusDirectional.circular(
        this.double.slider(
              label: '$label Image Border Radius',
              min: 0,
              max: 32,
              initialValue: borderRadius,
            ),
      ),
      fit: boxFit(label: label),
    );
  }
}
