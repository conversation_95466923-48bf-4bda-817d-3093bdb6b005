import 'package:flutter/material.dart';
import 'package:flux_ui/flux_ui.dart';
import 'package:widgetbook/widgetbook.dart';

WidgetbookLeafComponent backgroundColorWidgetWidgetbookComponent() =>
    WidgetbookLeafComponent(
      name: 'BackgroundColorWidget',
      useCase: _backgroundColorWidgetWidgetbook(),
    );

WidgetbookUseCase _backgroundColorWidgetWidgetbook() => WidgetbookUseCase(
      name: 'BackgroundColorWidget',
      builder: (context) => const _BackgroundColorWidgetWidgetbook(),
    );

class _BackgroundColorWidgetWidgetbook extends StatelessWidget {
  const _BackgroundColorWidgetWidgetbook();

  @override
  Widget build(BuildContext context) {
    final knobs = context.knobs;

    return SafeArea(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          BackgroundColorWidget(
            color: knobs.color(
              label: 'Background Color',
              initialValue: Colors.blue,
            ),
            enable: knobs.boolean(
              label: 'Enable',
              initialValue: true,
            ),
            child: const SizedBox(
              height: 100,
              child: Center(
                child: Text(
                  'Hello World',
                  style: TextStyle(
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
