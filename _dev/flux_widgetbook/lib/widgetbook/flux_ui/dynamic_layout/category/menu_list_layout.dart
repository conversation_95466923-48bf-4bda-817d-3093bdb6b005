import 'package:flutter/material.dart';
import 'package:flux_ui/flux_ui.dart';
import 'package:widgetbook/widgetbook.dart';

import '../../../../configs/category/menu_list_layout_config_book.dart';

WidgetbookLeafComponent menuListLayoutWidgetbookComponent() =>
    WidgetbookLeafComponent(
      name: 'MenuListLayout',
      useCase: _menuListLayoutWidgetBook(),
    );

WidgetbookUseCase _menuListLayoutWidgetBook() => WidgetbookUseCase(
      name: 'MenuListLayout',
      builder: (context) => const _MenuListLayoutWidgetBook(),
    );

class _MenuListLayoutWidgetBook extends StatelessWidget {
  const _MenuListLayoutWidgetBook();

  @override
  Widget build(BuildContext context) {
    final knobs = context.knobs;
    return SafeArea(
      child: MenuListLayout(
        config: createMenuListLayoutConfig(knobs),
      ),
    );
  }
}
