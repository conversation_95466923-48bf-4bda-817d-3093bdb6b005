import 'package:flutter/material.dart';
import 'package:flux_ui/flux_ui.dart';
import 'package:widgetbook/widgetbook.dart';

import '../../../../configs/header/header_config.dart';

WidgetbookLeafComponent headerTypeWidgetbookComponent() =>
    WidgetbookLeafComponent(
      name: 'HeaderType',
      useCase: _headerTypeWidgetbook(),
    );

WidgetbookUseCase _headerTypeWidgetbook() => WidgetbookUseCase(
      name: 'HeaderType',
      builder: (context) => const _HeaderTypeWidgetbook(),
    );

class _HeaderTypeWidgetbook extends StatelessWidget {
  const _HeaderTypeWidgetbook();

  @override
  Widget build(BuildContext context) {
    final knobs = context.knobs;

    return SafeArea(
      child: HeaderType(
        config: createHeaderConfig(knobs),
        getReplacedParams: (String? p0) => p0 ?? '',
      ),
    );
  }
}
