const { onCall } = require("firebase-functions/v2/https");
const admin = require('firebase-admin');

// Import services
const { validateCouponUsecase, applyCouponUsecase, updateOrderCouponUsecase } = require('./services/coupon');
const { checkBookingAvailabilityUsecase, bookingServicesUsecase } = require('./services/booking');

admin.initializeApp();

/// -------   Coupon endpoints  ------- ///
exports.validateCoupon = onCall(async(request) => {
    return await validateCouponUsecase(request);
});

exports.applyCoupon = onCall(async(request) => {
    return await applyCouponUsecase(request);
});

exports.updateOrderCoupon = onCall(async(request) => {
    return await updateOrderCouponUsecase(request);
});

/// -------   Booking endpoints  ------- ///
exports.checkBookingAvailability = onCall(async(request) => {
    return await checkBookingAvailabilityUsecase(request.data);
});

exports.bookingServices = onCall(async(request) => {
    return await bookingServicesUsecase(request);
});
