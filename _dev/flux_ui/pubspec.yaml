name: flux_ui
description: "The Basic and Dynamic Layout Widgets for FluxBuilder (Mobile E-commerce App) - https://fluxbuilder.com"
version: 0.0.7
homepage: https://fluxbuilder.com
publish_to: none # TODO:update when to publish

environment:
  sdk: ^3.5.0
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter

  # TODO:update when to publish
  inspireui:
    path: ../common_library

  intl: any
  quiver: 3.2.2
  responsive_builder: 0.7.1
  universal_platform: ^1.1.0
  lottie: ^3.1.2
  extended_image: 10.0.1
  share_plus: 10.1.4
  flutter_svg: ^2.0.13
  animated_text_kit: 4.2.3
  html: ^0.15.4
  url_launcher: 6.3.1
  collection: any
  freezed_annotation: 2.4.4
  html_unescape: 2.0.0
  visibility_detector: 0.4.0+2
  smooth_page_indicator: 1.2.0+3
  carousel_slider_plus: 7.1.0

    # HTML render
  flutter_widget_from_html_core: ^0.15.2
  fwfh_svg: 0.8.3
  fwfh_cached_network_image: 0.14.3
  fwfh_url_launcher: 0.9.1
  fwfh_chewie: 0.14.8
  fwfh_webview: 0.15.2
  google_fonts: ^6.2.1
  build_runner: ^2.4.13
  json_annotation: ^4.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  mockito: ^5.4.4
  bloc_test: ^9.1.7
  test: ^1.25.7
  freezed: 2.5.3
  json_serializable: ^6.8.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  # To add assets to your package, add an assets section, like this:
  assets:
    - assets/
    - assets/test/
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/to/asset-from-package
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/to/font-from-package
