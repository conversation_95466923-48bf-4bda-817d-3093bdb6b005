// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'flux_card_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FluxCardConfig _$FluxCardConfigFromJson(Map<String, dynamic> json) {
  return _FluxCardConfig.fromJson(json);
}

/// @nodoc
mixin _$FluxCardConfig {
  @ColorConverter()
  Color? get color => throw _privateConstructorUsedError;
  @ColorConverter()
  Color? get shadowColor => throw _privateConstructorUsedError;
  @ColorConverter()
  Color? get surfaceTintColor => throw _privateConstructorUsedError;
  double? get elevation => throw _privateConstructorUsedError;
  @BorderRadiusDirectionalConverter()
  BorderRadiusDirectional get borderRadius =>
      throw _privateConstructorUsedError;
  @BorderSideConverter()
  BorderSide get borderSide => throw _privateConstructorUsedError;
  @ClipConverter()
  Clip? get clipBehavior => throw _privateConstructorUsedError;
  @EdgeInsetsDirectionalConverter()
  EdgeInsetsDirectional? get margin => throw _privateConstructorUsedError;
  @EdgeInsetsDirectionalConverter()
  EdgeInsetsDirectional? get contentPadding =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FluxCardConfigCopyWith<FluxCardConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FluxCardConfigCopyWith<$Res> {
  factory $FluxCardConfigCopyWith(
          FluxCardConfig value, $Res Function(FluxCardConfig) then) =
      _$FluxCardConfigCopyWithImpl<$Res, FluxCardConfig>;
  @useResult
  $Res call(
      {@ColorConverter() Color? color,
      @ColorConverter() Color? shadowColor,
      @ColorConverter() Color? surfaceTintColor,
      double? elevation,
      @BorderRadiusDirectionalConverter() BorderRadiusDirectional borderRadius,
      @BorderSideConverter() BorderSide borderSide,
      @ClipConverter() Clip? clipBehavior,
      @EdgeInsetsDirectionalConverter() EdgeInsetsDirectional? margin,
      @EdgeInsetsDirectionalConverter() EdgeInsetsDirectional? contentPadding});
}

/// @nodoc
class _$FluxCardConfigCopyWithImpl<$Res, $Val extends FluxCardConfig>
    implements $FluxCardConfigCopyWith<$Res> {
  _$FluxCardConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? color = freezed,
    Object? shadowColor = freezed,
    Object? surfaceTintColor = freezed,
    Object? elevation = freezed,
    Object? borderRadius = null,
    Object? borderSide = null,
    Object? clipBehavior = freezed,
    Object? margin = freezed,
    Object? contentPadding = freezed,
  }) {
    return _then(_value.copyWith(
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as Color?,
      shadowColor: freezed == shadowColor
          ? _value.shadowColor
          : shadowColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      surfaceTintColor: freezed == surfaceTintColor
          ? _value.surfaceTintColor
          : surfaceTintColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      elevation: freezed == elevation
          ? _value.elevation
          : elevation // ignore: cast_nullable_to_non_nullable
              as double?,
      borderRadius: null == borderRadius
          ? _value.borderRadius
          : borderRadius // ignore: cast_nullable_to_non_nullable
              as BorderRadiusDirectional,
      borderSide: null == borderSide
          ? _value.borderSide
          : borderSide // ignore: cast_nullable_to_non_nullable
              as BorderSide,
      clipBehavior: freezed == clipBehavior
          ? _value.clipBehavior
          : clipBehavior // ignore: cast_nullable_to_non_nullable
              as Clip?,
      margin: freezed == margin
          ? _value.margin
          : margin // ignore: cast_nullable_to_non_nullable
              as EdgeInsetsDirectional?,
      contentPadding: freezed == contentPadding
          ? _value.contentPadding
          : contentPadding // ignore: cast_nullable_to_non_nullable
              as EdgeInsetsDirectional?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FluxCardConfigImplCopyWith<$Res>
    implements $FluxCardConfigCopyWith<$Res> {
  factory _$$FluxCardConfigImplCopyWith(_$FluxCardConfigImpl value,
          $Res Function(_$FluxCardConfigImpl) then) =
      __$$FluxCardConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@ColorConverter() Color? color,
      @ColorConverter() Color? shadowColor,
      @ColorConverter() Color? surfaceTintColor,
      double? elevation,
      @BorderRadiusDirectionalConverter() BorderRadiusDirectional borderRadius,
      @BorderSideConverter() BorderSide borderSide,
      @ClipConverter() Clip? clipBehavior,
      @EdgeInsetsDirectionalConverter() EdgeInsetsDirectional? margin,
      @EdgeInsetsDirectionalConverter() EdgeInsetsDirectional? contentPadding});
}

/// @nodoc
class __$$FluxCardConfigImplCopyWithImpl<$Res>
    extends _$FluxCardConfigCopyWithImpl<$Res, _$FluxCardConfigImpl>
    implements _$$FluxCardConfigImplCopyWith<$Res> {
  __$$FluxCardConfigImplCopyWithImpl(
      _$FluxCardConfigImpl _value, $Res Function(_$FluxCardConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? color = freezed,
    Object? shadowColor = freezed,
    Object? surfaceTintColor = freezed,
    Object? elevation = freezed,
    Object? borderRadius = null,
    Object? borderSide = null,
    Object? clipBehavior = freezed,
    Object? margin = freezed,
    Object? contentPadding = freezed,
  }) {
    return _then(_$FluxCardConfigImpl(
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as Color?,
      shadowColor: freezed == shadowColor
          ? _value.shadowColor
          : shadowColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      surfaceTintColor: freezed == surfaceTintColor
          ? _value.surfaceTintColor
          : surfaceTintColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      elevation: freezed == elevation
          ? _value.elevation
          : elevation // ignore: cast_nullable_to_non_nullable
              as double?,
      borderRadius: null == borderRadius
          ? _value.borderRadius
          : borderRadius // ignore: cast_nullable_to_non_nullable
              as BorderRadiusDirectional,
      borderSide: null == borderSide
          ? _value.borderSide
          : borderSide // ignore: cast_nullable_to_non_nullable
              as BorderSide,
      clipBehavior: freezed == clipBehavior
          ? _value.clipBehavior
          : clipBehavior // ignore: cast_nullable_to_non_nullable
              as Clip?,
      margin: freezed == margin
          ? _value.margin
          : margin // ignore: cast_nullable_to_non_nullable
              as EdgeInsetsDirectional?,
      contentPadding: freezed == contentPadding
          ? _value.contentPadding
          : contentPadding // ignore: cast_nullable_to_non_nullable
              as EdgeInsetsDirectional?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FluxCardConfigImpl implements _FluxCardConfig {
  const _$FluxCardConfigImpl(
      {@ColorConverter() this.color,
      @ColorConverter() this.shadowColor,
      @ColorConverter() this.surfaceTintColor,
      this.elevation,
      @BorderRadiusDirectionalConverter()
      this.borderRadius = BorderRadiusDirectional.zero,
      @BorderSideConverter() this.borderSide = BorderSide.none,
      @ClipConverter() this.clipBehavior,
      @EdgeInsetsDirectionalConverter() this.margin,
      @EdgeInsetsDirectionalConverter() this.contentPadding});

  factory _$FluxCardConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$FluxCardConfigImplFromJson(json);

  @override
  @ColorConverter()
  final Color? color;
  @override
  @ColorConverter()
  final Color? shadowColor;
  @override
  @ColorConverter()
  final Color? surfaceTintColor;
  @override
  final double? elevation;
  @override
  @JsonKey()
  @BorderRadiusDirectionalConverter()
  final BorderRadiusDirectional borderRadius;
  @override
  @JsonKey()
  @BorderSideConverter()
  final BorderSide borderSide;
  @override
  @ClipConverter()
  final Clip? clipBehavior;
  @override
  @EdgeInsetsDirectionalConverter()
  final EdgeInsetsDirectional? margin;
  @override
  @EdgeInsetsDirectionalConverter()
  final EdgeInsetsDirectional? contentPadding;

  @override
  String toString() {
    return 'FluxCardConfig(color: $color, shadowColor: $shadowColor, surfaceTintColor: $surfaceTintColor, elevation: $elevation, borderRadius: $borderRadius, borderSide: $borderSide, clipBehavior: $clipBehavior, margin: $margin, contentPadding: $contentPadding)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FluxCardConfigImpl &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.shadowColor, shadowColor) ||
                other.shadowColor == shadowColor) &&
            (identical(other.surfaceTintColor, surfaceTintColor) ||
                other.surfaceTintColor == surfaceTintColor) &&
            (identical(other.elevation, elevation) ||
                other.elevation == elevation) &&
            (identical(other.borderRadius, borderRadius) ||
                other.borderRadius == borderRadius) &&
            (identical(other.borderSide, borderSide) ||
                other.borderSide == borderSide) &&
            (identical(other.clipBehavior, clipBehavior) ||
                other.clipBehavior == clipBehavior) &&
            (identical(other.margin, margin) || other.margin == margin) &&
            (identical(other.contentPadding, contentPadding) ||
                other.contentPadding == contentPadding));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      color,
      shadowColor,
      surfaceTintColor,
      elevation,
      borderRadius,
      borderSide,
      clipBehavior,
      margin,
      contentPadding);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FluxCardConfigImplCopyWith<_$FluxCardConfigImpl> get copyWith =>
      __$$FluxCardConfigImplCopyWithImpl<_$FluxCardConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FluxCardConfigImplToJson(
      this,
    );
  }
}

abstract class _FluxCardConfig implements FluxCardConfig {
  const factory _FluxCardConfig(
      {@ColorConverter() final Color? color,
      @ColorConverter() final Color? shadowColor,
      @ColorConverter() final Color? surfaceTintColor,
      final double? elevation,
      @BorderRadiusDirectionalConverter()
      final BorderRadiusDirectional borderRadius,
      @BorderSideConverter() final BorderSide borderSide,
      @ClipConverter() final Clip? clipBehavior,
      @EdgeInsetsDirectionalConverter() final EdgeInsetsDirectional? margin,
      @EdgeInsetsDirectionalConverter()
      final EdgeInsetsDirectional? contentPadding}) = _$FluxCardConfigImpl;

  factory _FluxCardConfig.fromJson(Map<String, dynamic> json) =
      _$FluxCardConfigImpl.fromJson;

  @override
  @ColorConverter()
  Color? get color;
  @override
  @ColorConverter()
  Color? get shadowColor;
  @override
  @ColorConverter()
  Color? get surfaceTintColor;
  @override
  double? get elevation;
  @override
  @BorderRadiusDirectionalConverter()
  BorderRadiusDirectional get borderRadius;
  @override
  @BorderSideConverter()
  BorderSide get borderSide;
  @override
  @ClipConverter()
  Clip? get clipBehavior;
  @override
  @EdgeInsetsDirectionalConverter()
  EdgeInsetsDirectional? get margin;
  @override
  @EdgeInsetsDirectionalConverter()
  EdgeInsetsDirectional? get contentPadding;
  @override
  @JsonKey(ignore: true)
  _$$FluxCardConfigImplCopyWith<_$FluxCardConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
