// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'flux_container_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FluxContainerConfig _$FluxContainerConfigFromJson(Map<String, dynamic> json) {
  return _FluxContainerConfig.fromJson(json);
}

/// @nodoc
mixin _$FluxContainerConfig {
  double? get width => throw _privateConstructorUsedError;
  double? get height => throw _privateConstructorUsedError;
  @EdgeInsetsDirectionalConverter()
  EdgeInsetsDirectional? get padding => throw _privateConstructorUsedError;
  @EdgeInsetsDirectionalConverter()
  EdgeInsetsDirectional? get margin => throw _privateConstructorUsedError;
  @DecorationConverter()
  BoxDecoration? get decoration => throw _privateConstructorUsedError;
  @AlignmentDirectionalConverter()
  AlignmentDirectional? get alignment => throw _privateConstructorUsedError;
  @ConstraintsConverter()
  BoxConstraints? get constraints => throw _privateConstructorUsedError;
  @ClipConverter()
  Clip get clipBehavior => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FluxContainerConfigCopyWith<FluxContainerConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FluxContainerConfigCopyWith<$Res> {
  factory $FluxContainerConfigCopyWith(
          FluxContainerConfig value, $Res Function(FluxContainerConfig) then) =
      _$FluxContainerConfigCopyWithImpl<$Res, FluxContainerConfig>;
  @useResult
  $Res call(
      {double? width,
      double? height,
      @EdgeInsetsDirectionalConverter() EdgeInsetsDirectional? padding,
      @EdgeInsetsDirectionalConverter() EdgeInsetsDirectional? margin,
      @DecorationConverter() BoxDecoration? decoration,
      @AlignmentDirectionalConverter() AlignmentDirectional? alignment,
      @ConstraintsConverter() BoxConstraints? constraints,
      @ClipConverter() Clip clipBehavior});
}

/// @nodoc
class _$FluxContainerConfigCopyWithImpl<$Res, $Val extends FluxContainerConfig>
    implements $FluxContainerConfigCopyWith<$Res> {
  _$FluxContainerConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? width = freezed,
    Object? height = freezed,
    Object? padding = freezed,
    Object? margin = freezed,
    Object? decoration = freezed,
    Object? alignment = freezed,
    Object? constraints = freezed,
    Object? clipBehavior = null,
  }) {
    return _then(_value.copyWith(
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      padding: freezed == padding
          ? _value.padding
          : padding // ignore: cast_nullable_to_non_nullable
              as EdgeInsetsDirectional?,
      margin: freezed == margin
          ? _value.margin
          : margin // ignore: cast_nullable_to_non_nullable
              as EdgeInsetsDirectional?,
      decoration: freezed == decoration
          ? _value.decoration
          : decoration // ignore: cast_nullable_to_non_nullable
              as BoxDecoration?,
      alignment: freezed == alignment
          ? _value.alignment
          : alignment // ignore: cast_nullable_to_non_nullable
              as AlignmentDirectional?,
      constraints: freezed == constraints
          ? _value.constraints
          : constraints // ignore: cast_nullable_to_non_nullable
              as BoxConstraints?,
      clipBehavior: null == clipBehavior
          ? _value.clipBehavior
          : clipBehavior // ignore: cast_nullable_to_non_nullable
              as Clip,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FluxContainerConfigImplCopyWith<$Res>
    implements $FluxContainerConfigCopyWith<$Res> {
  factory _$$FluxContainerConfigImplCopyWith(_$FluxContainerConfigImpl value,
          $Res Function(_$FluxContainerConfigImpl) then) =
      __$$FluxContainerConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? width,
      double? height,
      @EdgeInsetsDirectionalConverter() EdgeInsetsDirectional? padding,
      @EdgeInsetsDirectionalConverter() EdgeInsetsDirectional? margin,
      @DecorationConverter() BoxDecoration? decoration,
      @AlignmentDirectionalConverter() AlignmentDirectional? alignment,
      @ConstraintsConverter() BoxConstraints? constraints,
      @ClipConverter() Clip clipBehavior});
}

/// @nodoc
class __$$FluxContainerConfigImplCopyWithImpl<$Res>
    extends _$FluxContainerConfigCopyWithImpl<$Res, _$FluxContainerConfigImpl>
    implements _$$FluxContainerConfigImplCopyWith<$Res> {
  __$$FluxContainerConfigImplCopyWithImpl(_$FluxContainerConfigImpl _value,
      $Res Function(_$FluxContainerConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? width = freezed,
    Object? height = freezed,
    Object? padding = freezed,
    Object? margin = freezed,
    Object? decoration = freezed,
    Object? alignment = freezed,
    Object? constraints = freezed,
    Object? clipBehavior = null,
  }) {
    return _then(_$FluxContainerConfigImpl(
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      padding: freezed == padding
          ? _value.padding
          : padding // ignore: cast_nullable_to_non_nullable
              as EdgeInsetsDirectional?,
      margin: freezed == margin
          ? _value.margin
          : margin // ignore: cast_nullable_to_non_nullable
              as EdgeInsetsDirectional?,
      decoration: freezed == decoration
          ? _value.decoration
          : decoration // ignore: cast_nullable_to_non_nullable
              as BoxDecoration?,
      alignment: freezed == alignment
          ? _value.alignment
          : alignment // ignore: cast_nullable_to_non_nullable
              as AlignmentDirectional?,
      constraints: freezed == constraints
          ? _value.constraints
          : constraints // ignore: cast_nullable_to_non_nullable
              as BoxConstraints?,
      clipBehavior: null == clipBehavior
          ? _value.clipBehavior
          : clipBehavior // ignore: cast_nullable_to_non_nullable
              as Clip,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FluxContainerConfigImpl extends _FluxContainerConfig {
  const _$FluxContainerConfigImpl(
      {this.width,
      this.height,
      @EdgeInsetsDirectionalConverter() this.padding,
      @EdgeInsetsDirectionalConverter() this.margin,
      @DecorationConverter() this.decoration,
      @AlignmentDirectionalConverter() this.alignment,
      @ConstraintsConverter() this.constraints,
      @ClipConverter() this.clipBehavior = Clip.none})
      : super._();

  factory _$FluxContainerConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$FluxContainerConfigImplFromJson(json);

  @override
  final double? width;
  @override
  final double? height;
  @override
  @EdgeInsetsDirectionalConverter()
  final EdgeInsetsDirectional? padding;
  @override
  @EdgeInsetsDirectionalConverter()
  final EdgeInsetsDirectional? margin;
  @override
  @DecorationConverter()
  final BoxDecoration? decoration;
  @override
  @AlignmentDirectionalConverter()
  final AlignmentDirectional? alignment;
  @override
  @ConstraintsConverter()
  final BoxConstraints? constraints;
  @override
  @JsonKey()
  @ClipConverter()
  final Clip clipBehavior;

  @override
  String toString() {
    return 'FluxContainerConfig(width: $width, height: $height, padding: $padding, margin: $margin, decoration: $decoration, alignment: $alignment, constraints: $constraints, clipBehavior: $clipBehavior)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FluxContainerConfigImpl &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.padding, padding) || other.padding == padding) &&
            (identical(other.margin, margin) || other.margin == margin) &&
            (identical(other.decoration, decoration) ||
                other.decoration == decoration) &&
            (identical(other.alignment, alignment) ||
                other.alignment == alignment) &&
            (identical(other.constraints, constraints) ||
                other.constraints == constraints) &&
            (identical(other.clipBehavior, clipBehavior) ||
                other.clipBehavior == clipBehavior));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, width, height, padding, margin,
      decoration, alignment, constraints, clipBehavior);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FluxContainerConfigImplCopyWith<_$FluxContainerConfigImpl> get copyWith =>
      __$$FluxContainerConfigImplCopyWithImpl<_$FluxContainerConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FluxContainerConfigImplToJson(
      this,
    );
  }
}

abstract class _FluxContainerConfig extends FluxContainerConfig {
  const factory _FluxContainerConfig(
      {final double? width,
      final double? height,
      @EdgeInsetsDirectionalConverter() final EdgeInsetsDirectional? padding,
      @EdgeInsetsDirectionalConverter() final EdgeInsetsDirectional? margin,
      @DecorationConverter() final BoxDecoration? decoration,
      @AlignmentDirectionalConverter() final AlignmentDirectional? alignment,
      @ConstraintsConverter() final BoxConstraints? constraints,
      @ClipConverter() final Clip clipBehavior}) = _$FluxContainerConfigImpl;
  const _FluxContainerConfig._() : super._();

  factory _FluxContainerConfig.fromJson(Map<String, dynamic> json) =
      _$FluxContainerConfigImpl.fromJson;

  @override
  double? get width;
  @override
  double? get height;
  @override
  @EdgeInsetsDirectionalConverter()
  EdgeInsetsDirectional? get padding;
  @override
  @EdgeInsetsDirectionalConverter()
  EdgeInsetsDirectional? get margin;
  @override
  @DecorationConverter()
  BoxDecoration? get decoration;
  @override
  @AlignmentDirectionalConverter()
  AlignmentDirectional? get alignment;
  @override
  @ConstraintsConverter()
  BoxConstraints? get constraints;
  @override
  @ClipConverter()
  Clip get clipBehavior;
  @override
  @JsonKey(ignore: true)
  _$$FluxContainerConfigImplCopyWith<_$FluxContainerConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
