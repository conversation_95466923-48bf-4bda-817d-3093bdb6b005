import 'dart:convert';

import '../../data/remote/notion/notion_api.dart';
import '../../data/remote/woo/woo_api.dart';
import '../../extensions.dart';
import '../../helpers/notion_helper.dart';
import '../../models/notion_database_model.dart';
import '../../utils.dart';

part 'database/blog.dart';
part 'database/category.dart';
part 'database/customer.dart';
part 'database/order.dart';
part 'database/product.dart';

class ConverterNotion {
  final NotionDatabaseModel database;
  final NotionApi notionApi;
  final WooApi wooApi;

  const ConverterNotion({
    required this.notionApi,
    required this.database,
    required this.wooApi,
  });

  Future<void> convert() async {
    if (database.customer?.isNotEmpty ?? false) {
      await convertCustomer();
    }
    if (database.category?.isNotEmpty ?? false) {
      await convertCategory();
    }
    if (database.order?.isNotEmpty ?? false) {
      await convertOrder();
    }
    if (database.product?.isNotEmpty ?? false) {
      await convertProduct();
    }
    if (database.blog?.isNotEmpty ?? false) {
      await convertBlog();
    }
  }
}
