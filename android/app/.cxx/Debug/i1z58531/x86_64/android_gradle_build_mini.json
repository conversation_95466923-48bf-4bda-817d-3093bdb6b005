{"buildFiles": ["/Users/<USER>/fvm/versions/stable/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Data/InspireUI/medita-app/android/app/.cxx/Debug/i1z58531/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Data/InspireUI/medita-app/android/app/.cxx/Debug/i1z58531/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}