// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a pt_PT locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'pt_PT';

  static String m0(x) => "Ativo há ${x}";

  static String m1(amount) => "Adicionar ${amount} pontos";

  static String m2(attribute) => "Qualquer ${attribute}";

  static String m3(point) => "Os seus pontos disponíveis: ${point}";

  static String m4(name) => "Lance feito com sucesso para \'${name}\'";

  static String m5(state) => "O adaptador Bluetooth está ${state}";

  static String m6(amount) => "Compre agora por ${amount}";

  static String m7(author) => "Autor: ${author}";

  static String m8(fieldName) => "${fieldName} não pode estar vazio.";

  static String m9(fieldName) =>
      "${fieldName} não pode ter menos de 3 caracteres.";

  static String m10(currency) => "Moeda alterada para ${currency}";

  static String m11(number) => "${number} caracteres restantes";

  static String m12(priceRate, pointRate) =>
      "${priceRate} = ${pointRate} Pontos";

  static String m13(count) => "${count} item";

  static String m14(count) => "${count} itens";

  static String m15(count) => "${count} item";

  static String m16(count) => "${count} itens";

  static String m17(country) => "O país ${country} não é suportado";

  static String m18(currency) => "${currency} não é suportado";

  static String m19(day) => "Há ${day} dias";

  static String m20(total) => "~${total} km";

  static String m21(timeLeft) => "Termina em ${timeLeft}";

  static String m22(captcha) => "Introduza ${captcha} para confirmar:";

  static String m23(message) => "Erro: ${message}";

  static String m24(message) => "Erro: ${message}";

  static String m25(time) => "A expirar em ${time}";

  static String m26(total) => ">${total} km";

  static String m27(hour) => "Há ${hour} horas";

  static String m28(currentBalance) =>
      "Apenas tem ${currentBalance} na sua carteira";

  static String m29(message) =>
      "Existe um problema com a aplicação durante o pedido dos dados, por favor contacte o administrador para resolver os problemas: ${message}";

  static String m30(currency, amount) =>
      "O valor máximo para usar este pagamento é ${currency} ${amount}";

  static String m31(size) => "Tamanho máximo do ficheiro: ${size} MB";

  static String m32(name, formattedPrice) => "${name}: ${formattedPrice}";

  static String m33(currency, amount) =>
      "O valor mínimo para usar este pagamento é ${currency} ${amount}";

  static String m34(storeName, minOrderAmount) => "";

  static String m35(amount) => "";

  static String m36(value) => "";

  static String m37(minute) => "Há ${minute} minutos";

  static String m38(month) => "Há ${month} meses";

  static String m39(store) => "Mais de ${store}";

  static String m40(number) => "Deve ser comprado em grupos de ${number}";

  static String m41(itemCount) => "${itemCount} itens";

  static String m42(price) => "Opções totais: ${price}";

  static String m43(amount) => "Pagar ${amount}";

  static String m44(name) => "${name} foi adicionado ao carrinho com sucesso";

  static String m45(total) => "Qtd: ${total}";

  static String m46(name) => "Recebeu dinheiro de ${name}";

  static String m47(count) =>
      "Pretende remover ${count} item(ns) da sua lista de desejos?";

  static String m48(percent) => "Desconto ${percent}%";

  static String m49(keyword) => "Resultados da pesquisa para: \'${keyword}\'";

  static String m50(keyword, count) => "${keyword} (${count} item)";

  static String m51(keyword, count) => "${keyword} (${count} itens)";

  static String m52(second) => "Há ${second} segundos";

  static String m53(totalCartQuantity) =>
      "Carrinho de compras, ${totalCartQuantity} itens";

  static String m54(numberOfUnitsSold) => "Vendido: ${numberOfUnitsSold}";

  static String m55(fieldName) => "O campo ${fieldName} é obrigatório";

  static String m56(total) => "${total} produtos";

  static String m57(name) => "Transferir dinheiro para ${name}";

  static String m58(amount) => "Use ${amount} pontos";

  static String m59(maxPointDiscount, maxPriceDiscount) =>
      "Use no máximo ${maxPointDiscount} pontos para um desconto de ${maxPriceDiscount} nesta encomenda!";

  static String m60(time) => "";

  static String m61(date) => "Válido até ${date}";

  static String m62(number) => "Versão ${number}";

  static String m63(balance) => "Saldo da carteira: ${balance}";

  static String m64(message) => "Aviso: ${message}";

  static String m65(defaultCurrency) =>
      "A moeda selecionada não está disponível para a funcionalidade Carteira, altere-a para ${defaultCurrency}";

  static String m66(length) => "Encontrámos ${length} produtos";

  static String m67(week) => "Semana ${week}";

  static String m68(name) => "Bem-vindo ${name}";

  static String m69(year) => "Há ${year} anos";

  static String m70(count) => "Está a selecionar ${count} item(ns)";

  static String m71(total) => "Atribuiu à encomenda nº${total}";

  static String m72(type) => "";

  static String m73(point) => "Tem ${point} pontos";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "aboutUs": MessageLookupByLibrary.simpleMessage("Sobre nós"),
    "account": MessageLookupByLibrary.simpleMessage("Conta"),
    "accountApprovalTitle": MessageLookupByLibrary.simpleMessage(
      "Em aprovação",
    ),
    "accountDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "Excluir a sua conta remove todas as suas informações pessoais da nossa base de dados.",
    ),
    "accountIsPendingApproval": MessageLookupByLibrary.simpleMessage(
      "A conta está pendente de aprovação.",
    ),
    "accountNumber": MessageLookupByLibrary.simpleMessage("Número da conta"),
    "accountSetup": MessageLookupByLibrary.simpleMessage(
      "Configuração da conta",
    ),
    "active": MessageLookupByLibrary.simpleMessage("Ativo"),
    "activeFor": m0,
    "activeLongAgo": MessageLookupByLibrary.simpleMessage(
      "Ativo há muito tempo",
    ),
    "activeNow": MessageLookupByLibrary.simpleMessage("Ativo agora"),
    "add": MessageLookupByLibrary.simpleMessage("Adicionar"),
    "addAName": MessageLookupByLibrary.simpleMessage("Adicione um nome"),
    "addANewPost": MessageLookupByLibrary.simpleMessage(
      "Adicionar uma nova publicação",
    ),
    "addASlug": MessageLookupByLibrary.simpleMessage("Adicionar URL amigável"),
    "addAmountPoints": m1,
    "addAnAttr": MessageLookupByLibrary.simpleMessage("Adicionar um atributo"),
    "addListing": MessageLookupByLibrary.simpleMessage("Adicionar listagem"),
    "addMessage": MessageLookupByLibrary.simpleMessage("Adicionar mensagem"),
    "addNew": MessageLookupByLibrary.simpleMessage("Adicionar novo"),
    "addNewAddress": MessageLookupByLibrary.simpleMessage(
      "Adicionar novo endereço",
    ),
    "addNewBlog": MessageLookupByLibrary.simpleMessage("Adicionar novo blog"),
    "addNewPost": MessageLookupByLibrary.simpleMessage("Criar nova publicação"),
    "addOrUsePointsSuccessMsg": MessageLookupByLibrary.simpleMessage(
      "Parabéns! Os pontos foram adicionados ou resgatados com sucesso.",
    ),
    "addPoint": MessageLookupByLibrary.simpleMessage("Adicionar ponto"),
    "addPoints": MessageLookupByLibrary.simpleMessage("Adicionar pontos"),
    "addProduct": MessageLookupByLibrary.simpleMessage("Adicionar produto"),
    "addToCart": MessageLookupByLibrary.simpleMessage("Adicionar ao carrinho"),
    "addToCartMaximum": MessageLookupByLibrary.simpleMessage(
      "A quantidade máxima foi excedida",
    ),
    "addToCartSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Adicionado ao carrinho com sucesso",
    ),
    "addToOrder": MessageLookupByLibrary.simpleMessage("Adicionar ao pedido"),
    "addToQuoteRequest": MessageLookupByLibrary.simpleMessage(
      "Adicionar ao pedido de orçamento",
    ),
    "addToWishlist": MessageLookupByLibrary.simpleMessage(
      "Adicionar à lista de desejos",
    ),
    "added": MessageLookupByLibrary.simpleMessage("Adicionado"),
    "addedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Adicionado com sucesso",
    ),
    "addingYourImage": MessageLookupByLibrary.simpleMessage(
      "A adicionar a sua imagem",
    ),
    "additionalInformation": MessageLookupByLibrary.simpleMessage(
      "Informação adicional",
    ),
    "additionalServices": MessageLookupByLibrary.simpleMessage(
      "Serviços adicionais",
    ),
    "address": MessageLookupByLibrary.simpleMessage("Morada"),
    "adults": MessageLookupByLibrary.simpleMessage("Adultos"),
    "advanceAmount": MessageLookupByLibrary.simpleMessage("Valor adiantado"),
    "advancePayment": MessageLookupByLibrary.simpleMessage(
      "Pagamento adiantado",
    ),
    "afternoon": MessageLookupByLibrary.simpleMessage("Tarde"),
    "agree": MessageLookupByLibrary.simpleMessage("Concordo"),
    "agreeWithPrivacy": MessageLookupByLibrary.simpleMessage(
      "Privacidade e termos",
    ),
    "albanian": MessageLookupByLibrary.simpleMessage("Albanês"),
    "all": MessageLookupByLibrary.simpleMessage("Todos"),
    "allBrands": MessageLookupByLibrary.simpleMessage("Todas as marcas"),
    "allDeliveryOrders": MessageLookupByLibrary.simpleMessage(
      "Todos os pedidos",
    ),
    "allOrders": MessageLookupByLibrary.simpleMessage("Últimas vendas"),
    "allProducts": MessageLookupByLibrary.simpleMessage("Todos os produtos"),
    "allow": MessageLookupByLibrary.simpleMessage("Permitir"),
    "allowCameraAccess": MessageLookupByLibrary.simpleMessage(
      "Permitir acesso à câmara?",
    ),
    "almostSoldOut": MessageLookupByLibrary.simpleMessage("Quase esgotado"),
    "amazing": MessageLookupByLibrary.simpleMessage("Incrível"),
    "amount": MessageLookupByLibrary.simpleMessage("Montante"),
    "anyAttr": m2,
    "appTrackingRequest": MessageLookupByLibrary.simpleMessage(
      "Este identificador será usado para entregar anúncios personalizados para você. \n\"Cancelar\" limitará a capacidade da rede de anúncios de entregar anúncios relevantes para você, mas não reduzirá o número de anúncios que você recebe.\nComo o dispositivo é restrito, o rastreamento é desabilitado e o sistema não pode mostrar uma caixa de diálogo de solicitação. \"Abrir configurações\" e permitir que o aplicativo rastreie sua atividade em aplicativos e sites de outras empresas?",
    ),
    "appTrackingTransparency": MessageLookupByLibrary.simpleMessage(
      "Transparência de rastreamento de aplicativos",
    ),
    "appearance": MessageLookupByLibrary.simpleMessage("Aparência"),
    "apply": MessageLookupByLibrary.simpleMessage("Aplicar"),
    "appointmentStartInvalidDay": MessageLookupByLibrary.simpleMessage(
      "Desculpe, os compromissos não podem começar neste dia.",
    ),
    "approve": MessageLookupByLibrary.simpleMessage("Aprovar"),
    "approved": MessageLookupByLibrary.simpleMessage("Aprovado"),
    "approvedRequests": MessageLookupByLibrary.simpleMessage(
      "Pedidos aprovados",
    ),
    "arabic": MessageLookupByLibrary.simpleMessage("Árabe"),
    "areYouSure": MessageLookupByLibrary.simpleMessage("Tem a certeza?"),
    "areYouSureDeleteAccount": MessageLookupByLibrary.simpleMessage(
      "Tem a certeza que deseja eliminar a sua conta?",
    ),
    "areYouSureLogOut": MessageLookupByLibrary.simpleMessage(
      "Tem a certeza que pretende sair?",
    ),
    "areYouWantToExit": MessageLookupByLibrary.simpleMessage(
      "Tem a certeza que deseja sair?",
    ),
    "assigned": MessageLookupByLibrary.simpleMessage("Atribuído"),
    "atLeastThreeCharacters": MessageLookupByLibrary.simpleMessage(
      "Pelo menos 3 caracteres...",
    ),
    "attribute": MessageLookupByLibrary.simpleMessage("Atributo"),
    "attributeAlreadyExists": MessageLookupByLibrary.simpleMessage(
      "O atributo já existe",
    ),
    "attributes": MessageLookupByLibrary.simpleMessage("Atributos"),
    "auction": MessageLookupByLibrary.simpleMessage("Leilão"),
    "auctionDates": MessageLookupByLibrary.simpleMessage("Datas do Leilão"),
    "auctionEnded": MessageLookupByLibrary.simpleMessage("Leilão encerrado"),
    "auctionEnds": MessageLookupByLibrary.simpleMessage("Fim do leilão"),
    "auctionHistory": MessageLookupByLibrary.simpleMessage(
      "Histórico de leilões",
    ),
    "auctionStarts": MessageLookupByLibrary.simpleMessage("Início do leilão"),
    "auctionStartsIn": MessageLookupByLibrary.simpleMessage(
      "O leilão começa em",
    ),
    "auctionType": MessageLookupByLibrary.simpleMessage("Tipo de leilão"),
    "audioDetected": MessageLookupByLibrary.simpleMessage(
      "Item(ns) de áudio detetado(s). Deseja adicionar ao reprodutor de áudio?",
    ),
    "availability": MessageLookupByLibrary.simpleMessage("Disponibilidade"),
    "availabilityProduct": MessageLookupByLibrary.simpleMessage(
      "Disponibilidade: ",
    ),
    "availableForTiers": MessageLookupByLibrary.simpleMessage(
      "Disponível para níveis",
    ),
    "availablePoints": m3,
    "averageRating": MessageLookupByLibrary.simpleMessage(
      "Classificação média",
    ),
    "b2bKingRegisterMsg": MessageLookupByLibrary.simpleMessage(
      "Entre em contato com o administrador para aprovar seu registro.",
    ),
    "back": MessageLookupByLibrary.simpleMessage("Voltar"),
    "backOrder": MessageLookupByLibrary.simpleMessage("Em espera"),
    "backToShop": MessageLookupByLibrary.simpleMessage("Voltar à loja"),
    "backToWallet": MessageLookupByLibrary.simpleMessage("Voltar à carteira"),
    "bagsCollections": MessageLookupByLibrary.simpleMessage("Coleções"),
    "balance": MessageLookupByLibrary.simpleMessage("Saldo"),
    "bank": MessageLookupByLibrary.simpleMessage("Banco"),
    "bannerListType": MessageLookupByLibrary.simpleMessage(
      "Tipo de lista de banner",
    ),
    "bannerType": MessageLookupByLibrary.simpleMessage("Tipo de banner"),
    "bannerYoutubeURL": MessageLookupByLibrary.simpleMessage(
      "URL do banner do Youtube",
    ),
    "basicInformation": MessageLookupByLibrary.simpleMessage(
      "Informação básica",
    ),
    "becomeADelivery": MessageLookupByLibrary.simpleMessage(
      "Torne-se uma entrega",
    ),
    "becomeAVendor": MessageLookupByLibrary.simpleMessage(
      "Torne-se um vendedor",
    ),
    "becomeAVendorDelivery": MessageLookupByLibrary.simpleMessage(
      "Torne-se um fornecedor/entrega",
    ),
    "benefits": MessageLookupByLibrary.simpleMessage("Benefícios"),
    "bengali": MessageLookupByLibrary.simpleMessage("Bengali"),
    "bid": MessageLookupByLibrary.simpleMessage("Licitação"),
    "bidIncrement": MessageLookupByLibrary.simpleMessage("Incremento de lance"),
    "bidSuccessMessage": m4,
    "billingAddress": MessageLookupByLibrary.simpleMessage(
      "Morada de faturação",
    ),
    "bleHasNotBeenEnabled": MessageLookupByLibrary.simpleMessage(
      "O Bluetooth não foi ativado",
    ),
    "bleState": m5,
    "block": MessageLookupByLibrary.simpleMessage("Bloquear"),
    "blockUser": MessageLookupByLibrary.simpleMessage("Bloquear utilizador"),
    "blog": MessageLookupByLibrary.simpleMessage("Blog"),
    "booked": MessageLookupByLibrary.simpleMessage("Já reservado"),
    "booking": MessageLookupByLibrary.simpleMessage("Reserva"),
    "bookingCancelled": MessageLookupByLibrary.simpleMessage(
      "Reserva cancelada",
    ),
    "bookingConfirm": MessageLookupByLibrary.simpleMessage("Confirmado"),
    "bookingError": MessageLookupByLibrary.simpleMessage(
      "Algo está errado. Por favor, tente novamente mais tarde.",
    ),
    "bookingHistory": MessageLookupByLibrary.simpleMessage(
      "Histórico de reservas",
    ),
    "bookingNow": MessageLookupByLibrary.simpleMessage("Reservar agora"),
    "bookingSuccess": MessageLookupByLibrary.simpleMessage(
      "Reservado com sucesso",
    ),
    "bookingSummary": MessageLookupByLibrary.simpleMessage("Resumo da reserva"),
    "bookingUnavailable": MessageLookupByLibrary.simpleMessage(
      "A reserva não está disponível",
    ),
    "bosnian": MessageLookupByLibrary.simpleMessage("Bósnio"),
    "branch": MessageLookupByLibrary.simpleMessage("Filial"),
    "branchChangeWarning": MessageLookupByLibrary.simpleMessage(
      "Lamentamos, mas o seu carrinho será esvaziado devido à mudança de região. Teremos todo o gosto em ajudá-lo(a) caso necessite de assistência.",
    ),
    "brand": MessageLookupByLibrary.simpleMessage("Marca"),
    "brands": MessageLookupByLibrary.simpleMessage("Marcas"),
    "brazil": MessageLookupByLibrary.simpleMessage("Português"),
    "bronze": MessageLookupByLibrary.simpleMessage("Bronze"),
    "bronzePriority": MessageLookupByLibrary.simpleMessage("Prioridade Bronze"),
    "burmese": MessageLookupByLibrary.simpleMessage("Birmanês"),
    "buyItNowPrice": MessageLookupByLibrary.simpleMessage(
      "Comprar agora preço",
    ),
    "buyNow": MessageLookupByLibrary.simpleMessage("Comprar agora"),
    "buyNowFor": m6,
    "by": MessageLookupByLibrary.simpleMessage("Por"),
    "byAppointmentOnly": MessageLookupByLibrary.simpleMessage(
      "Apenas por marcação",
    ),
    "byAuthor": m7,
    "byBrand": MessageLookupByLibrary.simpleMessage("Por marca"),
    "byCategory": MessageLookupByLibrary.simpleMessage("Por categoria"),
    "byPrice": MessageLookupByLibrary.simpleMessage("Por preço"),
    "bySignup": MessageLookupByLibrary.simpleMessage(
      "Ao registar-se, concorda com os nossos",
    ),
    "byTag": MessageLookupByLibrary.simpleMessage("Por etiqueta"),
    "call": MessageLookupByLibrary.simpleMessage("Ligar"),
    "callTo": MessageLookupByLibrary.simpleMessage("Fazer uma chamada para"),
    "callToVendor": MessageLookupByLibrary.simpleMessage(
      "Ligar para o dono da loja",
    ),
    "canNotCreateOrder": MessageLookupByLibrary.simpleMessage(
      "Não é possível criar o pedido",
    ),
    "canNotCreateUser": MessageLookupByLibrary.simpleMessage(
      "Não é possível criar o utilizador.",
    ),
    "canNotGetPayments": MessageLookupByLibrary.simpleMessage(
      "Não é possível obter os métodos de pagamento",
    ),
    "canNotGetShipping": MessageLookupByLibrary.simpleMessage(
      "Não é possível obter os métodos de envio",
    ),
    "canNotGetToken": MessageLookupByLibrary.simpleMessage(
      "Não é possível obter as informações do token.",
    ),
    "canNotLaunch": MessageLookupByLibrary.simpleMessage(
      "Não é possível iniciar esta aplicação. Verifique se as suas configurações em config.dart estão corretas",
    ),
    "canNotLoadThisLink": MessageLookupByLibrary.simpleMessage(
      "Não é possível carregar esta ligação",
    ),
    "canNotPlayVideo": MessageLookupByLibrary.simpleMessage(
      "Desculpe, este vídeo não pode ser reproduzido",
    ),
    "canNotSaveOrder": MessageLookupByLibrary.simpleMessage(
      "Não é possível guardar o pedido no site",
    ),
    "canNotUpdateInfo": MessageLookupByLibrary.simpleMessage(
      "Não é possível atualizar as informações do utilizador.",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancelar"),
    "cancelOrder": MessageLookupByLibrary.simpleMessage("Cancelar"),
    "cancelled": MessageLookupByLibrary.simpleMessage("Cancelado"),
    "cancelledRequests": MessageLookupByLibrary.simpleMessage(
      "Pedidos cancelados",
    ),
    "cannotBeEmpty": m8,
    "cannotDeleteAccount": MessageLookupByLibrary.simpleMessage(
      "Esta conta não pode ser eliminada",
    ),
    "cannotLessThreeLength": m9,
    "cannotSendMessage": MessageLookupByLibrary.simpleMessage(
      "Não pode enviar mensagens para este utilizador",
    ),
    "cantFindThisOrderId": MessageLookupByLibrary.simpleMessage(
      "Não é possível encontrar este ID de pedido",
    ),
    "cantPickDateInThePast": MessageLookupByLibrary.simpleMessage(
      "Data no passado não é permitida",
    ),
    "card": MessageLookupByLibrary.simpleMessage("Cartão"),
    "cardHolder": MessageLookupByLibrary.simpleMessage("Titular do cartão"),
    "cardNumber": MessageLookupByLibrary.simpleMessage("Número do cartão"),
    "cart": MessageLookupByLibrary.simpleMessage("Carrinho"),
    "cartDiscount": MessageLookupByLibrary.simpleMessage(
      "Desconto no carrinho",
    ),
    "cartNotAvailable": MessageLookupByLibrary.simpleMessage(
      "O carrinho não está disponível. Por favor, adicione alguns artigos ao seu carrinho.",
    ),
    "cartNotReadyForCheckout": MessageLookupByLibrary.simpleMessage(
      "O seu carrinho ainda está a ser processado. Por favor, aguarde um momento.",
    ),
    "cash": MessageLookupByLibrary.simpleMessage("Dinheiro"),
    "categories": MessageLookupByLibrary.simpleMessage("Categorias"),
    "category": MessageLookupByLibrary.simpleMessage("Categoria"),
    "change": MessageLookupByLibrary.simpleMessage("Alterar"),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("Mudar idioma"),
    "changePrinter": MessageLookupByLibrary.simpleMessage("Alterar impressora"),
    "changedCurrencyTo": m10,
    "characterRemain": m11,
    "chat": MessageLookupByLibrary.simpleMessage("Conversa"),
    "chatGPT": MessageLookupByLibrary.simpleMessage("Chat GPT"),
    "chatListScreen": MessageLookupByLibrary.simpleMessage("Mensagens"),
    "chatViaFacebook": MessageLookupByLibrary.simpleMessage(
      "Conversar via Facebook Messenger",
    ),
    "chatViaWhatApp": MessageLookupByLibrary.simpleMessage(
      "Conversar via WhatsApp",
    ),
    "chatWithBot": MessageLookupByLibrary.simpleMessage("Conversar com bot"),
    "chatWithStoreOwner": MessageLookupByLibrary.simpleMessage(
      "Conversar com o proprietário da loja",
    ),
    "checkConfirmLink": MessageLookupByLibrary.simpleMessage(
      "Verifique o seu e-mail para o link de confirmação",
    ),
    "checking": MessageLookupByLibrary.simpleMessage("A verificar..."),
    "checkout": MessageLookupByLibrary.simpleMessage("Finalizar compra"),
    "chinese": MessageLookupByLibrary.simpleMessage("Chinês"),
    "chineseSimplified": MessageLookupByLibrary.simpleMessage(
      "Chinês (simplificado)",
    ),
    "chineseTraditional": MessageLookupByLibrary.simpleMessage(
      "Chinês (tradicional)",
    ),
    "chooseBranch": MessageLookupByLibrary.simpleMessage("Escolher filial"),
    "chooseCategory": MessageLookupByLibrary.simpleMessage(
      "Escolher categoria",
    ),
    "chooseFromGallery": MessageLookupByLibrary.simpleMessage(
      "Escolher da galeria",
    ),
    "chooseFromServer": MessageLookupByLibrary.simpleMessage(
      "Escolher do servidor",
    ),
    "choosePlan": MessageLookupByLibrary.simpleMessage("Escolher plano"),
    "chooseStaff": MessageLookupByLibrary.simpleMessage("Escolher equipa"),
    "chooseType": MessageLookupByLibrary.simpleMessage("Escolher tipo"),
    "chooseYourPaymentMethod": MessageLookupByLibrary.simpleMessage(
      "Escolha o seu método de pagamento",
    ),
    "city": MessageLookupByLibrary.simpleMessage("Cidade"),
    "cityIsRequired": MessageLookupByLibrary.simpleMessage(
      "O campo cidade é obrigatório",
    ),
    "claim": MessageLookupByLibrary.simpleMessage("RECLAMAÇÃO"),
    "claimed": MessageLookupByLibrary.simpleMessage("Reivindicado"),
    "clear": MessageLookupByLibrary.simpleMessage("Limpar"),
    "clearCart": MessageLookupByLibrary.simpleMessage("Limpar carrinho"),
    "clearCartAndAddNew": MessageLookupByLibrary.simpleMessage(
      "Limpar carrinho e adicionar novo",
    ),
    "clearConversation": MessageLookupByLibrary.simpleMessage(
      "Limpar conversa",
    ),
    "close": MessageLookupByLibrary.simpleMessage("Fechar"),
    "closeNow": MessageLookupByLibrary.simpleMessage("Fechado agora"),
    "closed": MessageLookupByLibrary.simpleMessage("Fechado"),
    "codExtraFee": MessageLookupByLibrary.simpleMessage("Taxa extra COD"),
    "color": MessageLookupByLibrary.simpleMessage("Cor"),
    "columns": MessageLookupByLibrary.simpleMessage("colunas"),
    "comment": MessageLookupByLibrary.simpleMessage("Comentário"),
    "commentFirst": MessageLookupByLibrary.simpleMessage(
      "Por favor, escreva o seu comentário",
    ),
    "commentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Comentário enviado com sucesso, aguarde até que o seu comentário seja aprovado",
    ),
    "complete": MessageLookupByLibrary.simpleMessage("Concluído"),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirmar"),
    "confirmAccountDeletion": MessageLookupByLibrary.simpleMessage(
      "Confirmar eliminação da conta",
    ),
    "confirmClearCartWhenTopUp": MessageLookupByLibrary.simpleMessage(
      "O carrinho será limpo ao recarregar.",
    ),
    "confirmClearTheCart": MessageLookupByLibrary.simpleMessage(
      "Tem a certeza que deseja limpar o carrinho?",
    ),
    "confirmDelete": MessageLookupByLibrary.simpleMessage(
      "Tem a certeza que deseja eliminar isto? Esta ação não pode ser desfeita.",
    ),
    "confirmDeleteItem": MessageLookupByLibrary.simpleMessage(
      "Tem a certeza que deseja eliminar este item?",
    ),
    "confirmPassword": MessageLookupByLibrary.simpleMessage(
      "Confirmar palavra-passe",
    ),
    "confirmPasswordIsRequired": MessageLookupByLibrary.simpleMessage(
      "O campo de confirmação da palavra-passe é obrigatório",
    ),
    "confirmRemoveProductInCart": MessageLookupByLibrary.simpleMessage(
      "Tem a certeza que deseja remover este produto?",
    ),
    "connect": MessageLookupByLibrary.simpleMessage("Conectar"),
    "contact": MessageLookupByLibrary.simpleMessage("Contacto"),
    "content": MessageLookupByLibrary.simpleMessage("Conteúdo"),
    "continueShopping": MessageLookupByLibrary.simpleMessage(
      "Continuar a comprar",
    ),
    "continueToPayment": MessageLookupByLibrary.simpleMessage(
      "Continuar para pagamento",
    ),
    "continueToReview": MessageLookupByLibrary.simpleMessage(
      "Continuar para revisão",
    ),
    "continueToSelectItem": MessageLookupByLibrary.simpleMessage(
      "Continuar a selecionar o item",
    ),
    "continueToShipping": MessageLookupByLibrary.simpleMessage(
      "Continuar para envio",
    ),
    "continueWithShopify": MessageLookupByLibrary.simpleMessage(
      "Continuar com o Shopify",
    ),
    "continues": MessageLookupByLibrary.simpleMessage("Continuar"),
    "conversations": MessageLookupByLibrary.simpleMessage("Conversas"),
    "convertPoint": m12,
    "copied": MessageLookupByLibrary.simpleMessage("Copiado"),
    "copy": MessageLookupByLibrary.simpleMessage("Copiar"),
    "copyright": MessageLookupByLibrary.simpleMessage(
      "© 2024 InspireUI Todos os direitos reservados",
    ),
    "countItem": m13,
    "countItems": m14,
    "countProduct": m15,
    "countProducts": m16,
    "country": MessageLookupByLibrary.simpleMessage("País"),
    "countryCodeIsRequired": MessageLookupByLibrary.simpleMessage(
      "O código do país é obrigatório",
    ),
    "countryIsNotSupported": m17,
    "countryIsRequired": MessageLookupByLibrary.simpleMessage(
      "O campo país é obrigatório",
    ),
    "couponCode": MessageLookupByLibrary.simpleMessage("Código do cupão"),
    "couponHasBeenSavedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "O cupão foi guardado com sucesso.",
    ),
    "couponInvalid": MessageLookupByLibrary.simpleMessage(
      "O seu código de cupão é inválido",
    ),
    "couponMsgSuccess": MessageLookupByLibrary.simpleMessage(
      "Parabéns! O código do cupão foi aplicado com sucesso",
    ),
    "couponsDedicatedForYou": MessageLookupByLibrary.simpleMessage(
      "Cupões dedicados para si",
    ),
    "couponsManagement": MessageLookupByLibrary.simpleMessage(
      "Gestão de cupões",
    ),
    "create": MessageLookupByLibrary.simpleMessage("Criar"),
    "createAnAccount": MessageLookupByLibrary.simpleMessage("Criar uma conta"),
    "createNewPostSuccessfully": MessageLookupByLibrary.simpleMessage(
      "A sua publicação foi criada com sucesso como rascunho. Por favor, verifique o seu site de administração.",
    ),
    "createPost": MessageLookupByLibrary.simpleMessage("Criar publicação"),
    "createProduct": MessageLookupByLibrary.simpleMessage("Criar produto"),
    "createReviewSuccess": MessageLookupByLibrary.simpleMessage(
      "Obrigado pela sua avaliação",
    ),
    "createReviewSuccessMsg": MessageLookupByLibrary.simpleMessage(
      "Agradecemos muito a sua contribuição e valorizamos o seu feedback para nos ajudar a melhorar",
    ),
    "createVariants": MessageLookupByLibrary.simpleMessage(
      "Criar todas as variantes",
    ),
    "createdOn": MessageLookupByLibrary.simpleMessage("Criado em:"),
    "currencies": MessageLookupByLibrary.simpleMessage("Moedas"),
    "currencyIsNotSupported": m18,
    "currentBid": MessageLookupByLibrary.simpleMessage("Lance atual"),
    "currentPassword": MessageLookupByLibrary.simpleMessage(
      "Palavra-passe atual",
    ),
    "currentlyWeOnlyHave": MessageLookupByLibrary.simpleMessage(
      "Atualmente só temos",
    ),
    "customer": MessageLookupByLibrary.simpleMessage("Cliente"),
    "customerDetail": MessageLookupByLibrary.simpleMessage(
      "Detalhe do cliente",
    ),
    "customerNote": MessageLookupByLibrary.simpleMessage("Nota do cliente"),
    "cvv": MessageLookupByLibrary.simpleMessage("CVV"),
    "czech": MessageLookupByLibrary.simpleMessage("Checo"),
    "danish": MessageLookupByLibrary.simpleMessage("Dinamarquês"),
    "darkTheme": MessageLookupByLibrary.simpleMessage("Tema escuro"),
    "dashboard": MessageLookupByLibrary.simpleMessage("Painel de controlo"),
    "dataEmpty": MessageLookupByLibrary.simpleMessage("Sem dados"),
    "date": MessageLookupByLibrary.simpleMessage("Data"),
    "dateASC": MessageLookupByLibrary.simpleMessage("Data crescente"),
    "dateBooking": MessageLookupByLibrary.simpleMessage("Data da reserva"),
    "dateDESC": MessageLookupByLibrary.simpleMessage("Data decrescente"),
    "dateEnd": MessageLookupByLibrary.simpleMessage("Data de fim"),
    "dateLatest": MessageLookupByLibrary.simpleMessage("Data: Mais recente"),
    "dateOldest": MessageLookupByLibrary.simpleMessage("Data: Mais antiga"),
    "dateStart": MessageLookupByLibrary.simpleMessage("Data de início"),
    "dateTime": MessageLookupByLibrary.simpleMessage("Data e hora"),
    "dateWiseClose": MessageLookupByLibrary.simpleMessage("Fechar por data"),
    "daysAgo": m19,
    "debit": MessageLookupByLibrary.simpleMessage("Débito"),
    "decline": MessageLookupByLibrary.simpleMessage("Recusar"),
    "delete": MessageLookupByLibrary.simpleMessage("Eliminar"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("Eliminar conta"),
    "deleteAccountMsg": MessageLookupByLibrary.simpleMessage(
      "Tem a certeza que deseja eliminar a sua conta? Por favor, leia como a eliminação da conta irá afetar.",
    ),
    "deleteAccountSuccess": MessageLookupByLibrary.simpleMessage(
      "Conta eliminada com sucesso. A sua sessão expirou.",
    ),
    "deleteAll": MessageLookupByLibrary.simpleMessage("Eliminar tudo"),
    "deleteConversation": MessageLookupByLibrary.simpleMessage(
      "Eliminar conversa",
    ),
    "delivered": MessageLookupByLibrary.simpleMessage("Entregue"),
    "deliveredTo": MessageLookupByLibrary.simpleMessage("Entregue a"),
    "delivering": MessageLookupByLibrary.simpleMessage("A entregar"),
    "deliveryBoy": MessageLookupByLibrary.simpleMessage("Estafeta:"),
    "deliveryDate": MessageLookupByLibrary.simpleMessage("Data de entrega"),
    "deliveryDetails": MessageLookupByLibrary.simpleMessage(
      "Detalhes da entrega",
    ),
    "deliveryManagement": MessageLookupByLibrary.simpleMessage("Entrega"),
    "deliveryNotificationError": MessageLookupByLibrary.simpleMessage(
      "Sem dados.\nEste pedido foi removido.",
    ),
    "description": MessageLookupByLibrary.simpleMessage("Descrição"),
    "descriptionEnterVoucher": MessageLookupByLibrary.simpleMessage(
      "Por favor, introduza ou selecione o vale para a sua encomenda",
    ),
    "didntReceiveCode": MessageLookupByLibrary.simpleMessage(
      "Não recebeu o código?",
    ),
    "direction": MessageLookupByLibrary.simpleMessage("Direção"),
    "disablePurchase": MessageLookupByLibrary.simpleMessage("Desativar compra"),
    "discount": MessageLookupByLibrary.simpleMessage("Desconto"),
    "displayName": MessageLookupByLibrary.simpleMessage("Nome de exibição"),
    "distance": m20,
    "doNotAnyTransactions": MessageLookupByLibrary.simpleMessage(
      "Ainda não tem transações",
    ),
    "doYouWantToExitApp": MessageLookupByLibrary.simpleMessage(
      "Deseja sair da aplicação",
    ),
    "doYouWantToLeaveWithoutSubmit": MessageLookupByLibrary.simpleMessage(
      "Deseja sair sem submeter a sua avaliação?",
    ),
    "doYouWantToLogout": MessageLookupByLibrary.simpleMessage(
      "Deseja terminar sessão?",
    ),
    "doYouWantToUnblock": MessageLookupByLibrary.simpleMessage(
      "Pretende desbloquear este utilizador?",
    ),
    "doesNotSupportApplePay": MessageLookupByLibrary.simpleMessage(
      "ApplePay não é suportado. Por favor, verifique a sua carteira e cartão",
    ),
    "done": MessageLookupByLibrary.simpleMessage("Concluído"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage(
      "Não tem uma conta?",
    ),
    "download": MessageLookupByLibrary.simpleMessage("Transferir"),
    "downloadApp": MessageLookupByLibrary.simpleMessage("Transferir aplicação"),
    "downloadingImages": MessageLookupByLibrary.simpleMessage(
      "A transferir imagens...",
    ),
    "draft": MessageLookupByLibrary.simpleMessage("Rascunho"),
    "driverAssigned": MessageLookupByLibrary.simpleMessage(
      "Condutor atribuído",
    ),
    "duration": MessageLookupByLibrary.simpleMessage("Duração"),
    "dutch": MessageLookupByLibrary.simpleMessage("Holandês"),
    "earnings": MessageLookupByLibrary.simpleMessage("Ganhos"),
    "edit": MessageLookupByLibrary.simpleMessage("Editar:"),
    "editProductInfo": MessageLookupByLibrary.simpleMessage(
      "Editar informações do produto",
    ),
    "editWithoutColon": MessageLookupByLibrary.simpleMessage("Editar"),
    "egypt": MessageLookupByLibrary.simpleMessage("Egito"),
    "email": MessageLookupByLibrary.simpleMessage("E-mail"),
    "emailAddressInvalid": MessageLookupByLibrary.simpleMessage(
      "Endereço de email inválido",
    ),
    "emailAlreadyInUse": MessageLookupByLibrary.simpleMessage(
      "Email já em utilização!",
    ),
    "emailDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "A eliminação da sua conta irá cancelar a sua subscrição de todas as listas de e-mail.",
    ),
    "emailDoesNotExist": MessageLookupByLibrary.simpleMessage(
      "A conta de e-mail que introduziu não existe. Por favor, tente novamente.",
    ),
    "emailIsRequired": MessageLookupByLibrary.simpleMessage(
      "O campo de e-mail é obrigatório",
    ),
    "emailSubscription": MessageLookupByLibrary.simpleMessage(
      "Subscrição de e-mail",
    ),
    "emptyBookingHistoryMsg": MessageLookupByLibrary.simpleMessage(
      "Parece que ainda não fez nenhuma reserva.\nComece a explorar e faça a sua primeira reserva!",
    ),
    "emptyCart": MessageLookupByLibrary.simpleMessage("Carrinho vazio"),
    "emptyCartSubtitle": MessageLookupByLibrary.simpleMessage(
      "Parece que ainda não adicionou nenhum item ao carrinho. Comece a fazer compras para o preencher.",
    ),
    "emptyCartSubtitle02": MessageLookupByLibrary.simpleMessage(
      "Ups! O seu carrinho está um pouco leve.\n\nPronto para comprar algo fantástico?",
    ),
    "emptyComment": MessageLookupByLibrary.simpleMessage(
      "O seu comentário não pode estar vazio",
    ),
    "emptySearch": MessageLookupByLibrary.simpleMessage(
      "Ainda não procurou por itens. Comece agora",
    ),
    "emptyShippingMsg": MessageLookupByLibrary.simpleMessage(
      "Não existem opções de envio disponíveis. Certifique-se de que o seu endereço foi introduzido corretamente ou contacte-nos se precisar de ajuda.",
    ),
    "emptyUsername": MessageLookupByLibrary.simpleMessage(
      "O nome de utilizador/e-mail está vazio",
    ),
    "emptyWishlist": MessageLookupByLibrary.simpleMessage(
      "Lista de desejos vazia",
    ),
    "emptyWishlistSubtitle": MessageLookupByLibrary.simpleMessage(
      "Toque em qualquer coração ao lado de um produto para o adicionar aos favoritos. Vamos guardá-los para si aqui!",
    ),
    "emptyWishlistSubtitle02": MessageLookupByLibrary.simpleMessage(
      "A sua lista de desejos está vazia de momento.\nComece a adicionar produtos agora!",
    ),
    "enableForCheckout": MessageLookupByLibrary.simpleMessage(
      "Ativar para finalização de compra",
    ),
    "enableForLogin": MessageLookupByLibrary.simpleMessage(
      "Ativar para início de sessão",
    ),
    "enableForWallet": MessageLookupByLibrary.simpleMessage(
      "Ativar para carteira",
    ),
    "enableVacationMode": MessageLookupByLibrary.simpleMessage(
      "Ativar modo de férias",
    ),
    "endDateCantBeAfterFirstDate": MessageLookupByLibrary.simpleMessage(
      "Por favor, selecione uma data após a primeira data",
    ),
    "endsIn": m21,
    "english": MessageLookupByLibrary.simpleMessage("Inglês"),
    "enterAmount": MessageLookupByLibrary.simpleMessage("Introduza o montante"),
    "enterCaptcha": m22,
    "enterDescription": MessageLookupByLibrary.simpleMessage(
      "Insira a descrição",
    ),
    "enterEmailEachRecipient": MessageLookupByLibrary.simpleMessage(
      "Introduza um endereço de email para cada destinatário",
    ),
    "enterPoint": MessageLookupByLibrary.simpleMessage("Ponto de entrada"),
    "enterPrice": MessageLookupByLibrary.simpleMessage("Insira o preço"),
    "enterSentCode": MessageLookupByLibrary.simpleMessage(
      "Introduza o código enviado para",
    ),
    "enterVoucherCode": MessageLookupByLibrary.simpleMessage(
      "Introduza o código do vale",
    ),
    "enterYourEmail": MessageLookupByLibrary.simpleMessage(
      "Introduza o seu e-mail",
    ),
    "enterYourEmailOrUsername": MessageLookupByLibrary.simpleMessage(
      "Introduza o seu e-mail ou nome de utilizador",
    ),
    "enterYourFirstName": MessageLookupByLibrary.simpleMessage(
      "Introduza o seu primeiro nome",
    ),
    "enterYourLastName": MessageLookupByLibrary.simpleMessage(
      "Introduza o seu último nome",
    ),
    "enterYourMobile": MessageLookupByLibrary.simpleMessage(
      "Por favor, introduza o seu número de telemóvel",
    ),
    "enterYourNote": MessageLookupByLibrary.simpleMessage("Insira a sua nota"),
    "enterYourPassword": MessageLookupByLibrary.simpleMessage(
      "Introduza a sua palavra-passe",
    ),
    "enterYourPhone": MessageLookupByLibrary.simpleMessage(
      "Introduza o seu número de telefone para começar.",
    ),
    "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Introduza o seu número de telefone",
    ),
    "enterYourUsername": MessageLookupByLibrary.simpleMessage(
      "Introduza o seu nome de utilizador",
    ),
    "error": m23,
    "errorAmountTransfer": MessageLookupByLibrary.simpleMessage(
      "O montante introduzido é superior ao montante atual da carteira. Por favor, tente novamente!",
    ),
    "errorEmailFormat": MessageLookupByLibrary.simpleMessage(
      "Por favor, introduza um endereço de e-mail válido.",
    ),
    "errorMsg": m24,
    "errorOnGettingPost": MessageLookupByLibrary.simpleMessage(
      "Erro ao obter a publicação!",
    ),
    "errorPasswordFormat": MessageLookupByLibrary.simpleMessage(
      "Por favor, introduza uma palavra-passe com pelo menos 8 caracteres",
    ),
    "errorTitle": MessageLookupByLibrary.simpleMessage("Erro"),
    "evening": MessageLookupByLibrary.simpleMessage("Noite"),
    "events": MessageLookupByLibrary.simpleMessage("Eventos"),
    "expectedDeliveryDate": MessageLookupByLibrary.simpleMessage(
      "Data prevista de entrega",
    ),
    "expired": MessageLookupByLibrary.simpleMessage("Expirado"),
    "expiredDate": MessageLookupByLibrary.simpleMessage("Data expirada"),
    "expiredDateHint": MessageLookupByLibrary.simpleMessage("MM/AA"),
    "expiringInTime": m25,
    "exploreNow": MessageLookupByLibrary.simpleMessage("Explorar agora"),
    "external": MessageLookupByLibrary.simpleMessage("Externo"),
    "extraServices": MessageLookupByLibrary.simpleMessage("Serviços extra"),
    "failToAssign": MessageLookupByLibrary.simpleMessage(
      "Falha ao atribuir utilizador",
    ),
    "failedToGenerateLink": MessageLookupByLibrary.simpleMessage(
      "Falha ao gerar ligação",
    ),
    "failedToLoadAppConfig": MessageLookupByLibrary.simpleMessage(
      "Falha ao carregar a configuração da aplicação. Por favor, tente novamente ou reinicie a sua aplicação.",
    ),
    "failedToLoadImage": MessageLookupByLibrary.simpleMessage(
      "Falha ao carregar a imagem",
    ),
    "fair": MessageLookupByLibrary.simpleMessage("Razoável"),
    "favorite": MessageLookupByLibrary.simpleMessage("Favorito"),
    "fax": MessageLookupByLibrary.simpleMessage("Fax"),
    "feature": MessageLookupByLibrary.simpleMessage("Característica"),
    "featureNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Funcionalidade não disponível",
    ),
    "featureProducts": MessageLookupByLibrary.simpleMessage(
      "Produtos em destaque",
    ),
    "featured": MessageLookupByLibrary.simpleMessage("Em destaque"),
    "features": MessageLookupByLibrary.simpleMessage("Características"),
    "fileIsTooBig": MessageLookupByLibrary.simpleMessage(
      "O ficheiro é muito grande. Por favor, escolha um ficheiro mais pequeno!",
    ),
    "fileUploadFailed": MessageLookupByLibrary.simpleMessage(
      "Falha no carregamento do ficheiro!",
    ),
    "files": MessageLookupByLibrary.simpleMessage("Ficheiros"),
    "filter": MessageLookupByLibrary.simpleMessage("Filtro"),
    "fingerprintsTouchID": MessageLookupByLibrary.simpleMessage(
      "Impressões digitais, Touch ID",
    ),
    "finishSetup": MessageLookupByLibrary.simpleMessage(
      "Concluir configuração",
    ),
    "finnish": MessageLookupByLibrary.simpleMessage("Finlandês"),
    "firstComment": MessageLookupByLibrary.simpleMessage(
      "Seja o primeiro a comentar esta publicação!",
    ),
    "firstName": MessageLookupByLibrary.simpleMessage("Primeiro nome"),
    "firstNameIsRequired": MessageLookupByLibrary.simpleMessage(
      "O campo do primeiro nome é obrigatório",
    ),
    "firstRenewal": MessageLookupByLibrary.simpleMessage("Primeira renovação"),
    "fixedCartDiscount": MessageLookupByLibrary.simpleMessage(
      "Desconto fixo no carrinho",
    ),
    "fixedProductDiscount": MessageLookupByLibrary.simpleMessage(
      "Desconto fixo no produto",
    ),
    "forThisProduct": MessageLookupByLibrary.simpleMessage("para este produto"),
    "free": MessageLookupByLibrary.simpleMessage("Gratuito"),
    "freeOfCharge": MessageLookupByLibrary.simpleMessage("Gratuito"),
    "french": MessageLookupByLibrary.simpleMessage("Francês"),
    "friday": MessageLookupByLibrary.simpleMessage("Sexta-feira"),
    "from": MessageLookupByLibrary.simpleMessage("De"),
    "fullName": MessageLookupByLibrary.simpleMessage("Nome completo"),
    "gallery": MessageLookupByLibrary.simpleMessage("Galeria"),
    "generalError": MessageLookupByLibrary.simpleMessage(
      "Algo correu mal. Por favor, tente novamente.",
    ),
    "generalSetting": MessageLookupByLibrary.simpleMessage(
      "Configuração geral",
    ),
    "generatingLink": MessageLookupByLibrary.simpleMessage(
      "A gerar ligação...",
    ),
    "german": MessageLookupByLibrary.simpleMessage("Alemão"),
    "getNotification": MessageLookupByLibrary.simpleMessage(
      "Receber notificação",
    ),
    "getNotified": MessageLookupByLibrary.simpleMessage("Seja notificado!"),
    "getPasswordLink": MessageLookupByLibrary.simpleMessage(
      "Obter ligação da palavra-passe",
    ),
    "getStarted": MessageLookupByLibrary.simpleMessage("Começar"),
    "goBack": MessageLookupByLibrary.simpleMessage("Voltar"),
    "goBackHomePage": MessageLookupByLibrary.simpleMessage(
      "Voltar à página inicial",
    ),
    "goBackToAddress": MessageLookupByLibrary.simpleMessage(
      "Voltar ao endereço",
    ),
    "goBackToReview": MessageLookupByLibrary.simpleMessage("Voltar à revisão"),
    "goBackToShipping": MessageLookupByLibrary.simpleMessage("Voltar ao envio"),
    "gold": MessageLookupByLibrary.simpleMessage("Ouro"),
    "goldPriority": MessageLookupByLibrary.simpleMessage("Prioridade Ouro"),
    "good": MessageLookupByLibrary.simpleMessage("Bom"),
    "graphqlAuthError": MessageLookupByLibrary.simpleMessage(
      "Falha na autenticação. Por favor, inicie sessão novamente.",
    ),
    "graphqlAuthzError": MessageLookupByLibrary.simpleMessage(
      "Não tem permissão para executar esta ação.",
    ),
    "graphqlError": MessageLookupByLibrary.simpleMessage(
      "Algo correu mal ao tentar executar esta ação. Por favor, verifique novamente",
    ),
    "graphqlValidationError": MessageLookupByLibrary.simpleMessage(
      "Dados inválidos fornecidos. Por favor, verifique a sua entrada.",
    ),
    "greaterDistance": m26,
    "greek": MessageLookupByLibrary.simpleMessage("Grego"),
    "grossSales": MessageLookupByLibrary.simpleMessage("Vendas brutas"),
    "grouped": MessageLookupByLibrary.simpleMessage("Agrupado"),
    "guests": MessageLookupByLibrary.simpleMessage("Convidados"),
    "hasBeenDeleted": MessageLookupByLibrary.simpleMessage("foi eliminado"),
    "hebrew": MessageLookupByLibrary.simpleMessage("Hebraico"),
    "hideAbout": MessageLookupByLibrary.simpleMessage("Ocultar sobre"),
    "hideAddress": MessageLookupByLibrary.simpleMessage("Ocultar endereço"),
    "hideEmail": MessageLookupByLibrary.simpleMessage("Ocultar e-mail"),
    "hideMap": MessageLookupByLibrary.simpleMessage("Ocultar mapa"),
    "hidePhone": MessageLookupByLibrary.simpleMessage("Ocultar telefone"),
    "hidePolicy": MessageLookupByLibrary.simpleMessage("Ocultar política"),
    "hindi": MessageLookupByLibrary.simpleMessage("Hindi"),
    "history": MessageLookupByLibrary.simpleMessage("História"),
    "historyTransaction": MessageLookupByLibrary.simpleMessage("História"),
    "home": MessageLookupByLibrary.simpleMessage("Início"),
    "horizontal": MessageLookupByLibrary.simpleMessage("Horizontal"),
    "hour": MessageLookupByLibrary.simpleMessage("Hora"),
    "hoursAgo": m27,
    "howToEarnPoints": MessageLookupByLibrary.simpleMessage(
      "Como ganhar pontos?",
    ),
    "hungarian": MessageLookupByLibrary.simpleMessage("Húngaro"),
    "hungary": MessageLookupByLibrary.simpleMessage("Húngaro"),
    "iAgree": MessageLookupByLibrary.simpleMessage("Concordo com"),
    "imIn": MessageLookupByLibrary.simpleMessage("Estou dentro"),
    "imageFeature": MessageLookupByLibrary.simpleMessage(
      "Característica de imagem",
    ),
    "imageGallery": MessageLookupByLibrary.simpleMessage("Galeria de imagens"),
    "imageGenerate": MessageLookupByLibrary.simpleMessage("Geração de imagem"),
    "imageNetwork": MessageLookupByLibrary.simpleMessage("Rede de imagens"),
    "images": MessageLookupByLibrary.simpleMessage("Imagens"),
    "inStock": MessageLookupByLibrary.simpleMessage("Em stock"),
    "incorrectPassword": MessageLookupByLibrary.simpleMessage(
      "Palavra-passe incorreta",
    ),
    "india": MessageLookupByLibrary.simpleMessage("Hindi"),
    "indonesian": MessageLookupByLibrary.simpleMessage("Indonésio"),
    "informationTable": MessageLookupByLibrary.simpleMessage(
      "Tabela de informações",
    ),
    "installDigitsPlugin": MessageLookupByLibrary.simpleMessage(
      "Por favor, instale o plugin DIGITS: WordPress Mobile Number Signup and Login",
    ),
    "instantlyClose": MessageLookupByLibrary.simpleMessage(
      "Fechar instantaneamente",
    ),
    "insufficientBalanceMessage": m28,
    "invalidAddress": MessageLookupByLibrary.simpleMessage(
      "Por favor, introduza um endereço válido",
    ),
    "invalidAddressFormat": MessageLookupByLibrary.simpleMessage(
      "Por favor, introduza um endereço completo com o nome da rua e o número",
    ),
    "invalidCity": MessageLookupByLibrary.simpleMessage(
      "Por favor, introduza um nome de cidade válido",
    ),
    "invalidCityFormat": MessageLookupByLibrary.simpleMessage(
      "Por favor, introduza um nome de cidade válido sem caracteres especiais",
    ),
    "invalidCountry": MessageLookupByLibrary.simpleMessage(
      "Selecione um país válido",
    ),
    "invalidCountryCode": MessageLookupByLibrary.simpleMessage(
      "Selecione um código de país válido",
    ),
    "invalidCountryCodeFormat": MessageLookupByLibrary.simpleMessage(
      "Selecione um código de país válido na lista",
    ),
    "invalidCountryFormat": MessageLookupByLibrary.simpleMessage(
      "Selecione um país da lista",
    ),
    "invalidEmail": MessageLookupByLibrary.simpleMessage(
      "Por favor, introduza um endereço de e-mail válido",
    ),
    "invalidEmailFormat": MessageLookupByLibrary.simpleMessage(
      "Introduza um formato de e-mail válido (por exemplo, exemplo@domínio.com)",
    ),
    "invalidPhone": MessageLookupByLibrary.simpleMessage(
      "Por favor introduza um número de telefone válido",
    ),
    "invalidPhoneFormat": MessageLookupByLibrary.simpleMessage(
      "Por favor, introduza um formato de número de telefone válido",
    ),
    "invalidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Número de telefone inválido",
    ),
    "invalidPostalCode": MessageLookupByLibrary.simpleMessage(
      "Por favor introduza um código postal válido",
    ),
    "invalidPostalCodeFormat": MessageLookupByLibrary.simpleMessage(
      "Por favor, introduza um formato de código postal válido para o seu país",
    ),
    "invalidProvince": MessageLookupByLibrary.simpleMessage(""),
    "invalidProvinceFormat": MessageLookupByLibrary.simpleMessage(""),
    "invalidSMSCode": MessageLookupByLibrary.simpleMessage(
      "Código de verificação SMS inválido",
    ),
    "invalidYearOfBirth": MessageLookupByLibrary.simpleMessage(
      "Ano de nascimento inválido",
    ),
    "invoice": MessageLookupByLibrary.simpleMessage("Fatura"),
    "isEverythingSet": MessageLookupByLibrary.simpleMessage(
      "Está tudo definido...?",
    ),
    "isTyping": MessageLookupByLibrary.simpleMessage("está a escrever..."),
    "italian": MessageLookupByLibrary.simpleMessage("Italiano"),
    "item": MessageLookupByLibrary.simpleMessage("Item"),
    "itemCondition": MessageLookupByLibrary.simpleMessage(""),
    "itemConditionNew": MessageLookupByLibrary.simpleMessage(""),
    "itemTotal": MessageLookupByLibrary.simpleMessage("Total de itens:"),
    "items": MessageLookupByLibrary.simpleMessage("Itens"),
    "itsOrdered": MessageLookupByLibrary.simpleMessage("Foi encomendado!"),
    "iwantToCreateAccount": MessageLookupByLibrary.simpleMessage(
      "Quero criar uma conta",
    ),
    "japanese": MessageLookupByLibrary.simpleMessage("Japonês"),
    "kannada": MessageLookupByLibrary.simpleMessage("Kannada"),
    "keep": MessageLookupByLibrary.simpleMessage("Manter"),
    "khmer": MessageLookupByLibrary.simpleMessage("Khmer"),
    "korean": MessageLookupByLibrary.simpleMessage("Coreano"),
    "kurdish": MessageLookupByLibrary.simpleMessage("Curdo"),
    "language": MessageLookupByLibrary.simpleMessage("Idiomas"),
    "languageSuccess": MessageLookupByLibrary.simpleMessage(
      "O idioma foi atualizado com sucesso",
    ),
    "lao": MessageLookupByLibrary.simpleMessage("Lao"),
    "lastName": MessageLookupByLibrary.simpleMessage("Último nome"),
    "lastNameIsRequired": MessageLookupByLibrary.simpleMessage(
      "O campo do último nome é obrigatório",
    ),
    "lastTransactions": MessageLookupByLibrary.simpleMessage(
      "Últimas transações",
    ),
    "latestProducts": MessageLookupByLibrary.simpleMessage("Últimos produtos"),
    "layout": MessageLookupByLibrary.simpleMessage("Layouts"),
    "lightTheme": MessageLookupByLibrary.simpleMessage("Tema claro"),
    "link": MessageLookupByLibrary.simpleMessage("Ligação"),
    "list": MessageLookupByLibrary.simpleMessage("lista"),
    "listBannerType": MessageLookupByLibrary.simpleMessage(
      "Tipo de banner de lista",
    ),
    "listBannerVideo": MessageLookupByLibrary.simpleMessage(
      "Lista de vídeo de banner",
    ),
    "listMessages": MessageLookupByLibrary.simpleMessage("Notificar mensagens"),
    "listTile": MessageLookupByLibrary.simpleMessage("Lista de blocos"),
    "listening": MessageLookupByLibrary.simpleMessage("A ouvir..."),
    "loadFail": MessageLookupByLibrary.simpleMessage("Falha no carregamento!"),
    "loadFailed": MessageLookupByLibrary.simpleMessage(""),
    "loading": MessageLookupByLibrary.simpleMessage("A carregar..."),
    "loadingLink": MessageLookupByLibrary.simpleMessage(
      "A carregar ligação...",
    ),
    "location": MessageLookupByLibrary.simpleMessage("Localização"),
    "lockScreenAndSecurity": MessageLookupByLibrary.simpleMessage(
      "Ecrã de bloqueio e segurança",
    ),
    "login": MessageLookupByLibrary.simpleMessage("Iniciar sessão"),
    "loginCanceled": MessageLookupByLibrary.simpleMessage(
      "O início de sessão foi cancelado",
    ),
    "loginErrorServiceProvider": m29,
    "loginFailed": MessageLookupByLibrary.simpleMessage(
      "Falha no início de sessão!",
    ),
    "loginInvalid": MessageLookupByLibrary.simpleMessage(
      "Não está autorizado a usar esta aplicação.",
    ),
    "loginRequired": MessageLookupByLibrary.simpleMessage(
      "Início de sessão necessário",
    ),
    "loginSuccess": MessageLookupByLibrary.simpleMessage(
      "Início de sessão com sucesso!",
    ),
    "loginToComment": MessageLookupByLibrary.simpleMessage(
      "Por favor, inicie sessão para comentar",
    ),
    "loginToContinue": MessageLookupByLibrary.simpleMessage(
      "Por favor, inicie sessão para continuar",
    ),
    "loginToReview": MessageLookupByLibrary.simpleMessage(
      "Inicie sessão para avaliar",
    ),
    "loginToYourAccount": MessageLookupByLibrary.simpleMessage(
      "Inicie sessão na sua conta",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("Terminar sessão"),
    "logoutFailed": MessageLookupByLibrary.simpleMessage(""),
    "logoutSuccess": MessageLookupByLibrary.simpleMessage(""),
    "loyaltyVoucher": MessageLookupByLibrary.simpleMessage(""),
    "malay": MessageLookupByLibrary.simpleMessage("Malaio"),
    "manCollections": MessageLookupByLibrary.simpleMessage(
      "Coleções masculinas",
    ),
    "manageApiKey": MessageLookupByLibrary.simpleMessage("Gerir chave API"),
    "manageStock": MessageLookupByLibrary.simpleMessage("Gerir stock"),
    "map": MessageLookupByLibrary.simpleMessage("Mapa"),
    "marathi": MessageLookupByLibrary.simpleMessage("Marathi"),
    "markAsRead": MessageLookupByLibrary.simpleMessage("Marcar como lido"),
    "markAsShipped": MessageLookupByLibrary.simpleMessage(
      "Marcar como enviado",
    ),
    "markAsUnread": MessageLookupByLibrary.simpleMessage(
      "Marcar como não lido",
    ),
    "maxAmountForPayment": m30,
    "maximumFileSizeMb": m31,
    "maybeLater": MessageLookupByLibrary.simpleMessage("Talvez mais tarde"),
    "menuOrder": MessageLookupByLibrary.simpleMessage("Ordem do menu"),
    "menuServiceItems": m32,
    "menus": MessageLookupByLibrary.simpleMessage("Menus"),
    "message": MessageLookupByLibrary.simpleMessage("Mensagem"),
    "messageTo": MessageLookupByLibrary.simpleMessage("Enviar mensagem para"),
    "minAmountForPayment": m33,
    "minOrderAmount": m34,
    "minTotalCouponInvalidMsg": m35,
    "minTransaction": m36,
    "minimumQuantityIs": MessageLookupByLibrary.simpleMessage(
      "A quantidade mínima é",
    ),
    "minutesAgo": m37,
    "mobile": MessageLookupByLibrary.simpleMessage("Telemóvel"),
    "mobileIsRequired": MessageLookupByLibrary.simpleMessage(
      "O número de telemóvel é obrigatório",
    ),
    "mobileNumberInUse": MessageLookupByLibrary.simpleMessage(
      "Número de telemóvel já em utilização!",
    ),
    "mobileNumberIsNotRegistered": MessageLookupByLibrary.simpleMessage(
      "O número de telefone não está registrado!",
    ),
    "mobileVerification": MessageLookupByLibrary.simpleMessage(
      "Verificação móvel",
    ),
    "momentAgo": MessageLookupByLibrary.simpleMessage("Há um momento"),
    "monday": MessageLookupByLibrary.simpleMessage("Segunda-feira"),
    "monthsAgo": m38,
    "more": MessageLookupByLibrary.simpleMessage("...mais"),
    "moreFromStore": m39,
    "moreInformation": MessageLookupByLibrary.simpleMessage("Mais informação"),
    "morning": MessageLookupByLibrary.simpleMessage("Manhã"),
    "multipleSellersDetected": MessageLookupByLibrary.simpleMessage(
      "Vários vendedores detetados",
    ),
    "multipleSellersDetectedAndDisableMultiVendorCheckoutContent":
        MessageLookupByLibrary.simpleMessage(
          "Está a tentar adicionar um produto de um novo vendedor ao seu carrinho. Note que apenas pode comprar a um vendedor de cada vez.",
        ),
    "multipleSellersDetectedAndEnableMultiVendorCheckoutContent":
        MessageLookupByLibrary.simpleMessage(
          "Está a tentar adicionar um produto de um novo vendedor ao seu carrinho. Pretende continuar?",
        ),
    "mustBeBoughtInGroupsOf": m40,
    "mustSelectOneItem": MessageLookupByLibrary.simpleMessage(
      "Deve selecionar 1 item",
    ),
    "myCart": MessageLookupByLibrary.simpleMessage("O meu carrinho"),
    "myCoupons": MessageLookupByLibrary.simpleMessage(""),
    "myOrder": MessageLookupByLibrary.simpleMessage("A minha encomenda"),
    "myPoints": MessageLookupByLibrary.simpleMessage("Os meus pontos"),
    "myProducts": MessageLookupByLibrary.simpleMessage("Os meus produtos"),
    "myProductsEmpty": MessageLookupByLibrary.simpleMessage(
      "Não tem nenhum produto. Tente criar um!",
    ),
    "myQRCode": MessageLookupByLibrary.simpleMessage(""),
    "myQRCodeNote": MessageLookupByLibrary.simpleMessage(""),
    "myRating": MessageLookupByLibrary.simpleMessage("A minha avaliação"),
    "myReviews": MessageLookupByLibrary.simpleMessage("As minhas avaliações"),
    "myWallet": MessageLookupByLibrary.simpleMessage("A minha carteira"),
    "myWishList": MessageLookupByLibrary.simpleMessage(
      "A minha lista de desejos",
    ),
    "nItems": m41,
    "name": MessageLookupByLibrary.simpleMessage("Nome"),
    "nameOnCard": MessageLookupByLibrary.simpleMessage("Nome no cartão"),
    "nearbyPlaces": MessageLookupByLibrary.simpleMessage("Lugares próximos"),
    "needHelp": MessageLookupByLibrary.simpleMessage("Precisa de ajuda?"),
    "needToLoginAgain": MessageLookupByLibrary.simpleMessage(
      "Precisa de iniciar sessão novamente para efetuar a atualização",
    ),
    "netherlands": MessageLookupByLibrary.simpleMessage("Holandês"),
    "networkError": MessageLookupByLibrary.simpleMessage(""),
    "networkServerError": MessageLookupByLibrary.simpleMessage(""),
    "networkTimeout": MessageLookupByLibrary.simpleMessage(""),
    "newAppConfig": MessageLookupByLibrary.simpleMessage(
      "Novo conteúdo disponível!",
    ),
    "newPassword": MessageLookupByLibrary.simpleMessage("Nova palavra-passe"),
    "newVariation": MessageLookupByLibrary.simpleMessage("Nova variação"),
    "next": MessageLookupByLibrary.simpleMessage("Seguinte"),
    "niceName": MessageLookupByLibrary.simpleMessage("Nome simpático"),
    "no": MessageLookupByLibrary.simpleMessage("Não"),
    "noAddressHaveBeenSaved": MessageLookupByLibrary.simpleMessage(
      "Nenhum endereço foi guardado ainda",
    ),
    "noBackHistoryItem": MessageLookupByLibrary.simpleMessage(
      "Nenhum item do histórico anterior",
    ),
    "noBlog": MessageLookupByLibrary.simpleMessage(
      "Ops, o blog parece não existir mais",
    ),
    "noCameraPermissionIsGranted": MessageLookupByLibrary.simpleMessage(
      "Não é concedida permissão de câmara. Por favor, conceda-a nas definições do seu dispositivo.",
    ),
    "noComments": MessageLookupByLibrary.simpleMessage("Sem comentários"),
    "noConversation": MessageLookupByLibrary.simpleMessage(
      "Ainda não há conversa",
    ),
    "noConversationDescription": MessageLookupByLibrary.simpleMessage(
      "Aparecerá quando alguém iniciar uma conversa consigo",
    ),
    "noData": MessageLookupByLibrary.simpleMessage("Sem mais dados"),
    "noFavoritesYet": MessageLookupByLibrary.simpleMessage(
      "Ainda não há favoritos.",
    ),
    "noFileToDownload": MessageLookupByLibrary.simpleMessage(
      "Nenhum ficheiro para transferir.",
    ),
    "noForwardHistoryItem": MessageLookupByLibrary.simpleMessage(
      "Nenhum item do histórico seguinte",
    ),
    "noInternetConnection": MessageLookupByLibrary.simpleMessage(
      "Sem ligação à Internet",
    ),
    "noListingNearby": MessageLookupByLibrary.simpleMessage(
      "Nenhuma listagem por perto!",
    ),
    "noOrders": MessageLookupByLibrary.simpleMessage("Sem pedidos"),
    "noPaymentMethodsAvailable": MessageLookupByLibrary.simpleMessage(
      "Não existem métodos de pagamento disponíveis.",
    ),
    "noPermissionForCurrentRole": MessageLookupByLibrary.simpleMessage(
      "Desculpe, este produto não está acessível para a sua função atual",
    ),
    "noPermissionToViewProduct": MessageLookupByLibrary.simpleMessage(
      "Este produto está disponível apenas para utilizadores com funções específicas. Por favor, inicie sessão com as credenciais apropriadas para aceder a este produto ou contacte-nos para mais informações",
    ),
    "noPermissionToViewProductMsg": MessageLookupByLibrary.simpleMessage(
      "Por favor, inicie sessão com as credenciais apropriadas para aceder a este produto ou contacte-nos para mais informações",
    ),
    "noPost": MessageLookupByLibrary.simpleMessage(
      "Ops, esta página parece não existir mais!",
    ),
    "noPrinters": MessageLookupByLibrary.simpleMessage("Sem impressoras"),
    "noProduct": MessageLookupByLibrary.simpleMessage("Sem produto"),
    "noResultFound": MessageLookupByLibrary.simpleMessage(
      "Nenhum resultado encontrado",
    ),
    "noReviews": MessageLookupByLibrary.simpleMessage("Sem avaliações"),
    "noSlotAvailable": MessageLookupByLibrary.simpleMessage(
      "Nenhum slot disponível",
    ),
    "noStoreNearby": MessageLookupByLibrary.simpleMessage(
      "Nenhuma loja por perto!",
    ),
    "noSuggestionSearch": MessageLookupByLibrary.simpleMessage("Sem sugestões"),
    "noThanks": MessageLookupByLibrary.simpleMessage("Não, obrigado"),
    "noTransactionsMsg": MessageLookupByLibrary.simpleMessage(
      "Lamentamos, não foram encontradas transações!",
    ),
    "noVideoFound": MessageLookupByLibrary.simpleMessage(
      "Desculpe, nenhum vídeo encontrado",
    ),
    "none": MessageLookupByLibrary.simpleMessage("Nenhum"),
    "normal": MessageLookupByLibrary.simpleMessage(""),
    "notFindResult": MessageLookupByLibrary.simpleMessage(
      "Desculpe, não encontrámos nenhum resultado",
    ),
    "notFound": MessageLookupByLibrary.simpleMessage("Não encontrado"),
    "notRated": MessageLookupByLibrary.simpleMessage("Não avaliado"),
    "note": MessageLookupByLibrary.simpleMessage("Nota do pedido"),
    "noteMessage": MessageLookupByLibrary.simpleMessage("Nota"),
    "noteOptional": MessageLookupByLibrary.simpleMessage(""),
    "noteTransfer": MessageLookupByLibrary.simpleMessage("Nota (opcional)"),
    "notice": MessageLookupByLibrary.simpleMessage("Aviso"),
    "notifications": MessageLookupByLibrary.simpleMessage("Notificações"),
    "notifyLatestOffer": MessageLookupByLibrary.simpleMessage(
      "Notificar últimas ofertas e disponibilidade de produtos",
    ),
    "ofThisProduct": MessageLookupByLibrary.simpleMessage("deste produto"),
    "ok": MessageLookupByLibrary.simpleMessage("OK"),
    "on": MessageLookupByLibrary.simpleMessage("em"),
    "onSale": MessageLookupByLibrary.simpleMessage("À venda"),
    "onVacation": MessageLookupByLibrary.simpleMessage("De férias"),
    "oneEachRecipient": MessageLookupByLibrary.simpleMessage(
      "1 para cada destinatário",
    ),
    "online": MessageLookupByLibrary.simpleMessage("Online"),
    "open24Hours": MessageLookupByLibrary.simpleMessage("Aberto 24 horas"),
    "openMap": MessageLookupByLibrary.simpleMessage("Mapa"),
    "openNow": MessageLookupByLibrary.simpleMessage("Aberto agora"),
    "openSettings": MessageLookupByLibrary.simpleMessage("Abrir configurações"),
    "openingHours": MessageLookupByLibrary.simpleMessage(
      "Horário de funcionamento",
    ),
    "optional": MessageLookupByLibrary.simpleMessage("Opcional"),
    "options": MessageLookupByLibrary.simpleMessage("Opções"),
    "optionsTotal": m42,
    "or": MessageLookupByLibrary.simpleMessage("ou"),
    "orLoginWith": MessageLookupByLibrary.simpleMessage("ou inicie sessão com"),
    "orderConfirmation": MessageLookupByLibrary.simpleMessage(
      "Confirmação do pedido",
    ),
    "orderConfirmationMsg": MessageLookupByLibrary.simpleMessage(
      "Tem a certeza que deseja criar o pedido?",
    ),
    "orderDate": MessageLookupByLibrary.simpleMessage("Data do pedido"),
    "orderDetail": MessageLookupByLibrary.simpleMessage("Detalhes do pedido"),
    "orderHistory": MessageLookupByLibrary.simpleMessage(
      "Histórico de pedidos",
    ),
    "orderId": MessageLookupByLibrary.simpleMessage("ID do pedido:"),
    "orderIdWithoutColon": MessageLookupByLibrary.simpleMessage("ID do pedido"),
    "orderNo": MessageLookupByLibrary.simpleMessage("Nº do pedido"),
    "orderNotes": MessageLookupByLibrary.simpleMessage("Notas do pedido"),
    "orderNumber": MessageLookupByLibrary.simpleMessage("Número do pedido"),
    "orderStatusCanceledReversal": MessageLookupByLibrary.simpleMessage(
      "Cancelamento revertido",
    ),
    "orderStatusCancelled": MessageLookupByLibrary.simpleMessage("Cancelado"),
    "orderStatusChargeBack": MessageLookupByLibrary.simpleMessage("Estorno"),
    "orderStatusCompleted": MessageLookupByLibrary.simpleMessage("Concluído"),
    "orderStatusDenied": MessageLookupByLibrary.simpleMessage("Negado"),
    "orderStatusExpired": MessageLookupByLibrary.simpleMessage("Expirado"),
    "orderStatusFailed": MessageLookupByLibrary.simpleMessage("Falhou"),
    "orderStatusOnHold": MessageLookupByLibrary.simpleMessage("Em espera"),
    "orderStatusPending": MessageLookupByLibrary.simpleMessage("Pendente"),
    "orderStatusPendingPayment": MessageLookupByLibrary.simpleMessage(
      "Pagamento pendente",
    ),
    "orderStatusProcessed": MessageLookupByLibrary.simpleMessage("Processado"),
    "orderStatusProcessing": MessageLookupByLibrary.simpleMessage(
      "A processar",
    ),
    "orderStatusRefunded": MessageLookupByLibrary.simpleMessage("Reembolsado"),
    "orderStatusReversed": MessageLookupByLibrary.simpleMessage("Revertido"),
    "orderStatusShipped": MessageLookupByLibrary.simpleMessage("Enviado"),
    "orderStatusVoided": MessageLookupByLibrary.simpleMessage("Anulado"),
    "orderSuccessMsg1": MessageLookupByLibrary.simpleMessage(
      "Pode verificar o estado do seu pedido usando a nossa funcionalidade de estado de entrega. Receberá um e-mail de confirmação do pedido com os detalhes do seu pedido e uma ligação para acompanhar o seu progresso.",
    ),
    "orderSuccessMsg2": MessageLookupByLibrary.simpleMessage(
      "Pode iniciar sessão na sua conta usando o e-mail e palavra-passe definidos anteriormente. Na sua conta pode editar os dados do seu perfil, verificar o histórico de transações, editar a subscrição da newsletter.",
    ),
    "orderSuccessTitle1": MessageLookupByLibrary.simpleMessage(
      "Colocou o pedido com sucesso",
    ),
    "orderSuccessTitle2": MessageLookupByLibrary.simpleMessage("A sua conta"),
    "orderSummary": MessageLookupByLibrary.simpleMessage("Resumo do pedido"),
    "orderTotal": MessageLookupByLibrary.simpleMessage("Total do pedido"),
    "orderTracking": MessageLookupByLibrary.simpleMessage(
      "Rastreamento de pedidos",
    ),
    "orders": MessageLookupByLibrary.simpleMessage("Pedidos"),
    "otpVerification": MessageLookupByLibrary.simpleMessage("Verificação OTP"),
    "ourBankDetails": MessageLookupByLibrary.simpleMessage(
      "Os nossos dados bancários",
    ),
    "outOfStock": MessageLookupByLibrary.simpleMessage("Sem stock"),
    "pageView": MessageLookupByLibrary.simpleMessage("Visualização da página"),
    "paid": MessageLookupByLibrary.simpleMessage("Pago"),
    "paidStatus": MessageLookupByLibrary.simpleMessage("Estado do pagamento"),
    "password": MessageLookupByLibrary.simpleMessage("Palavra-passe"),
    "passwordIsRequired": MessageLookupByLibrary.simpleMessage(
      "O campo da palavra-passe é obrigatório",
    ),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "As palavras-passe não coincidem",
    ),
    "pasteYourImageUrl": MessageLookupByLibrary.simpleMessage(
      "Cole o URL da sua imagem",
    ),
    "payByWallet": MessageLookupByLibrary.simpleMessage("Pagar com carteira"),
    "payNow": MessageLookupByLibrary.simpleMessage("Pagar agora"),
    "payWithAmount": m43,
    "payment": MessageLookupByLibrary.simpleMessage("Pagamento"),
    "paymentDetailsChangedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Os detalhes de pagamento foram alterados com sucesso.",
    ),
    "paymentMethod": MessageLookupByLibrary.simpleMessage(
      "Método de pagamento",
    ),
    "paymentMethodIsNotSupported": MessageLookupByLibrary.simpleMessage(
      "Este método de pagamento não é suportado",
    ),
    "paymentMethods": MessageLookupByLibrary.simpleMessage(
      "Métodos de pagamento",
    ),
    "paymentSettings": MessageLookupByLibrary.simpleMessage(
      "Definições de pagamento",
    ),
    "paymentSuccessful": MessageLookupByLibrary.simpleMessage(
      "Pagamento efetuado com sucesso",
    ),
    "pending": MessageLookupByLibrary.simpleMessage("Pendente"),
    "pendingReviews": MessageLookupByLibrary.simpleMessage(
      "Avaliações pendentes",
    ),
    "persian": MessageLookupByLibrary.simpleMessage("Persa"),
    "phone": MessageLookupByLibrary.simpleMessage("Telemóvel"),
    "phoneEmpty": MessageLookupByLibrary.simpleMessage(
      "O telemóvel está vazio",
    ),
    "phoneHintFormat": MessageLookupByLibrary.simpleMessage(
      "Formato: +351123456789",
    ),
    "phoneIsRequired": MessageLookupByLibrary.simpleMessage(
      "O campo do número de telemóvel é obrigatório",
    ),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("Número de telemóvel"),
    "phoneNumberVerification": MessageLookupByLibrary.simpleMessage(
      "Verificação do número de telemóvel",
    ),
    "pickADate": MessageLookupByLibrary.simpleMessage("Escolha a data e hora"),
    "pickVoucherToApply": MessageLookupByLibrary.simpleMessage(""),
    "picking": MessageLookupByLibrary.simpleMessage("A aguardar recolha"),
    "placeMyOrder": MessageLookupByLibrary.simpleMessage(
      "Fazer a minha encomenda",
    ),
    "platinum": MessageLookupByLibrary.simpleMessage(""),
    "platinumPriority": MessageLookupByLibrary.simpleMessage(""),
    "playAll": MessageLookupByLibrary.simpleMessage("Reproduzir tudo"),
    "pleaseAddPrice": MessageLookupByLibrary.simpleMessage(
      "Por favor, adicione o preço",
    ),
    "pleaseAgreeTerms": MessageLookupByLibrary.simpleMessage(
      "Por favor, concorde com os nossos termos",
    ),
    "pleaseAllowAccessCameraGallery": MessageLookupByLibrary.simpleMessage(
      "Por favor, permita o acesso à câmara e à galeria",
    ),
    "pleaseCheckInternet": MessageLookupByLibrary.simpleMessage(
      "Por favor, verifique a ligação à Internet!",
    ),
    "pleaseChooseBranch": MessageLookupByLibrary.simpleMessage(
      "Por favor, escolha uma filial",
    ),
    "pleaseChooseCategory": MessageLookupByLibrary.simpleMessage(
      "Por favor, escolha a categoria",
    ),
    "pleaseEnterProductName": MessageLookupByLibrary.simpleMessage(
      "Por favor, introduza o nome do produto",
    ),
    "pleaseFillCode": MessageLookupByLibrary.simpleMessage(
      "Por favor, preencha o seu código",
    ),
    "pleaseFillUpAllCellsProperly": MessageLookupByLibrary.simpleMessage(
      "* Por favor, preencha todas as células corretamente",
    ),
    "pleaseIncreaseOrDecreaseTheQuantity": MessageLookupByLibrary.simpleMessage(
      "Por favor, aumente ou diminua a quantidade para continuar",
    ),
    "pleaseInput": MessageLookupByLibrary.simpleMessage(
      "Por favor, preencha todos os campos obrigatórios",
    ),
    "pleaseInputFillAllFields": MessageLookupByLibrary.simpleMessage(
      "Por favor, preencha todos os campos",
    ),
    "pleaseSelectADate": MessageLookupByLibrary.simpleMessage(
      "Por favor, selecione uma data de reserva",
    ),
    "pleaseSelectALocation": MessageLookupByLibrary.simpleMessage(
      "Por favor, selecione um local",
    ),
    "pleaseSelectAllAttributes": MessageLookupByLibrary.simpleMessage(
      "Por favor, escolha uma opção para cada atributo do produto",
    ),
    "pleaseSelectAttr": MessageLookupByLibrary.simpleMessage(
      "Por favor, selecione pelo menos 1 opção para cada atributo ativo",
    ),
    "pleaseSelectImages": MessageLookupByLibrary.simpleMessage(
      "Por favor, selecione as imagens",
    ),
    "pleaseSelectRequiredOptions": MessageLookupByLibrary.simpleMessage(
      "Por favor, selecione as opções necessárias!",
    ),
    "pleaseSignInBeforeUploading": MessageLookupByLibrary.simpleMessage(
      "Por favor, inicie sessão antes de enviar quaisquer ficheiros",
    ),
    "point": MessageLookupByLibrary.simpleMessage("Ponto"),
    "pointHistory": MessageLookupByLibrary.simpleMessage(""),
    "pointMsgConfigNotFound": MessageLookupByLibrary.simpleMessage(
      "Não foi encontrada nenhuma configuração de pontos de desconto no servidor",
    ),
    "pointMsgEnter": MessageLookupByLibrary.simpleMessage(
      "Por favor, introduza o ponto de desconto",
    ),
    "pointMsgMaximumDiscountPoint": MessageLookupByLibrary.simpleMessage(
      "Ponto máximo de desconto",
    ),
    "pointMsgNotEnough": MessageLookupByLibrary.simpleMessage(
      "Não tem pontos de desconto suficientes. O seu total de pontos de desconto é",
    ),
    "pointMsgOverMaximumDiscountPoint": MessageLookupByLibrary.simpleMessage(
      "Atingiu o ponto máximo de desconto",
    ),
    "pointMsgOverTotalBill": MessageLookupByLibrary.simpleMessage(
      "O valor total do desconto é superior ao total da fatura",
    ),
    "pointMsgRemove": MessageLookupByLibrary.simpleMessage(
      "O ponto de desconto foi removido",
    ),
    "pointMsgSuccess": MessageLookupByLibrary.simpleMessage(
      "O ponto de desconto foi aplicado com sucesso",
    ),
    "pointRewardMessage": MessageLookupByLibrary.simpleMessage(
      "Existe uma regra de desconto para aplicar os seus pontos no carrinho",
    ),
    "points": MessageLookupByLibrary.simpleMessage(""),
    "pointsAddedMsg": MessageLookupByLibrary.simpleMessage(""),
    "pointsAddedSuccessfully": MessageLookupByLibrary.simpleMessage(""),
    "pointsRedeemedMsg": MessageLookupByLibrary.simpleMessage(""),
    "pointsRedeemedSuccessfully": MessageLookupByLibrary.simpleMessage(""),
    "polish": MessageLookupByLibrary.simpleMessage("Polaco"),
    "poor": MessageLookupByLibrary.simpleMessage("Fraco"),
    "popular": MessageLookupByLibrary.simpleMessage("Popular"),
    "popularity": MessageLookupByLibrary.simpleMessage("Popularidade"),
    "posAddressToolTip": MessageLookupByLibrary.simpleMessage(
      "Este endereço será guardado no seu dispositivo local. NÃO é o endereço do utilizador",
    ),
    "postContent": MessageLookupByLibrary.simpleMessage("Conteúdo"),
    "postFail": MessageLookupByLibrary.simpleMessage(
      "A sua publicação não foi criada",
    ),
    "postImageFeature": MessageLookupByLibrary.simpleMessage(
      "Funcionalidade de imagem",
    ),
    "postManagement": MessageLookupByLibrary.simpleMessage(
      "Gestão de publicações",
    ),
    "postProduct": MessageLookupByLibrary.simpleMessage("Publicar produto"),
    "postSuccessfully": MessageLookupByLibrary.simpleMessage(
      "A sua publicação foi criada com sucesso",
    ),
    "postTitle": MessageLookupByLibrary.simpleMessage("Título"),
    "prepaid": MessageLookupByLibrary.simpleMessage("Pré-pago"),
    "prev": MessageLookupByLibrary.simpleMessage("Anterior"),
    "preview": MessageLookupByLibrary.simpleMessage("Pré-visualizar"),
    "price": MessageLookupByLibrary.simpleMessage("Preço"),
    "priceHighToLow": MessageLookupByLibrary.simpleMessage(
      "Preço: Do mais alto ao mais baixo",
    ),
    "priceLowToHigh": MessageLookupByLibrary.simpleMessage(
      "Preço: Do mais baixo ao mais alto",
    ),
    "prices": MessageLookupByLibrary.simpleMessage("Preços"),
    "printReceipt": MessageLookupByLibrary.simpleMessage("Imprimir recibo"),
    "printer": MessageLookupByLibrary.simpleMessage("Impressora"),
    "printerNotFound": MessageLookupByLibrary.simpleMessage(
      "A impressora não foi encontrada",
    ),
    "printerSelection": MessageLookupByLibrary.simpleMessage(
      "Seleção de impressora",
    ),
    "printing": MessageLookupByLibrary.simpleMessage("A imprimir..."),
    "privacyAndTerm": MessageLookupByLibrary.simpleMessage(
      "Privacidade e termos",
    ),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage(
      "Política de privacidade",
    ),
    "privacyTerms": MessageLookupByLibrary.simpleMessage(
      "Privacidade e termos",
    ),
    "private": MessageLookupByLibrary.simpleMessage("Privado"),
    "processing": MessageLookupByLibrary.simpleMessage("A processar..."),
    "product": MessageLookupByLibrary.simpleMessage("Produto"),
    "productAddToCart": m44,
    "productAdded": MessageLookupByLibrary.simpleMessage(
      "O produto foi adicionado",
    ),
    "productCreateReview": MessageLookupByLibrary.simpleMessage(
      "O seu produto aparecerá após revisão",
    ),
    "productExpired": MessageLookupByLibrary.simpleMessage(
      "Desculpe, este produto não pode ser acedido porque expirou",
    ),
    "productName": MessageLookupByLibrary.simpleMessage("Nome do produto"),
    "productNameCanNotEmpty": MessageLookupByLibrary.simpleMessage(
      "O nome do produto não pode estar vazio",
    ),
    "productNeedAtLeastOneVariation": MessageLookupByLibrary.simpleMessage(
      "A variável do tipo de produto necessita de pelo menos uma variante",
    ),
    "productNeedNameAndPrice": MessageLookupByLibrary.simpleMessage(
      "O tipo de produto simples necessita do nome e do preço normal",
    ),
    "productOutOfStock": MessageLookupByLibrary.simpleMessage(
      "Este produto está esgotado",
    ),
    "productOverview": MessageLookupByLibrary.simpleMessage(
      "Visão geral do produto",
    ),
    "productRating": MessageLookupByLibrary.simpleMessage("A sua avaliação"),
    "productReview": MessageLookupByLibrary.simpleMessage(
      "Avaliação do produto",
    ),
    "productType": MessageLookupByLibrary.simpleMessage("Tipo de produto"),
    "products": MessageLookupByLibrary.simpleMessage("Produtos"),
    "promptPayID": MessageLookupByLibrary.simpleMessage("ID do PromptPay:"),
    "promptPayName": MessageLookupByLibrary.simpleMessage("Nome do PromptPay:"),
    "promptPayType": MessageLookupByLibrary.simpleMessage("Tipo de PromptPay:"),
    "publish": MessageLookupByLibrary.simpleMessage("Publicar"),
    "pullToLoadMore": MessageLookupByLibrary.simpleMessage(
      "Puxe para carregar mais",
    ),
    "pullToRefresh": MessageLookupByLibrary.simpleMessage(""),
    "pullUpLoad": MessageLookupByLibrary.simpleMessage(""),
    "qRCodeMsgSuccess": MessageLookupByLibrary.simpleMessage(
      "O código QR foi guardado com sucesso",
    ),
    "qRCodeSaveFailure": MessageLookupByLibrary.simpleMessage(
      "Falha ao guardar o código QR",
    ),
    "qty": MessageLookupByLibrary.simpleMessage("Qtd"),
    "qtyTotal": m45,
    "quantity": MessageLookupByLibrary.simpleMessage("Quantidade"),
    "quantityProductExceedInStock": MessageLookupByLibrary.simpleMessage(
      "A quantidade atual é superior à quantidade em stock",
    ),
    "random": MessageLookupByLibrary.simpleMessage("Aleatório"),
    "rankDetails": MessageLookupByLibrary.simpleMessage(""),
    "rankDetailsMsg": MessageLookupByLibrary.simpleMessage(""),
    "rate": MessageLookupByLibrary.simpleMessage("Avaliar"),
    "rateProduct": MessageLookupByLibrary.simpleMessage("Avaliar produto"),
    "rateTheApp": MessageLookupByLibrary.simpleMessage("Avalie a aplicação"),
    "rateThisApp": MessageLookupByLibrary.simpleMessage(
      "Avalie esta aplicação",
    ),
    "rateThisAppDescription": MessageLookupByLibrary.simpleMessage(
      "Se gosta desta aplicação, dedique um pouco do seu tempo para a avaliar! Isso realmente ajuda-nos e não deverá demorar mais do que um minuto.",
    ),
    "rating": MessageLookupByLibrary.simpleMessage("Avaliação"),
    "ratingFirst": MessageLookupByLibrary.simpleMessage(
      "Por favor, avalie antes de enviar o seu comentário",
    ),
    "reOrder": MessageLookupByLibrary.simpleMessage("Reordenar"),
    "readReviews": MessageLookupByLibrary.simpleMessage("Avaliações"),
    "readyToPick": MessageLookupByLibrary.simpleMessage("Pronto para recolha"),
    "received": MessageLookupByLibrary.simpleMessage(""),
    "receivedMoney": MessageLookupByLibrary.simpleMessage("Dinheiro recebido"),
    "receivedMoneyFrom": m46,
    "receiver": MessageLookupByLibrary.simpleMessage("Destinatário"),
    "recent": MessageLookupByLibrary.simpleMessage("Recente"),
    "recentSearches": MessageLookupByLibrary.simpleMessage("Histórico"),
    "recentView": MessageLookupByLibrary.simpleMessage(
      "A sua visualização recente",
    ),
    "recentlyViewed": MessageLookupByLibrary.simpleMessage(
      "Visto recentemente",
    ),
    "recommended": MessageLookupByLibrary.simpleMessage("Recomendado"),
    "recurringTotals": MessageLookupByLibrary.simpleMessage(
      "Totais recorrentes",
    ),
    "redeem": MessageLookupByLibrary.simpleMessage(""),
    "redeemPoints": MessageLookupByLibrary.simpleMessage(""),
    "redeemRewards": MessageLookupByLibrary.simpleMessage(""),
    "redeemed": MessageLookupByLibrary.simpleMessage(""),
    "refresh": MessageLookupByLibrary.simpleMessage("Atualizar"),
    "refreshCompleted": MessageLookupByLibrary.simpleMessage(""),
    "refreshing": MessageLookupByLibrary.simpleMessage(""),
    "refund": MessageLookupByLibrary.simpleMessage("Reembolso"),
    "refundOrderFailed": MessageLookupByLibrary.simpleMessage(
      "O pedido de reembolso não foi bem-sucedido",
    ),
    "refundOrderSuccess": MessageLookupByLibrary.simpleMessage(
      "O pedido de reembolso foi efetuado com sucesso!",
    ),
    "refundRequest": MessageLookupByLibrary.simpleMessage(
      "Pedido de reembolso",
    ),
    "refundRequested": MessageLookupByLibrary.simpleMessage(
      "Reembolso solicitado",
    ),
    "refunds": MessageLookupByLibrary.simpleMessage("Reembolsos"),
    "regenerateResponse": MessageLookupByLibrary.simpleMessage(
      "Regenerar resposta",
    ),
    "registerAs": MessageLookupByLibrary.simpleMessage("Registar como"),
    "registerAsVendor": MessageLookupByLibrary.simpleMessage(
      "Registar como vendedor",
    ),
    "registerErrorSyncAccount": MessageLookupByLibrary.simpleMessage(
      "Não é possível sincronizar a conta. Por favor, inicie sessão para continuar.",
    ),
    "registerFailed": MessageLookupByLibrary.simpleMessage("O registo falhou"),
    "registerInvalid": MessageLookupByLibrary.simpleMessage(
      "O pedido é inválido ou expirou. Tente novamente",
    ),
    "registerSuccess": MessageLookupByLibrary.simpleMessage(
      "Registo efetuado com sucesso",
    ),
    "regularPrice": MessageLookupByLibrary.simpleMessage("Preço normal"),
    "relatedLayoutTitle": MessageLookupByLibrary.simpleMessage(
      "Produtos que poderá gostar",
    ),
    "releaseToLoadMore": MessageLookupByLibrary.simpleMessage(
      "Solte para carregar mais",
    ),
    "releaseToRefresh": MessageLookupByLibrary.simpleMessage(""),
    "remainingAmountCod": MessageLookupByLibrary.simpleMessage(""),
    "remove": MessageLookupByLibrary.simpleMessage("Remover"),
    "removeFromWishList": MessageLookupByLibrary.simpleMessage(
      "Remover da lista de desejos",
    ),
    "removeWishlist": MessageLookupByLibrary.simpleMessage(
      "Remover da lista de desejos",
    ),
    "removeWishlistContent": m47,
    "requestBooking": MessageLookupByLibrary.simpleMessage("Solicitar reserva"),
    "requestTooMany": MessageLookupByLibrary.simpleMessage(
      "Solicitou demasiados códigos num curto período de tempo. Por favor, tente novamente mais tarde.",
    ),
    "resend": MessageLookupByLibrary.simpleMessage("Reenviar"),
    "reservePrice": MessageLookupByLibrary.simpleMessage(""),
    "reset": MessageLookupByLibrary.simpleMessage("Repor"),
    "resetPassword": MessageLookupByLibrary.simpleMessage(
      "Repor palavra-passe",
    ),
    "resetYourPassword": MessageLookupByLibrary.simpleMessage(
      "Repor a sua palavra-passe",
    ),
    "results": MessageLookupByLibrary.simpleMessage("Resultados"),
    "retry": MessageLookupByLibrary.simpleMessage("Tentar novamente"),
    "reverse": MessageLookupByLibrary.simpleMessage(""),
    "review": MessageLookupByLibrary.simpleMessage("Pré-visualização"),
    "reviewApproval": MessageLookupByLibrary.simpleMessage(
      "Aprovação de avaliação",
    ),
    "reviewPendingApproval": MessageLookupByLibrary.simpleMessage(
      "A sua avaliação foi enviada e está a aguardar aprovação!",
    ),
    "reviewSent": MessageLookupByLibrary.simpleMessage(
      "A sua avaliação foi enviada!",
    ),
    "reviews": MessageLookupByLibrary.simpleMessage("Avaliações"),
    "rewards": MessageLookupByLibrary.simpleMessage(""),
    "romanian": MessageLookupByLibrary.simpleMessage("Romeno"),
    "russian": MessageLookupByLibrary.simpleMessage("Russo"),
    "sale": m48,
    "salePrice": MessageLookupByLibrary.simpleMessage("Preço promocional"),
    "saturday": MessageLookupByLibrary.simpleMessage("Sábado"),
    "save": MessageLookupByLibrary.simpleMessage("Guardar"),
    "saveAddress": MessageLookupByLibrary.simpleMessage("Guardar endereço"),
    "saveAddressSuccess": MessageLookupByLibrary.simpleMessage(
      "O seu endereço foi guardado com sucesso",
    ),
    "saveForLater": MessageLookupByLibrary.simpleMessage(
      "Guardar para mais tarde",
    ),
    "saveQRCode": MessageLookupByLibrary.simpleMessage("Guardar código QR"),
    "saveToWishList": MessageLookupByLibrary.simpleMessage(
      "Guardar na lista de desejos",
    ),
    "scanPoints": MessageLookupByLibrary.simpleMessage(
      "Pontos de digitalização",
    ),
    "scanQRCode": MessageLookupByLibrary.simpleMessage("Escaneie o código QR"),
    "scannerCannotScan": MessageLookupByLibrary.simpleMessage(
      "Este item não pode ser digitalizado",
    ),
    "scannerLoginFirst": MessageLookupByLibrary.simpleMessage(
      "Para digitalizar uma encomenda, precisa primeiro de iniciar sessão",
    ),
    "scannerOrderAvailable": MessageLookupByLibrary.simpleMessage(
      "Esta encomenda não está disponível para a sua conta",
    ),
    "search": MessageLookupByLibrary.simpleMessage("Pesquisar"),
    "searchByCountryNameOrDialCode": MessageLookupByLibrary.simpleMessage(
      "Pesquisar por nome do país ou código de marcação",
    ),
    "searchByName": MessageLookupByLibrary.simpleMessage(
      "Pesquisar por nome...",
    ),
    "searchEmptyDataMessage": MessageLookupByLibrary.simpleMessage(
      "Ups! Parece que não existem resultados para os seus critérios de pesquisa",
    ),
    "searchForItems": MessageLookupByLibrary.simpleMessage("Pesquisar itens"),
    "searchInput": MessageLookupByLibrary.simpleMessage(
      "Por favor, escreva no campo de pesquisa",
    ),
    "searchOrderId": MessageLookupByLibrary.simpleMessage(
      "Pesquisar ID de encomenda...",
    ),
    "searchPlace": MessageLookupByLibrary.simpleMessage("Local de pesquisa"),
    "searchResultFor": m49,
    "searchResultItem": m50,
    "searchResultItems": m51,
    "searchingAddress": MessageLookupByLibrary.simpleMessage(
      "A pesquisar endereço",
    ),
    "secondsAgo": m52,
    "seeAll": MessageLookupByLibrary.simpleMessage("Ver tudo"),
    "seeNewAppConfig": MessageLookupByLibrary.simpleMessage(
      "Continue a ver novos conteúdos na sua aplicação",
    ),
    "seeOrder": MessageLookupByLibrary.simpleMessage("Ver encomenda"),
    "seeReviews": MessageLookupByLibrary.simpleMessage("Ver avaliações"),
    "select": MessageLookupByLibrary.simpleMessage("Selecionar"),
    "selectAddress": MessageLookupByLibrary.simpleMessage(
      "Selecionar endereço",
    ),
    "selectAll": MessageLookupByLibrary.simpleMessage("Selecionar tudo"),
    "selectDate": MessageLookupByLibrary.simpleMessage(""),
    "selectDates": MessageLookupByLibrary.simpleMessage("Selecionar datas"),
    "selectFileCancelled": MessageLookupByLibrary.simpleMessage(
      "Seleção de ficheiro cancelada!",
    ),
    "selectImage": MessageLookupByLibrary.simpleMessage("Selecionar imagem"),
    "selectItem": MessageLookupByLibrary.simpleMessage("Selecionar item"),
    "selectNone": MessageLookupByLibrary.simpleMessage("Não selecionar nenhum"),
    "selectPrinter": MessageLookupByLibrary.simpleMessage(
      "Selecionar impressora",
    ),
    "selectRole": MessageLookupByLibrary.simpleMessage("Selecionar função"),
    "selectStore": MessageLookupByLibrary.simpleMessage("Selecionar loja"),
    "selectTheColor": MessageLookupByLibrary.simpleMessage("Selecione a cor"),
    "selectTheFile": MessageLookupByLibrary.simpleMessage(
      "Selecionar o ficheiro",
    ),
    "selectThePoint": MessageLookupByLibrary.simpleMessage("Selecione o ponto"),
    "selectTheQuantity": MessageLookupByLibrary.simpleMessage(
      "Selecione a quantidade",
    ),
    "selectTheSize": MessageLookupByLibrary.simpleMessage(
      "Selecione o tamanho",
    ),
    "selectType": MessageLookupByLibrary.simpleMessage("Selecione o tipo"),
    "selectVoucher": MessageLookupByLibrary.simpleMessage("Selecionar vale"),
    "send": MessageLookupByLibrary.simpleMessage("Enviar"),
    "sendBack": MessageLookupByLibrary.simpleMessage("Devolver"),
    "sendSMSCode": MessageLookupByLibrary.simpleMessage("Obter código"),
    "sendSMStoVendor": MessageLookupByLibrary.simpleMessage(
      "Enviar SMS para o dono da loja",
    ),
    "sendTo": MessageLookupByLibrary.simpleMessage(
      "A conta para a qual pretende transferir (email)",
    ),
    "separateMultipleEmailWithComma": MessageLookupByLibrary.simpleMessage(
      "Separe vários endereços de email com vírgulas",
    ),
    "serbian": MessageLookupByLibrary.simpleMessage("Sérvio"),
    "sessionExpired": MessageLookupByLibrary.simpleMessage("Sessão expirada"),
    "setAnAddressInSettingPage": MessageLookupByLibrary.simpleMessage(
      "Por favor, defina um endereço na página de definições",
    ),
    "settings": MessageLookupByLibrary.simpleMessage("Definições"),
    "setup": MessageLookupByLibrary.simpleMessage("Configuração"),
    "share": MessageLookupByLibrary.simpleMessage("Partilhar"),
    "shareProductData": MessageLookupByLibrary.simpleMessage(
      "Partilhar dados do produto",
    ),
    "shareProductLink": MessageLookupByLibrary.simpleMessage(
      "Partilhar ligação do produto",
    ),
    "shipped": MessageLookupByLibrary.simpleMessage("Enviado"),
    "shipping": MessageLookupByLibrary.simpleMessage("Envio"),
    "shippingAddress": MessageLookupByLibrary.simpleMessage(
      "Endereço de envio",
    ),
    "shippingFee": MessageLookupByLibrary.simpleMessage("Taxa de envio"),
    "shippingMethod": MessageLookupByLibrary.simpleMessage("Método de envio"),
    "shop": MessageLookupByLibrary.simpleMessage("Loja"),
    "shopEmail": MessageLookupByLibrary.simpleMessage("Email da loja"),
    "shopName": MessageLookupByLibrary.simpleMessage("Nome da loja"),
    "shopOrders": MessageLookupByLibrary.simpleMessage("Encomendas da loja"),
    "shopPhone": MessageLookupByLibrary.simpleMessage("Telefone da loja"),
    "shopSlug": MessageLookupByLibrary.simpleMessage("Slug da loja"),
    "shopifyCustomerAccountLoginDescription":
        MessageLookupByLibrary.simpleMessage(""),
    "shopifyCustomerAccountLoginTitle": MessageLookupByLibrary.simpleMessage(
      "",
    ),
    "shoppingCartItems": m53,
    "shortDescription": MessageLookupByLibrary.simpleMessage("Descrição breve"),
    "showAllMyOrdered": MessageLookupByLibrary.simpleMessage(
      "Mostrar todas as minhas encomendas",
    ),
    "showDetails": MessageLookupByLibrary.simpleMessage("Mostrar detalhes"),
    "showGallery": MessageLookupByLibrary.simpleMessage("Mostrar galeria"),
    "showLess": MessageLookupByLibrary.simpleMessage("Mostrar menos"),
    "showMore": MessageLookupByLibrary.simpleMessage("Mostrar mais"),
    "signIn": MessageLookupByLibrary.simpleMessage("Iniciar sessão"),
    "signInWithEmail": MessageLookupByLibrary.simpleMessage(
      "Iniciar sessão com email",
    ),
    "signUp": MessageLookupByLibrary.simpleMessage("Registar"),
    "signup": MessageLookupByLibrary.simpleMessage("Registar"),
    "silver": MessageLookupByLibrary.simpleMessage(""),
    "silverPriority": MessageLookupByLibrary.simpleMessage(""),
    "simple": MessageLookupByLibrary.simpleMessage("Simples"),
    "simpleList": MessageLookupByLibrary.simpleMessage("Lista Simples"),
    "size": MessageLookupByLibrary.simpleMessage("Tamanho"),
    "sizeGuide": MessageLookupByLibrary.simpleMessage("Guia de tamanhos"),
    "skip": MessageLookupByLibrary.simpleMessage("Ignorar"),
    "sku": MessageLookupByLibrary.simpleMessage("SKU"),
    "slovak": MessageLookupByLibrary.simpleMessage("Eslovaco"),
    "smsCodeExpired": MessageLookupByLibrary.simpleMessage(
      "O código SMS expirou. Por favor, reenvie o código de verificação para tentar novamente",
    ),
    "sold": m54,
    "soldBy": MessageLookupByLibrary.simpleMessage("Vendido por"),
    "somethingWrong": MessageLookupByLibrary.simpleMessage(
      "Algo correu mal. Por favor, tente novamente mais tarde",
    ),
    "sortBy": MessageLookupByLibrary.simpleMessage("Ordenar por"),
    "sortCode": MessageLookupByLibrary.simpleMessage("Código de ordenação"),
    "spanish": MessageLookupByLibrary.simpleMessage("Espanhol"),
    "speechNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Discurso não disponível",
    ),
    "spendAtLeast": MessageLookupByLibrary.simpleMessage(""),
    "startExploring": MessageLookupByLibrary.simpleMessage("Comece a explorar"),
    "startPrice": MessageLookupByLibrary.simpleMessage(""),
    "startShopping": MessageLookupByLibrary.simpleMessage("Comece a comprar"),
    "startingBid": MessageLookupByLibrary.simpleMessage(""),
    "state": MessageLookupByLibrary.simpleMessage("Estado"),
    "stateIsRequired": MessageLookupByLibrary.simpleMessage(
      "O campo do estado é obrigatório",
    ),
    "stateProvince": MessageLookupByLibrary.simpleMessage("Estado/Província"),
    "status": MessageLookupByLibrary.simpleMessage("Estado"),
    "stock": MessageLookupByLibrary.simpleMessage("Stock"),
    "stockQuantity": MessageLookupByLibrary.simpleMessage(
      "Quantidade em stock",
    ),
    "stop": MessageLookupByLibrary.simpleMessage("Parar"),
    "store": MessageLookupByLibrary.simpleMessage("Loja"),
    "storeAddress": MessageLookupByLibrary.simpleMessage("Endereço da loja"),
    "storeBanner": MessageLookupByLibrary.simpleMessage("Banner"),
    "storeBrand": MessageLookupByLibrary.simpleMessage("Marca da loja"),
    "storeClosed": MessageLookupByLibrary.simpleMessage(
      "A loja está fechada agora",
    ),
    "storeEmail": MessageLookupByLibrary.simpleMessage("Email da loja"),
    "storeInformation": MessageLookupByLibrary.simpleMessage(
      "Informação da loja",
    ),
    "storeListBanner": MessageLookupByLibrary.simpleMessage(
      "Banner da lista de lojas",
    ),
    "storeLocation": MessageLookupByLibrary.simpleMessage(
      "Localização da loja",
    ),
    "storeLocatorSearchPlaceholder": MessageLookupByLibrary.simpleMessage(
      "Introduza morada/cidade",
    ),
    "storeLogo": MessageLookupByLibrary.simpleMessage("Logótipo da loja"),
    "storeMobileBanner": MessageLookupByLibrary.simpleMessage(
      "Banner móvel da loja",
    ),
    "storeSettings": MessageLookupByLibrary.simpleMessage("Definições da loja"),
    "storeSliderBanner": MessageLookupByLibrary.simpleMessage(
      "Banner deslizante da loja",
    ),
    "storeStaticBanner": MessageLookupByLibrary.simpleMessage(
      "Banner estático da loja",
    ),
    "storeVacation": MessageLookupByLibrary.simpleMessage("Férias da loja"),
    "stores": MessageLookupByLibrary.simpleMessage("Lojas"),
    "street": MessageLookupByLibrary.simpleMessage("Rua"),
    "street2": MessageLookupByLibrary.simpleMessage("Rua 2"),
    "streetIsRequired": MessageLookupByLibrary.simpleMessage(
      "O campo do nome da rua é obrigatório",
    ),
    "streetName": MessageLookupByLibrary.simpleMessage("Nome da rua"),
    "streetNameApartment": MessageLookupByLibrary.simpleMessage("Apartamento"),
    "streetNameBlock": MessageLookupByLibrary.simpleMessage("Bloco"),
    "subTitleOrderConfirmed": MessageLookupByLibrary.simpleMessage(
      "Obrigado pela sua encomenda. Estamos a processá-la rapidamente. Receberá um email de confirmação em breve",
    ),
    "submit": MessageLookupByLibrary.simpleMessage("Submeter"),
    "submitYourPost": MessageLookupByLibrary.simpleMessage(
      "Submeta a sua publicação",
    ),
    "subtotal": MessageLookupByLibrary.simpleMessage("Subtotal"),
    "successful": MessageLookupByLibrary.simpleMessage("Bem-sucedido"),
    "sunday": MessageLookupByLibrary.simpleMessage("Domingo"),
    "support": MessageLookupByLibrary.simpleMessage("Suporte"),
    "swahili": MessageLookupByLibrary.simpleMessage("Suaíli"),
    "swedish": MessageLookupByLibrary.simpleMessage("Sueco"),
    "systemError": MessageLookupByLibrary.simpleMessage(""),
    "tag": MessageLookupByLibrary.simpleMessage("Etiqueta"),
    "tagNotExist": MessageLookupByLibrary.simpleMessage(
      "Esta etiqueta não existe",
    ),
    "tags": MessageLookupByLibrary.simpleMessage("Etiquetas"),
    "takePicture": MessageLookupByLibrary.simpleMessage("Tirar fotografia"),
    "tamil": MessageLookupByLibrary.simpleMessage("Tâmil"),
    "tapSelectLocation": MessageLookupByLibrary.simpleMessage(
      "Toque para selecionar este local",
    ),
    "tapTheMicToTalk": MessageLookupByLibrary.simpleMessage(
      "Toque no microfone para falar",
    ),
    "tax": MessageLookupByLibrary.simpleMessage("Imposto"),
    "teraWallet": MessageLookupByLibrary.simpleMessage("Carteira Tera"),
    "terrible": MessageLookupByLibrary.simpleMessage("Terrível"),
    "thailand": MessageLookupByLibrary.simpleMessage("Tailandês"),
    "theFieldIsRequired": m55,
    "thisDateIsNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Esta data não está disponível",
    ),
    "thisFeatureDoesNotSupportTheCurrentLanguage":
        MessageLookupByLibrary.simpleMessage(
          "Esta funcionalidade não é compatível com o idioma atual",
        ),
    "thisIsCustomerRole": MessageLookupByLibrary.simpleMessage(
      "Esta é a função do cliente",
    ),
    "thisIsDeliveryrRole": MessageLookupByLibrary.simpleMessage(
      "Esta é a função de entrega",
    ),
    "thisIsVendorRole": MessageLookupByLibrary.simpleMessage(
      "Esta é a função do vendedor",
    ),
    "thisItemIsSold": MessageLookupByLibrary.simpleMessage(""),
    "thisPlatformNotSupportWebview": MessageLookupByLibrary.simpleMessage(
      "Esta plataforma não suporta visualização web",
    ),
    "thisProductNotSupport": MessageLookupByLibrary.simpleMessage(
      "Este produto não é suportado",
    ),
    "thursday": MessageLookupByLibrary.simpleMessage("Quinta-feira"),
    "tickets": MessageLookupByLibrary.simpleMessage("Bilhetes"),
    "time": MessageLookupByLibrary.simpleMessage("Tempo"),
    "timeLeft": MessageLookupByLibrary.simpleMessage(""),
    "title": MessageLookupByLibrary.simpleMessage("Título"),
    "titleAToZ": MessageLookupByLibrary.simpleMessage("Título: A a Z"),
    "titleFirst": MessageLookupByLibrary.simpleMessage(
      "Por favor adicione o título",
    ),
    "titleZToA": MessageLookupByLibrary.simpleMessage("Título: Z a A"),
    "to": MessageLookupByLibrary.simpleMessage("Para"),
    "toRate": MessageLookupByLibrary.simpleMessage("Para avaliar"),
    "tooManyFailedLogin": MessageLookupByLibrary.simpleMessage(
      "Demasiadas tentativas de início de sessão falhadas. Por favor, tente novamente mais tarde",
    ),
    "topUp": MessageLookupByLibrary.simpleMessage("Carregar"),
    "topUpProductNotFound": MessageLookupByLibrary.simpleMessage(
      "Produto de carregamento não encontrado",
    ),
    "total": MessageLookupByLibrary.simpleMessage("Total"),
    "totalAmount": MessageLookupByLibrary.simpleMessage(""),
    "totalCartValue": MessageLookupByLibrary.simpleMessage(
      "O valor total da encomenda deve ser de pelo menos",
    ),
    "totalPoints": MessageLookupByLibrary.simpleMessage("Total de pontos"),
    "totalPrice": MessageLookupByLibrary.simpleMessage("Preço total"),
    "totalProducts": m56,
    "totalTax": MessageLookupByLibrary.simpleMessage("Total de impostos"),
    "trackingNumberIs": MessageLookupByLibrary.simpleMessage(
      "O número de rastreio é",
    ),
    "trackingPage": MessageLookupByLibrary.simpleMessage("Página de rastreio"),
    "transactionCancelled": MessageLookupByLibrary.simpleMessage(
      "Transação cancelada",
    ),
    "transactionDetail": MessageLookupByLibrary.simpleMessage(
      "Detalhe da transação",
    ),
    "transactionFailded": MessageLookupByLibrary.simpleMessage(
      "A transação falhou",
    ),
    "transactionFailed": MessageLookupByLibrary.simpleMessage(
      "Transação falhou",
    ),
    "transactionFee": MessageLookupByLibrary.simpleMessage("Taxa de transação"),
    "transactionResult": MessageLookupByLibrary.simpleMessage(
      "Resultado da transação",
    ),
    "transfer": MessageLookupByLibrary.simpleMessage("Transferir"),
    "transferConfirm": MessageLookupByLibrary.simpleMessage(
      "Confirmação de transferência",
    ),
    "transferErrorMessage": MessageLookupByLibrary.simpleMessage(
      "Não pode transferir para este utilizador",
    ),
    "transferFailed": MessageLookupByLibrary.simpleMessage(
      "A transferência falhou",
    ),
    "transferMoneyTo": m57,
    "transferSuccess": MessageLookupByLibrary.simpleMessage(
      "Transferência bem-sucedida",
    ),
    "tuesday": MessageLookupByLibrary.simpleMessage("Terça-feira"),
    "turkish": MessageLookupByLibrary.simpleMessage("Turco"),
    "turnOnBle": MessageLookupByLibrary.simpleMessage("Ligue o Bluetooth"),
    "typeAMessage": MessageLookupByLibrary.simpleMessage(
      "Escreva uma mensagem...",
    ),
    "typeYourMessage": MessageLookupByLibrary.simpleMessage(
      "Escreva a sua mensagem aqui...",
    ),
    "typing": MessageLookupByLibrary.simpleMessage("A escrever..."),
    "ukrainian": MessageLookupByLibrary.simpleMessage("Ucraniano"),
    "unavailable": MessageLookupByLibrary.simpleMessage("Indisponível"),
    "unblock": MessageLookupByLibrary.simpleMessage("Desbloquear"),
    "unblockUser": MessageLookupByLibrary.simpleMessage(
      "Desbloquear utilizador",
    ),
    "undo": MessageLookupByLibrary.simpleMessage("Desfazer"),
    "unexpectedError": MessageLookupByLibrary.simpleMessage(""),
    "unknownError": MessageLookupByLibrary.simpleMessage(""),
    "unpaid": MessageLookupByLibrary.simpleMessage("Por pagar"),
    "upRankNote1": MessageLookupByLibrary.simpleMessage(""),
    "upRankNote2": MessageLookupByLibrary.simpleMessage(""),
    "update": MessageLookupByLibrary.simpleMessage("Atualizar"),
    "updateFailed": MessageLookupByLibrary.simpleMessage("Atualização falhou!"),
    "updateInfo": MessageLookupByLibrary.simpleMessage("Atualizar informação"),
    "updatePassword": MessageLookupByLibrary.simpleMessage(
      "Atualizar palavra-passe",
    ),
    "updateStatus": MessageLookupByLibrary.simpleMessage("Atualizar estado"),
    "updateSuccess": MessageLookupByLibrary.simpleMessage(
      "Atualização com sucesso!",
    ),
    "updateUserFailed": MessageLookupByLibrary.simpleMessage(""),
    "updateUserInfor": MessageLookupByLibrary.simpleMessage("Atualizar perfil"),
    "uploadFile": MessageLookupByLibrary.simpleMessage("Carregar ficheiro"),
    "uploadImage": MessageLookupByLibrary.simpleMessage("Carregar imagem"),
    "uploadProduct": MessageLookupByLibrary.simpleMessage("Carregar produto"),
    "uploading": MessageLookupByLibrary.simpleMessage("A carregar"),
    "url": MessageLookupByLibrary.simpleMessage("URL"),
    "useAmountPoints": m58,
    "useMaximumPointDiscount": m59,
    "useNow": MessageLookupByLibrary.simpleMessage("Usar agora"),
    "usePoint": MessageLookupByLibrary.simpleMessage("Ponto de uso"),
    "useThisImage": MessageLookupByLibrary.simpleMessage("Usar esta imagem"),
    "used": MessageLookupByLibrary.simpleMessage(""),
    "userExists": MessageLookupByLibrary.simpleMessage(
      "Este nome de utilizador/email não está disponível",
    ),
    "userHasBeenBlocked": MessageLookupByLibrary.simpleMessage(
      "O utilizador foi bloqueado",
    ),
    "userNameInCorrect": MessageLookupByLibrary.simpleMessage(
      "O nome de utilizador ou palavra-passe está incorreto",
    ),
    "userNotFound": MessageLookupByLibrary.simpleMessage(
      "Utilizador não encontrado",
    ),
    "username": MessageLookupByLibrary.simpleMessage("Nome de utilizador"),
    "usernameAlreadyInUse": MessageLookupByLibrary.simpleMessage(
      "Nome de utilizador já em utilização!",
    ),
    "usernameAndPasswordRequired": MessageLookupByLibrary.simpleMessage(
      "Nome de utilizador e palavra-passe são obrigatórios",
    ),
    "usernameInvalid": MessageLookupByLibrary.simpleMessage(
      "Nome de utilizador inválido",
    ),
    "usernameIsRequired": MessageLookupByLibrary.simpleMessage(
      "O nome de utilizador é obrigatório",
    ),
    "vacationMessage": MessageLookupByLibrary.simpleMessage(
      "Mensagem de férias",
    ),
    "vacationType": MessageLookupByLibrary.simpleMessage("Tipo de férias"),
    "validUntil": m60,
    "validUntilDate": m61,
    "variable": MessageLookupByLibrary.simpleMessage("Variável"),
    "variation": MessageLookupByLibrary.simpleMessage("Variação"),
    "vendor": MessageLookupByLibrary.simpleMessage("Vendedor"),
    "vendorAdmin": MessageLookupByLibrary.simpleMessage(
      "Administrador do vendedor",
    ),
    "vendorInfo": MessageLookupByLibrary.simpleMessage(
      "Informação do vendedor",
    ),
    "verificationCode": MessageLookupByLibrary.simpleMessage(
      "Código de verificação (6 dígitos)",
    ),
    "verifySMSCode": MessageLookupByLibrary.simpleMessage("Verificar"),
    "version": m62,
    "viaWallet": MessageLookupByLibrary.simpleMessage("Via carteira"),
    "video": MessageLookupByLibrary.simpleMessage("Vídeo"),
    "vietnamese": MessageLookupByLibrary.simpleMessage("Vietnamita"),
    "view": MessageLookupByLibrary.simpleMessage("Ver"),
    "viewAll": MessageLookupByLibrary.simpleMessage(""),
    "viewCart": MessageLookupByLibrary.simpleMessage("Ver carrinho"),
    "viewDetail": MessageLookupByLibrary.simpleMessage("Ver detalhes"),
    "viewMore": MessageLookupByLibrary.simpleMessage("Ver mais"),
    "viewOnGoogleMaps": MessageLookupByLibrary.simpleMessage(
      "Ver no Google Maps",
    ),
    "viewOrder": MessageLookupByLibrary.simpleMessage("Ver encomenda"),
    "viewPointHistory": MessageLookupByLibrary.simpleMessage(""),
    "viewRecentTransactions": MessageLookupByLibrary.simpleMessage(
      "Ver transações recentes",
    ),
    "visible": MessageLookupByLibrary.simpleMessage("Visível"),
    "visitStore": MessageLookupByLibrary.simpleMessage("Visitar loja"),
    "waitForLoad": MessageLookupByLibrary.simpleMessage(
      "A aguardar o carregamento da imagem",
    ),
    "waitForPost": MessageLookupByLibrary.simpleMessage(
      "A aguardar a publicação do produto",
    ),
    "waiting": MessageLookupByLibrary.simpleMessage("A aguardar"),
    "waitingForConfirmation": MessageLookupByLibrary.simpleMessage(
      "A aguardar confirmação",
    ),
    "walletBalance": MessageLookupByLibrary.simpleMessage("Saldo da carteira"),
    "walletBalanceWithValue": m63,
    "walletName": MessageLookupByLibrary.simpleMessage("Nome da carteira"),
    "warning": m64,
    "warningCurrencyMessageForWallet": m65,
    "weFoundBlogs": MessageLookupByLibrary.simpleMessage("Encontrámos blog(s)"),
    "weFoundProducts": m66,
    "weNeedCameraAccessTo": MessageLookupByLibrary.simpleMessage(
      "Precisamos de acesso à câmara para digitalizar o código QR ou código de barras",
    ),
    "weSentAnOTPTo": MessageLookupByLibrary.simpleMessage(
      "Foi enviado um código de autenticação para",
    ),
    "weWillSendYouNotification": MessageLookupByLibrary.simpleMessage(
      "Enviaremos notificações quando novos produtos ou ofertas estiverem disponíveis. Pode sempre alterar esta definição nas definições",
    ),
    "webView": MessageLookupByLibrary.simpleMessage("Visualização web"),
    "website": MessageLookupByLibrary.simpleMessage("Website"),
    "wednesday": MessageLookupByLibrary.simpleMessage("Quarta-feira"),
    "week": m67,
    "welcome": MessageLookupByLibrary.simpleMessage("Bem-vindo"),
    "welcomeBack": MessageLookupByLibrary.simpleMessage(
      "Bem-vindo(a) de volta",
    ),
    "welcomeRegister": MessageLookupByLibrary.simpleMessage(
      "Comece a sua experiência de compras connosco agora",
    ),
    "welcomeUser": m68,
    "whichLanguageDoYouPrefer": MessageLookupByLibrary.simpleMessage(
      "Qual idioma prefere?",
    ),
    "wholesaleRegisterMsg": MessageLookupByLibrary.simpleMessage(
      "Por favor, contacte o administrador para aprovar o seu registo",
    ),
    "willNotSendAndReceiveMessage": MessageLookupByLibrary.simpleMessage(
      "Não poderá enviar nem receber mensagens deste utilizador.",
    ),
    "winningBid": MessageLookupByLibrary.simpleMessage(""),
    "withdrawAmount": MessageLookupByLibrary.simpleMessage(
      "Montante a levantar",
    ),
    "withdrawRequest": MessageLookupByLibrary.simpleMessage(
      "Pedido de levantamento",
    ),
    "withdrawal": MessageLookupByLibrary.simpleMessage("Levantamento"),
    "womanCollections": MessageLookupByLibrary.simpleMessage(
      "Coleções femininas",
    ),
    "writeComment": MessageLookupByLibrary.simpleMessage(
      "Escreva o seu comentário",
    ),
    "writeTitle": MessageLookupByLibrary.simpleMessage("Escreva seu título"),
    "writeYourNote": MessageLookupByLibrary.simpleMessage("Escreva a sua nota"),
    "yearsAgo": m69,
    "yes": MessageLookupByLibrary.simpleMessage("Sim"),
    "youAreOur": MessageLookupByLibrary.simpleMessage(""),
    "youAreSelecting": m70,
    "youCanOnlyOrderSingleStore": MessageLookupByLibrary.simpleMessage(
      "Só pode comprar numa única loja",
    ),
    "youCanOnlyPurchase": MessageLookupByLibrary.simpleMessage(
      "Só pode comprar",
    ),
    "youDontHaveAnyCoupons": MessageLookupByLibrary.simpleMessage(""),
    "youDontHavePermissionToCreatePost": MessageLookupByLibrary.simpleMessage(
      "Não tem permissão para criar uma publicação",
    ),
    "youHave": MessageLookupByLibrary.simpleMessage(""),
    "youHaveAssignedToOrder": m71,
    "youHaveBeenSaveAddressYourLocal": MessageLookupByLibrary.simpleMessage(
      "Guardou o endereço no seu local",
    ),
    "youHaveNoPost": MessageLookupByLibrary.simpleMessage(
      "Não tem publicações",
    ),
    "youHavePassed": m72,
    "youHavePoints": m73,
    "youMightAlsoLike": MessageLookupByLibrary.simpleMessage(
      "Também poderá gostar",
    ),
    "youNeedToLoginCheckout": MessageLookupByLibrary.simpleMessage(
      "Precisa de iniciar sessão para finalizar a compra",
    ),
    "youNotBeAsked": MessageLookupByLibrary.simpleMessage(
      "Não voltará a ser questionado após a conclusão",
    ),
    "yourAccountIsUnderReview": MessageLookupByLibrary.simpleMessage(
      "A sua conta está em análise. Por favor, contacte o administrador se precisar de ajuda",
    ),
    "yourAddressExistYourLocal": MessageLookupByLibrary.simpleMessage(
      "O seu endereço existe no seu local",
    ),
    "yourAddressHasBeenSaved": MessageLookupByLibrary.simpleMessage(
      "O endereço foi guardado no seu armazenamento local",
    ),
    "yourApplicationIsUnderReview": MessageLookupByLibrary.simpleMessage(
      "A sua candidatura está em análise",
    ),
    "yourBagIsEmpty": MessageLookupByLibrary.simpleMessage(
      "O seu carrinho está vazio",
    ),
    "yourBookingDetail": MessageLookupByLibrary.simpleMessage(
      "Detalhes da sua reserva",
    ),
    "yourEarningsThisMonth": MessageLookupByLibrary.simpleMessage(
      "Os seus ganhos este mês",
    ),
    "yourNote": MessageLookupByLibrary.simpleMessage("A sua nota"),
    "yourOrderHasBeenAdded": MessageLookupByLibrary.simpleMessage(
      "A sua encomenda foi adicionada",
    ),
    "yourOrderIsConfirmed": MessageLookupByLibrary.simpleMessage(
      "A sua encomenda foi confirmada!",
    ),
    "yourOrderIsEmpty": MessageLookupByLibrary.simpleMessage(
      "A sua encomenda está vazia",
    ),
    "yourOrderIsEmptyMsg": MessageLookupByLibrary.simpleMessage(
      "Parece que ainda não adicionou nenhum item.\nComece a comprar para preencher o seu carrinho.",
    ),
    "yourOrders": MessageLookupByLibrary.simpleMessage("As suas encomendas"),
    "yourProductIsUnderReview": MessageLookupByLibrary.simpleMessage(
      "O seu produto está em análise",
    ),
    "yourUsernameEmail": MessageLookupByLibrary.simpleMessage(
      "O seu nome de utilizador ou email",
    ),
    "zipCode": MessageLookupByLibrary.simpleMessage("Código postal"),
    "zipCodeIsRequired": MessageLookupByLibrary.simpleMessage(
      "O campo do código postal é obrigatório",
    ),
  };
}
