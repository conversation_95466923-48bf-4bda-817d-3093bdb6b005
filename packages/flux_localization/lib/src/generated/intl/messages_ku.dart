// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ku locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ku';

  static String m3(point) => "خاڵی بەردەست: ${point}";

  static String m19(day) => "${day} ڕۆژ پێش ئێستا";

  static String m21(timeLeft) => "${timeLeft} تەواو دەبێت";

  static String m23(message) => "هەڵە: ${message}";

  static String m25(time) => "بەسەردەچێت لە ${time}";

  static String m27(hour) => "${hour} کاتژمێر پێش ئێستا";

  static String m29(message) =>
      "کێشەیەک هەیە لەگەڵ سێرڤس لەکاتی داواکردنی داتا، تکایە پەیوەندی بکە بە سەرپەرشتیارەوى ئەپەکە بۆ چارەسەرکردنی کێشەکان: ${message}";

  static String m37(minute) => "${minute} خولەك پێش ئێستا";

  static String m41(itemCount) => "${itemCount} ئایتم";

  static String m42(price) => "کۆى گشتى هەڵبژاردنەکان: ${price}";

  static String m48(percent) => "داشکاندن ${percent} %";

  static String m52(second) => "${second} چرکە پێش ئێستا";

  static String m53(totalCartQuantity) =>
      "سەبەتەى کڕین, ${totalCartQuantity} ئایتم";

  static String m54(numberOfUnitsSold) => "Sold: ${numberOfUnitsSold}";

  static String m56(total) => "${total} بەرهەمەکان";

  static String m61(date) => "گونجاوە تا ${date}";

  static String m64(message) => "ئاگاداری: ${message}";

  static String m66(length) => "${length} بەرهەم دۆزرایەوە";

  static String m73(point) => "تۆ هەندێك پۆینتت هەیە";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "aboutUs": MessageLookupByLibrary.simpleMessage("دەربارەی ئێمە"),
    "addListing": MessageLookupByLibrary.simpleMessage("لیستێك زیاد بکە"),
    "addProduct": MessageLookupByLibrary.simpleMessage("زیادکردنى بەرهەم"),
    "addToCart": MessageLookupByLibrary.simpleMessage(
      "زیادکردن بۆ سەبەتەى کڕین",
    ),
    "addedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "بەسەرکەوتوویی زیادکرا",
    ),
    "addingYourImage": MessageLookupByLibrary.simpleMessage("وێنە زیاد بکە"),
    "additionalInformation": MessageLookupByLibrary.simpleMessage(
      "زانیاری زیاتر",
    ),
    "address": MessageLookupByLibrary.simpleMessage("ناونیشان"),
    "agreeWithPrivacy": MessageLookupByLibrary.simpleMessage(
      "مەرج و یاسای بەکارهێنان",
    ),
    "all": MessageLookupByLibrary.simpleMessage("هەموو"),
    "allOrders": MessageLookupByLibrary.simpleMessage("کۆتا فرۆشتنەکان"),
    "almostSoldOut": MessageLookupByLibrary.simpleMessage("نزیکە لە تەواوبوون"),
    "apply": MessageLookupByLibrary.simpleMessage("پەسەندکردن"),
    "approve": MessageLookupByLibrary.simpleMessage("پەسەندکردن"),
    "approved": MessageLookupByLibrary.simpleMessage("پەسەندکراوە"),
    "areYouSure": MessageLookupByLibrary.simpleMessage("دڵنیای؟"),
    "attributes": MessageLookupByLibrary.simpleMessage("تایبەتمەندیەکان"),
    "availability": MessageLookupByLibrary.simpleMessage("بەردەستێتی"),
    "availablePoints": m3,
    "back": MessageLookupByLibrary.simpleMessage(" گەڕانەو بۆ هەنگاوى پێشو "),
    "backOrder": MessageLookupByLibrary.simpleMessage("لەپاش داواکرد"),
    "backToShop": MessageLookupByLibrary.simpleMessage("بگەڕێوە بۆ فرۆشگا"),
    "bagsCollections": MessageLookupByLibrary.simpleMessage("کۆڵێکشن"),
    "billingAddress": MessageLookupByLibrary.simpleMessage("ناونیشانی پارەدان"),
    "blog": MessageLookupByLibrary.simpleMessage("بڵاوکراوەکان"),
    "booking": MessageLookupByLibrary.simpleMessage("شوێنگرتن"),
    "bookingError": MessageLookupByLibrary.simpleMessage(
      "شوێنگرتن سەرکەوتوو نەبوو، تکایە تەماشای زانیاریەکانت بکە",
    ),
    "bookingHistory": MessageLookupByLibrary.simpleMessage("مێژووی شوێنگرتن"),
    "bookingNow": MessageLookupByLibrary.simpleMessage("شوێن بگرە"),
    "bookingSuccess": MessageLookupByLibrary.simpleMessage(
      "شوێنگرتن بەسەرکەوتوویی تۆمارکرا",
    ),
    "bookingSummary": MessageLookupByLibrary.simpleMessage("پوختەی شوێنگرتن"),
    "brand": MessageLookupByLibrary.simpleMessage("براند"),
    "brazil": MessageLookupByLibrary.simpleMessage("بەڕازیل"),
    "buyNow": MessageLookupByLibrary.simpleMessage("ئێستا بیکڕە"),
    "byCategory": MessageLookupByLibrary.simpleMessage("بەپێی بەش"),
    "byPrice": MessageLookupByLibrary.simpleMessage("بەپێی نرخ"),
    "byTag": MessageLookupByLibrary.simpleMessage("بەپێی تاگ"),
    "canNotLaunch": MessageLookupByLibrary.simpleMessage(
      "ناتوانێت ئەم کاربەرنامەیە دەست پێ بکات، دڵنیابە لەوەی ڕێکبەندەکانی config.dart ڕاستن",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("هەڵوەشاندنەوە"),
    "cantFindThisOrderId": MessageLookupByLibrary.simpleMessage(
      "ئەم ژمارەى داواکارییە نەدۆزرایەوە",
    ),
    "cardHolder": MessageLookupByLibrary.simpleMessage("هەڵگرى کارت"),
    "cardNumber": MessageLookupByLibrary.simpleMessage("ژمارەى کارت"),
    "cart": MessageLookupByLibrary.simpleMessage("سەبەتەى کڕین"),
    "cartDiscount": MessageLookupByLibrary.simpleMessage("داشکاندن لەسەر سەلە"),
    "categories": MessageLookupByLibrary.simpleMessage("بەشەکان"),
    "category": MessageLookupByLibrary.simpleMessage("بەش"),
    "change": MessageLookupByLibrary.simpleMessage("گۆڕین"),
    "chatListScreen": MessageLookupByLibrary.simpleMessage("نامەکان"),
    "checkConfirmLink": MessageLookupByLibrary.simpleMessage(
      "ئیمەیلەکەت بپشکنە بۆ لینکی دووپاتکردنەوە",
    ),
    "checkout": MessageLookupByLibrary.simpleMessage("داواکردن"),
    "chinese": MessageLookupByLibrary.simpleMessage("چینی"),
    "chooseFromGallery": MessageLookupByLibrary.simpleMessage(
      "هەڵبژاردن لە گەلەریەوە",
    ),
    "chooseFromServer": MessageLookupByLibrary.simpleMessage(
      "هەڵبژاردن لە سێرڤەرەوە",
    ),
    "chooseYourPaymentMethod": MessageLookupByLibrary.simpleMessage(
      "شێوازی پارەدان هەڵبژێرە",
    ),
    "city": MessageLookupByLibrary.simpleMessage("شار"),
    "cityIsRequired": MessageLookupByLibrary.simpleMessage(" شار داواکراوە"),
    "clear": MessageLookupByLibrary.simpleMessage("پاککردنەوە"),
    "clearCart": MessageLookupByLibrary.simpleMessage(
      "پاککردنەوەى سەبەتەى کڕین",
    ),
    "close": MessageLookupByLibrary.simpleMessage("داخستن"),
    "color": MessageLookupByLibrary.simpleMessage("ڕەنگ"),
    "commentFirst": MessageLookupByLibrary.simpleMessage(
      "تکایە بۆچونەکەت بنوسە",
    ),
    "contact": MessageLookupByLibrary.simpleMessage("پەیوەندی"),
    "continueToPayment": MessageLookupByLibrary.simpleMessage(
      "بەردەوام بە بۆ پارەدان",
    ),
    "continueToReview": MessageLookupByLibrary.simpleMessage(
      "بەردەوام بە بۆ هەڵسەنگاندن",
    ),
    "continueToShipping": MessageLookupByLibrary.simpleMessage(
      "بەردەوام بە بۆ گەیاندن",
    ),
    "continues": MessageLookupByLibrary.simpleMessage("بەردەوامبە"),
    "conversations": MessageLookupByLibrary.simpleMessage("گفتوگۆکان"),
    "country": MessageLookupByLibrary.simpleMessage("وڵات"),
    "countryIsRequired": MessageLookupByLibrary.simpleMessage(
      " وڵات داواکراوە",
    ),
    "couponCode": MessageLookupByLibrary.simpleMessage("کۆپۆن کۆد"),
    "couponHasBeenSavedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "کۆپۆن کۆد بەسەرکەوتووی تۆمارکرا",
    ),
    "couponInvalid": MessageLookupByLibrary.simpleMessage(
      "کۆپپۆنەکەت تەواو نیە",
    ),
    "couponMsgSuccess": MessageLookupByLibrary.simpleMessage(
      "پیرۆزە، کۆپۆن کۆد بەسەرکەوتوویی بەکارهێندرا",
    ),
    "createAnAccount": MessageLookupByLibrary.simpleMessage(
      "دروستکردنی هەژمار",
    ),
    "createProduct": MessageLookupByLibrary.simpleMessage("بەرهەمێك زیاد بکە"),
    "createdOn": MessageLookupByLibrary.simpleMessage("دروستکراوە لە: "),
    "currencies": MessageLookupByLibrary.simpleMessage("دراوەکان"),
    "currentPassword": MessageLookupByLibrary.simpleMessage("تێپەڕوشەی ئێستا"),
    "currentlyWeOnlyHave": MessageLookupByLibrary.simpleMessage("ئێستا تەنیا"),
    "cvv": MessageLookupByLibrary.simpleMessage("CVV"),
    "darkTheme": MessageLookupByLibrary.simpleMessage("دۆخی تاریك"),
    "dashboard": MessageLookupByLibrary.simpleMessage("داشبۆرد"),
    "dataEmpty": MessageLookupByLibrary.simpleMessage("بەرواری بەتالە"),
    "date": MessageLookupByLibrary.simpleMessage("بەروار"),
    "dateBooking": MessageLookupByLibrary.simpleMessage("بەرواری شوێنگرتن"),
    "dateEnd": MessageLookupByLibrary.simpleMessage("بەرواری تەواوبون"),
    "dateStart": MessageLookupByLibrary.simpleMessage("بەرواری دەسپێکردن"),
    "daysAgo": m19,
    "deliveredTo": MessageLookupByLibrary.simpleMessage("گەیەندرا بۆ"),
    "description": MessageLookupByLibrary.simpleMessage("پێناسە"),
    "didntReceiveCode": MessageLookupByLibrary.simpleMessage(
      "کۆدت بەدەست نەگەیشتووە",
    ),
    "discount": MessageLookupByLibrary.simpleMessage("داشکاندن"),
    "displayName": MessageLookupByLibrary.simpleMessage("ناوی پێشاندان"),
    "doYouWantToExitApp": MessageLookupByLibrary.simpleMessage(
      "دەتەوێت بەرنامەکە دابخەیتەوە بکەیت؟",
    ),
    "done": MessageLookupByLibrary.simpleMessage("تەواوبوو"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage("هەژمارت نیە؟"),
    "download": MessageLookupByLibrary.simpleMessage("داگرتن"),
    "duration": MessageLookupByLibrary.simpleMessage("ماوە"),
    "earnings": MessageLookupByLibrary.simpleMessage("پەیداکردنەکان"),
    "edit": MessageLookupByLibrary.simpleMessage("گۆڕانکارى: "),
    "editProductInfo": MessageLookupByLibrary.simpleMessage(
      "گۆڕانکارى کردن لە زانیارى بەرهەم",
    ),
    "editWithoutColon": MessageLookupByLibrary.simpleMessage("گۆڕانکارى"),
    "egypt": MessageLookupByLibrary.simpleMessage("میسر"),
    "email": MessageLookupByLibrary.simpleMessage("ئیمەیل"),
    "emailIsRequired": MessageLookupByLibrary.simpleMessage(
      " ئیمەیل ئادرەس داواکراوە",
    ),
    "emptyCartSubtitle": MessageLookupByLibrary.simpleMessage(
      "وادیارە تا ئێستا هیچ بڕگەیەکت بۆ سەبەتەى کڕینەکانت زیاد نەکردووە. دەست بکە بە بازاڕکردن بۆ پڕکردنەوەی.",
    ),
    "emptyUsername": MessageLookupByLibrary.simpleMessage(
      "ناوی بەکارهێنەر بەتاڵە",
    ),
    "emptyWishlistSubtitle": MessageLookupByLibrary.simpleMessage(
      "کرتە لەسەر ئایکۆنى دڵ بکە ئێمە دڵخوازەکانت لێرە بۆ زیاد دەکەین",
    ),
    "endsIn": m21,
    "english": MessageLookupByLibrary.simpleMessage("ئینگلیزی"),
    "enterSentCode": MessageLookupByLibrary.simpleMessage(
      "ئەو کۆدە بنوسەوە کە نێردراوە بۆ",
    ),
    "enterYourEmail": MessageLookupByLibrary.simpleMessage("ئیمەیلەکەت بنوسە"),
    "enterYourPassword": MessageLookupByLibrary.simpleMessage(
      "تێپەڕوشەکەت بنوسە",
    ),
    "error": m23,
    "events": MessageLookupByLibrary.simpleMessage("بۆنەکان"),
    "expired": MessageLookupByLibrary.simpleMessage("بەسەرچووە"),
    "expiredDate": MessageLookupByLibrary.simpleMessage("بەروارى بەسەر چوون"),
    "expiredDateHint": MessageLookupByLibrary.simpleMessage("MM/YY"),
    "expiringInTime": m25,
    "extraServices": MessageLookupByLibrary.simpleMessage("خزمەتگوزاری زیاتر"),
    "featureProducts": MessageLookupByLibrary.simpleMessage("بابەتی تایبەت"),
    "featured": MessageLookupByLibrary.simpleMessage("تایبەت"),
    "features": MessageLookupByLibrary.simpleMessage("تایبەتمەندیەکان"),
    "fileIsTooBig": MessageLookupByLibrary.simpleMessage(
      "قەبارەى ئەم فایلە زۆر گەورەیە، تکایە فایلێکى قەبارە کەم تر هەڵبژێرە",
    ),
    "fileUploadFailed": MessageLookupByLibrary.simpleMessage(
      "بارکردنى فایل سەرکەوتوونەبوو!",
    ),
    "files": MessageLookupByLibrary.simpleMessage("فایلەکان"),
    "filter": MessageLookupByLibrary.simpleMessage("فلتەر"),
    "firstName": MessageLookupByLibrary.simpleMessage("ناوی یەکەم"),
    "firstNameIsRequired": MessageLookupByLibrary.simpleMessage(
      "بۆشایی ناوی یەکەم داواکراوە",
    ),
    "fixedCartDiscount": MessageLookupByLibrary.simpleMessage(
      "داشکاندنى نرخى پوختە لە کارت",
    ),
    "fixedProductDiscount": MessageLookupByLibrary.simpleMessage(
      "داشکاندنى نرخى پوختە لە بەرهەم",
    ),
    "forThisProduct": MessageLookupByLibrary.simpleMessage("بۆ ئەم بەرهەمە"),
    "french": MessageLookupByLibrary.simpleMessage("فەڕەنسی"),
    "from": MessageLookupByLibrary.simpleMessage("لە"),
    "gallery": MessageLookupByLibrary.simpleMessage("گەلەرى"),
    "generalSetting": MessageLookupByLibrary.simpleMessage(
      "ڕێکخستنە گشتییەکان",
    ),
    "german": MessageLookupByLibrary.simpleMessage("ئەڵمانی"),
    "getNotification": MessageLookupByLibrary.simpleMessage(
      "وەرگرتنی ئاگادارکردنەوەکان",
    ),
    "getPasswordLink": MessageLookupByLibrary.simpleMessage(
      "بەدەستهێنانی لینکی تێپەڕە ووشە",
    ),
    "goBackHomePage": MessageLookupByLibrary.simpleMessage(
      "بگەڕێوە لاپەڕەی سەرەکی",
    ),
    "goBackToAddress": MessageLookupByLibrary.simpleMessage(
      "بگەڕێوە بۆ ناونیشان",
    ),
    "goBackToReview": MessageLookupByLibrary.simpleMessage(
      "بگەڕێوە بۆ هەڵسەنگاندن",
    ),
    "goBackToShipping": MessageLookupByLibrary.simpleMessage(
      "بگەڕێوە بۆ گەیاندن",
    ),
    "grossSales": MessageLookupByLibrary.simpleMessage("Gross Sales"),
    "guests": MessageLookupByLibrary.simpleMessage("میوان"),
    "hebrew": MessageLookupByLibrary.simpleMessage("عیبرى"),
    "home": MessageLookupByLibrary.simpleMessage("سەرەکى"),
    "hour": MessageLookupByLibrary.simpleMessage("کاتژمێر"),
    "hoursAgo": m27,
    "hungary": MessageLookupByLibrary.simpleMessage("هەنگاریا"),
    "iAgree": MessageLookupByLibrary.simpleMessage("ڕازیم بە"),
    "imageGallery": MessageLookupByLibrary.simpleMessage("گەلەری وێنە"),
    "inStock": MessageLookupByLibrary.simpleMessage("هەمانە"),
    "indonesian": MessageLookupByLibrary.simpleMessage("ئەندەنۆسی"),
    "invalidSMSCode": MessageLookupByLibrary.simpleMessage(
      "کۆدی پشتڕاستکردنەوەی کورتە نامە هەڵەیە!",
    ),
    "italian": MessageLookupByLibrary.simpleMessage("ئیتاڵی"),
    "itemTotal": MessageLookupByLibrary.simpleMessage("کۆی گشتى ئایتم: "),
    "items": MessageLookupByLibrary.simpleMessage("ئایتم"),
    "itsOrdered": MessageLookupByLibrary.simpleMessage("داواکردنەکەت  نێردرا"),
    "iwantToCreateAccount": MessageLookupByLibrary.simpleMessage(
      "دەمەوێت هەژمار دروست بکەم",
    ),
    "japanese": MessageLookupByLibrary.simpleMessage("یابانی"),
    "kurdish": MessageLookupByLibrary.simpleMessage("Kurdish"),
    "language": MessageLookupByLibrary.simpleMessage("زمان"),
    "languageSuccess": MessageLookupByLibrary.simpleMessage(
      "زمان بەسەرکەوتوویی گۆڕدرا",
    ),
    "lastName": MessageLookupByLibrary.simpleMessage("ناوی باوك"),
    "lastNameIsRequired": MessageLookupByLibrary.simpleMessage(
      " ناوی باوك داواکراوە",
    ),
    "layout": MessageLookupByLibrary.simpleMessage("لەیئاوتەکان"),
    "listMessages": MessageLookupByLibrary.simpleMessage(
      "نامەکانی ئاگادارکردنەوە",
    ),
    "loadFail": MessageLookupByLibrary.simpleMessage(
      "بارکردن سەرکەوتوو نەبوو!",
    ),
    "loading": MessageLookupByLibrary.simpleMessage("باردەکات..."),
    "location": MessageLookupByLibrary.simpleMessage("شوێن"),
    "login": MessageLookupByLibrary.simpleMessage("چوونەدەرەوە"),
    "loginCanceled": MessageLookupByLibrary.simpleMessage(
      "چوونەژوورەوە هەڵوەشێندرایەوە",
    ),
    "loginErrorServiceProvider": m29,
    "loginToYourAccount": MessageLookupByLibrary.simpleMessage("چوونەژوورەوە"),
    "logout": MessageLookupByLibrary.simpleMessage("چوونەدەرەوە"),
    "manCollections": MessageLookupByLibrary.simpleMessage("کۆڵێکشنی پیاوان"),
    "manageStock": MessageLookupByLibrary.simpleMessage("بەڕێوە بردنى کۆگا"),
    "map": MessageLookupByLibrary.simpleMessage("نەخشە"),
    "maybeLater": MessageLookupByLibrary.simpleMessage(" دوایی"),
    "message": MessageLookupByLibrary.simpleMessage("نامە"),
    "minimumQuantityIs": MessageLookupByLibrary.simpleMessage("کەمترین بڕ"),
    "minutesAgo": m37,
    "more": MessageLookupByLibrary.simpleMessage("...زیاتر"),
    "mustSelectOneItem": MessageLookupByLibrary.simpleMessage(
      "پێویستە ١ ئایتم هەڵبژێریت",
    ),
    "myCart": MessageLookupByLibrary.simpleMessage("سەبەتەى کڕینەکانم"),
    "myPoints": MessageLookupByLibrary.simpleMessage("پۆینتەکانم"),
    "myProducts": MessageLookupByLibrary.simpleMessage("بەرهەمەکانم"),
    "myProductsEmpty": MessageLookupByLibrary.simpleMessage(
      "هیچ بەرهەمت نیە، هەوڵ بدە بەرهەمێك زیاد بکەیت؟",
    ),
    "myWishList": MessageLookupByLibrary.simpleMessage("لیستی دڵخوازەکانم"),
    "nItems": m41,
    "name": MessageLookupByLibrary.simpleMessage("ناو"),
    "nearbyPlaces": MessageLookupByLibrary.simpleMessage("شوێنی نزیك"),
    "newPassword": MessageLookupByLibrary.simpleMessage("تێپەڕوشەی نوێ"),
    "next": MessageLookupByLibrary.simpleMessage("دواتر"),
    "niceName": MessageLookupByLibrary.simpleMessage("ناوی جوان"),
    "no": MessageLookupByLibrary.simpleMessage("نەخێر"),
    "noBackHistoryItem": MessageLookupByLibrary.simpleMessage(
      "مێژووی بەردەست نیە",
    ),
    "noBlog": MessageLookupByLibrary.simpleMessage(
      "هیچ بڵاوکراوەیەک بەردەست نیە",
    ),
    "noData": MessageLookupByLibrary.simpleMessage("داتای زیاتر نیە"),
    "noFavoritesYet": MessageLookupByLibrary.simpleMessage("هیچ دڵخوازێکت نیە"),
    "noForwardHistoryItem": MessageLookupByLibrary.simpleMessage(
      "مێژووی بەردەست نیە",
    ),
    "noInternetConnection": MessageLookupByLibrary.simpleMessage(
      "ئینتەرنێت نیە!",
    ),
    "noOrders": MessageLookupByLibrary.simpleMessage("داواکردنت نەناردووە"),
    "noPost": MessageLookupByLibrary.simpleMessage(
      "ئۆۆۆ ، ئەم لاپەڕەیە لەوە دەچێت چیتر بوونی نەبێت !",
    ),
    "noProduct": MessageLookupByLibrary.simpleMessage("بەرهەم نیە"),
    "noResultFound": MessageLookupByLibrary.simpleMessage("هیچ نەدۆزرایەوە"),
    "noReviews": MessageLookupByLibrary.simpleMessage("هەڵسەنگاندن نیە"),
    "noThanks": MessageLookupByLibrary.simpleMessage("نا، سوپاس"),
    "notFound": MessageLookupByLibrary.simpleMessage("نەدۆزرایەوە"),
    "notifications": MessageLookupByLibrary.simpleMessage("ئاگادارکەروەکان"),
    "ofThisProduct": MessageLookupByLibrary.simpleMessage("ئەم بەرهەمە"),
    "ok": MessageLookupByLibrary.simpleMessage("باشە"),
    "onSale": MessageLookupByLibrary.simpleMessage("لە فرۆشتن"),
    "options": MessageLookupByLibrary.simpleMessage("هەڵبژاردنەکان"),
    "optionsTotal": m42,
    "or": MessageLookupByLibrary.simpleMessage("یان"),
    "orLoginWith": MessageLookupByLibrary.simpleMessage("یان خاڵبە بە"),
    "orderDate": MessageLookupByLibrary.simpleMessage("بەرواری داواکردن"),
    "orderDetail": MessageLookupByLibrary.simpleMessage("وردەکاری داواکردنەکە"),
    "orderHistory": MessageLookupByLibrary.simpleMessage("مێژووی داواکردن"),
    "orderId": MessageLookupByLibrary.simpleMessage("ژمارەى داواکارى: "),
    "orderNo": MessageLookupByLibrary.simpleMessage("کۆدی داواکردنەکەت."),
    "orderNotes": MessageLookupByLibrary.simpleMessage("تێبینیەکانی داواکردن"),
    "orderStatusCancelled": MessageLookupByLibrary.simpleMessage(
      "هەڵوەشێندراوە",
    ),
    "orderStatusCompleted": MessageLookupByLibrary.simpleMessage("تەواوکراوە"),
    "orderStatusFailed": MessageLookupByLibrary.simpleMessage(
      "سەرکەوتوو نەبوو",
    ),
    "orderStatusOnHold": MessageLookupByLibrary.simpleMessage("چاوەڕێیە"),
    "orderStatusPendingPayment": MessageLookupByLibrary.simpleMessage(
      "چاوەڕێی پارەدانە",
    ),
    "orderStatusProcessing": MessageLookupByLibrary.simpleMessage("بەڕێکردن"),
    "orderStatusRefunded": MessageLookupByLibrary.simpleMessage("گەڕێندراوە"),
    "orderSuccessMsg1": MessageLookupByLibrary.simpleMessage(
      "دەتوانیت باری ئەم ڕێکخستنە بپشکنی بە بەکارهێنانی تایبەتمەندی دۆخی گەیاندنمان. ئیمەیڵێکی دڵنیاکردنەوەی فەرمانت پێدەگات لەگەڵ وردەکاریەکانی فەرمانەکەت و لینکێک بۆ بەدواداچوونی پێشکەوتنەکەی.",
    ),
    "orderSuccessMsg2": MessageLookupByLibrary.simpleMessage(
      "دەتوانیت داخڵى هەژمارەکەت ببیت بە بەکارهێنانی ئیمەیڵ و تێپەڕوشە کە پێشتر پێناسە کراوە. لەسەر هەژمارەکەت. دەتوانیت زانیاری پرۆفایلەکەت بژار بکەیت، مێژووی مامەڵەکان بپشکنە، بەشداریکردن لە هەواڵنامە بژار بکەیت.",
    ),
    "orderSuccessTitle1": MessageLookupByLibrary.simpleMessage(
      "داواکردنەکەت بەسەرکەوتوویی نێردرا ",
    ),
    "orderSuccessTitle2": MessageLookupByLibrary.simpleMessage("هەژمارەکەت"),
    "orderTotal": MessageLookupByLibrary.simpleMessage("کۆى گشتى داواکاریی"),
    "outOfStock": MessageLookupByLibrary.simpleMessage("نەماوە"),
    "password": MessageLookupByLibrary.simpleMessage("تێپەڕوشە"),
    "payment": MessageLookupByLibrary.simpleMessage("پارەدان"),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("شێوازی پارەدان"),
    "paymentMethods": MessageLookupByLibrary.simpleMessage("شێوازی پارەدان"),
    "pending": MessageLookupByLibrary.simpleMessage("چاوەڕوانە"),
    "persian": MessageLookupByLibrary.simpleMessage("Persian"),
    "phone": MessageLookupByLibrary.simpleMessage("تەلەفۆن"),
    "phoneIsRequired": MessageLookupByLibrary.simpleMessage(
      " ژمارە تەلەفۆن داواکراوە",
    ),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("ژمارە تەلەفۆن"),
    "phoneNumberVerification": MessageLookupByLibrary.simpleMessage(
      "دووپاتکردنەوەی ژمارە تەلەفۆن",
    ),
    "placeMyOrder": MessageLookupByLibrary.simpleMessage("داواکردنەکەم بنێرە"),
    "pleaseAgreeTerms": MessageLookupByLibrary.simpleMessage(
      "تکایە بە مەرجەکان ڕازی بە",
    ),
    "pleaseCheckInternet": MessageLookupByLibrary.simpleMessage(
      "تکایە بزانە ئینتەرنێتت بەردەستە؟,",
    ),
    "pleaseFillCode": MessageLookupByLibrary.simpleMessage(
      "تکایە کۆدەکەت بنوسەوە",
    ),
    "pleaseFillUpAllCellsProperly": MessageLookupByLibrary.simpleMessage(
      "*تکایە هەموو بۆشاییەکان پڕبکەوە",
    ),
    "pleaseInput": MessageLookupByLibrary.simpleMessage(
      "تکایە هەموو بۆشاییەکان پڕبکەوە",
    ),
    "pleaseInputFillAllFields": MessageLookupByLibrary.simpleMessage(
      "تکایە بۆشاییە داواکراوەکان پڕ بکەوە",
    ),
    "pleaseSelectAllAttributes": MessageLookupByLibrary.simpleMessage(
      "تکایە بەربژارێک هەڵبژێرە بۆ هەر تایبەتمەندییەکی بەرهەمەکە",
    ),
    "pleaseSelectRequiredOptions": MessageLookupByLibrary.simpleMessage(
      "تکایە هەڵبژاردنە پێویستەکان دیارى بکە!",
    ),
    "point": MessageLookupByLibrary.simpleMessage("پۆینت"),
    "pointRewardMessage": MessageLookupByLibrary.simpleMessage(
      "یاسای داشکاندن هەیە بۆ جێبەجێکردنی خاڵەکانت بۆ سەبەتەى کڕین",
    ),
    "postProduct": MessageLookupByLibrary.simpleMessage("بەرهەم پۆست بکە"),
    "prev": MessageLookupByLibrary.simpleMessage("پێشتر"),
    "prices": MessageLookupByLibrary.simpleMessage("نرخەکان"),
    "privacyAndTerm": MessageLookupByLibrary.simpleMessage(
      "مەرج و یاسای بەکارهێنان",
    ),
    "product": MessageLookupByLibrary.simpleMessage("بەرهەم"),
    "productAdded": MessageLookupByLibrary.simpleMessage("کاڵا زیادکرا"),
    "productName": MessageLookupByLibrary.simpleMessage("ناوی بەرهەم"),
    "productRating": MessageLookupByLibrary.simpleMessage("هەڵسەنگاندنەکەت"),
    "productType": MessageLookupByLibrary.simpleMessage("جۆری بەرهەم"),
    "products": MessageLookupByLibrary.simpleMessage("بەرهەمەکان"),
    "pullToLoadMore": MessageLookupByLibrary.simpleMessage(
      "ڕاکێشە بۆ بارکردنی زیاتر",
    ),
    "qty": MessageLookupByLibrary.simpleMessage("بڕ"),
    "rate": MessageLookupByLibrary.simpleMessage("هەڵسەنگاندن"),
    "rateTheApp": MessageLookupByLibrary.simpleMessage(
      "هەڵسەنگاندنى ئەپڵیکەشن",
    ),
    "rateThisApp": MessageLookupByLibrary.simpleMessage(
      "هەڵسەنگاندنی ئەم ئەپە",
    ),
    "rateThisAppDescription": MessageLookupByLibrary.simpleMessage(
      "ئەگەر ئەم ئەپەت بەدڵ بوو، تکایە هەڵسەنگاندن ئەنجام بدە. بەڕاستی یارمەتیمان دەدات و نابێت یەک خولەک زیاترت پێ بچێت",
    ),
    "rating": MessageLookupByLibrary.simpleMessage("هەڵسەنگاند"),
    "ratingFirst": MessageLookupByLibrary.simpleMessage(
      "تکایە هەڵسەنگاندن بکە پێش ناردنی بۆچون!",
    ),
    "readReviews": MessageLookupByLibrary.simpleMessage("هەڵسەنگاندنەکان"),
    "recent": MessageLookupByLibrary.simpleMessage("نوێترینیان"),
    "recentSearches": MessageLookupByLibrary.simpleMessage("مێژوو"),
    "recentView": MessageLookupByLibrary.simpleMessage("دوایین بینینەکانت"),
    "refresh": MessageLookupByLibrary.simpleMessage("نوێکردنەوە"),
    "refundRequest": MessageLookupByLibrary.simpleMessage(
      "داواکاری گەڕاندنەوە",
    ),
    "refunds": MessageLookupByLibrary.simpleMessage("قەرەبووکردنەوەکان"),
    "registerAsVendor": MessageLookupByLibrary.simpleMessage(
      "تۆمارکردن وەك فرۆشیار",
    ),
    "regularPrice": MessageLookupByLibrary.simpleMessage("نرخی ئاسایی"),
    "releaseToLoadMore": MessageLookupByLibrary.simpleMessage(
      "بەردە بۆ بارکردنی زیاتر",
    ),
    "remove": MessageLookupByLibrary.simpleMessage("سڕینەوە"),
    "removeFromWishList": MessageLookupByLibrary.simpleMessage(
      "سڕینەوە لە لیستی دڵخوازەکاندا",
    ),
    "requestBooking": MessageLookupByLibrary.simpleMessage("شوێنگرتن داوابکە"),
    "resend": MessageLookupByLibrary.simpleMessage(" دووبارە بینێرەوە"),
    "reset": MessageLookupByLibrary.simpleMessage("ڕیسێت کردن"),
    "resetPassword": MessageLookupByLibrary.simpleMessage(
      "ڕیسێتکردنی تێپەڕوشە",
    ),
    "resetYourPassword": MessageLookupByLibrary.simpleMessage(
      "ڕیسێتکردنی تێپەڕوشە",
    ),
    "review": MessageLookupByLibrary.simpleMessage("پێشبینین"),
    "reviewApproval": MessageLookupByLibrary.simpleMessage(
      "پێداچوونەوە ى پەسەندکردن",
    ),
    "reviewPendingApproval": MessageLookupByLibrary.simpleMessage(
      "هەڵسەنگاندنەکەت بەسەرکەوتووی نێردرا و لە چاوەڕوانى پەسەندکردندایە!",
    ),
    "reviewSent": MessageLookupByLibrary.simpleMessage(
      "هەڵسەنگاندنەکەت بەسەرکەوتووی نێردرا!",
    ),
    "reviews": MessageLookupByLibrary.simpleMessage("هەڵسەنگاندنەکان"),
    "romanian": MessageLookupByLibrary.simpleMessage("ڕۆمانی"),
    "russian": MessageLookupByLibrary.simpleMessage("ڕوسی"),
    "sale": m48,
    "salePrice": MessageLookupByLibrary.simpleMessage("نرخی داشکاندن"),
    "saveAddress": MessageLookupByLibrary.simpleMessage("ناونیشان هەڵبگرە"),
    "saveAddressSuccess": MessageLookupByLibrary.simpleMessage(
      "ناونیشان پشتر لە لۆکال زیادکراوە",
    ),
    "saveForLater": MessageLookupByLibrary.simpleMessage("تۆمارکردن بۆ دوایی"),
    "saveToWishList": MessageLookupByLibrary.simpleMessage(
      "بخەرە ناو لیستی دڵخوازەکانم",
    ),
    "search": MessageLookupByLibrary.simpleMessage("گەڕان"),
    "searchForItems": MessageLookupByLibrary.simpleMessage(
      "گەڕان بەدوای ئایتم",
    ),
    "searchInput": MessageLookupByLibrary.simpleMessage(
      "تکایە بۆشایی گەڕان بەکاربهێنە",
    ),
    "searchOrderId": MessageLookupByLibrary.simpleMessage(
      "بگەڕێ بۆ ژمارەى داواکارى...",
    ),
    "searchPlace": MessageLookupByLibrary.simpleMessage("گەڕان بەدوای شوێن"),
    "searchingAddress": MessageLookupByLibrary.simpleMessage(
      "گەران بەدوای ناونیشان",
    ),
    "secondsAgo": m52,
    "seeAll": MessageLookupByLibrary.simpleMessage("پێشاندانی هەموو"),
    "selectAddress": MessageLookupByLibrary.simpleMessage("ناونیشان هەڵبژێرە"),
    "selectFileCancelled": MessageLookupByLibrary.simpleMessage(
      "فایلى هەڵبژێردراو پوچەڵ کرایەوە!",
    ),
    "selectImage": MessageLookupByLibrary.simpleMessage("هەڵبژاردنى وێنە"),
    "selectTheColor": MessageLookupByLibrary.simpleMessage("ڕەنگ هەڵبژێرە"),
    "selectThePoint": MessageLookupByLibrary.simpleMessage("خاڵ دیاریبکە"),
    "selectTheQuantity": MessageLookupByLibrary.simpleMessage(
      "چەندێتی دیاری بکە",
    ),
    "selectTheSize": MessageLookupByLibrary.simpleMessage("قەبارە هەڵبژێرە"),
    "send": MessageLookupByLibrary.simpleMessage("ناردن"),
    "sendSMSCode": MessageLookupByLibrary.simpleMessage("کۆدى چالاککردن بنێرە"),
    "settings": MessageLookupByLibrary.simpleMessage("ڕێکخستنەکان"),
    "share": MessageLookupByLibrary.simpleMessage("شەیرکردن"),
    "shipping": MessageLookupByLibrary.simpleMessage("گەیاندن"),
    "shippingAddress": MessageLookupByLibrary.simpleMessage(
      "ناونیشانی گەیاندن",
    ),
    "shippingMethod": MessageLookupByLibrary.simpleMessage("شێوازی گەیاندن"),
    "shop": MessageLookupByLibrary.simpleMessage("فرۆشگا"),
    "shopOrders": MessageLookupByLibrary.simpleMessage("داواکردنەکان"),
    "shoppingCartItems": m53,
    "shortDescription": MessageLookupByLibrary.simpleMessage(
      "پوختەى ڕوونکردنەوە",
    ),
    "showAllMyOrdered": MessageLookupByLibrary.simpleMessage(
      "هەموو داواکردنەکانم پێشان بدە",
    ),
    "showDetails": MessageLookupByLibrary.simpleMessage(
      "پیشاندانى وردەکارییەکان",
    ),
    "showGallery": MessageLookupByLibrary.simpleMessage("پێشاندانی گەلەری"),
    "signIn": MessageLookupByLibrary.simpleMessage("چوونەژوورەوە"),
    "signInWithEmail": MessageLookupByLibrary.simpleMessage(
      "چوونەژوورەوە بە ئیمەیل",
    ),
    "signUp": MessageLookupByLibrary.simpleMessage("خۆتۆمارکردن"),
    "signup": MessageLookupByLibrary.simpleMessage("خۆتۆمارکردن"),
    "size": MessageLookupByLibrary.simpleMessage("قەبارە"),
    "skip": MessageLookupByLibrary.simpleMessage("پەڕاندن"),
    "sku": MessageLookupByLibrary.simpleMessage("SKU"),
    "sold": m54,
    "soldBy": MessageLookupByLibrary.simpleMessage("فرۆشراوە لەلایان"),
    "spanish": MessageLookupByLibrary.simpleMessage("ئیسپانى"),
    "startShopping": MessageLookupByLibrary.simpleMessage("بازاڕی بکە"),
    "stateIsRequired": MessageLookupByLibrary.simpleMessage(
      " پارێزگا داواکراوە",
    ),
    "stateProvince": MessageLookupByLibrary.simpleMessage("پاڕێزگا"),
    "status": MessageLookupByLibrary.simpleMessage("دۆخ"),
    "stock": MessageLookupByLibrary.simpleMessage("گۆکا"),
    "stockQuantity": MessageLookupByLibrary.simpleMessage("بڕی ناو کۆگا"),
    "stores": MessageLookupByLibrary.simpleMessage("فرۆشگاکان"),
    "streetIsRequired": MessageLookupByLibrary.simpleMessage(
      " ناونیشان داواکراوە",
    ),
    "streetName": MessageLookupByLibrary.simpleMessage("ناوی شەقام"),
    "streetNameApartment": MessageLookupByLibrary.simpleMessage("شوقە"),
    "streetNameBlock": MessageLookupByLibrary.simpleMessage("بلۆك"),
    "subtotal": MessageLookupByLibrary.simpleMessage("نرخی پێش کۆتایی"),
    "tags": MessageLookupByLibrary.simpleMessage("تاگ"),
    "takePicture": MessageLookupByLibrary.simpleMessage("وێنەیەک بگرە"),
    "tapSelectLocation": MessageLookupByLibrary.simpleMessage(
      "کرتە بکە بۆ هەڵبژاردنی شوێن",
    ),
    "tax": MessageLookupByLibrary.simpleMessage("باج"),
    "thailand": MessageLookupByLibrary.simpleMessage("تایلەندى"),
    "thisFeatureDoesNotSupportTheCurrentLanguage":
        MessageLookupByLibrary.simpleMessage(
          "ئەم تایبەتمەندییە پشتگیری ئەم زمانە ناکات",
        ),
    "thisPlatformNotSupportWebview": MessageLookupByLibrary.simpleMessage(
      "ئەم پلاتفۆڕمە پشتگیری وێبڤیو ناکات",
    ),
    "tickets": MessageLookupByLibrary.simpleMessage("تکتەکان"),
    "total": MessageLookupByLibrary.simpleMessage("نرخی کۆتایی"),
    "totalCartValue": MessageLookupByLibrary.simpleMessage(
      "پێویستە بەهای کۆی داواکردنەکان لانیکەم",
    ),
    "totalProducts": m56,
    "totalTax": MessageLookupByLibrary.simpleMessage("کۆی باج"),
    "trackingNumberIs": MessageLookupByLibrary.simpleMessage("کۆدی بەدواداچون"),
    "trackingPage": MessageLookupByLibrary.simpleMessage("لاپەڕەی بەدواداچون"),
    "transactionCancelled": MessageLookupByLibrary.simpleMessage(
      "مامەڵە هەڵوەشێنرایەوە",
    ),
    "turkish": MessageLookupByLibrary.simpleMessage("تورکی"),
    "typeYourMessage": MessageLookupByLibrary.simpleMessage(
      "پەیامەکەت لێرە بنووسە...",
    ),
    "unavailable": MessageLookupByLibrary.simpleMessage("بەردەست نیە"),
    "undo": MessageLookupByLibrary.simpleMessage("پاشگەزبونەوە"),
    "update": MessageLookupByLibrary.simpleMessage("نوێکردنەوە"),
    "updateInfo": MessageLookupByLibrary.simpleMessage(
      "نوێکردنەوى زانیارییەکان",
    ),
    "updateStatus": MessageLookupByLibrary.simpleMessage("نوێکردنەوەى دۆخ"),
    "updateUserInfor": MessageLookupByLibrary.simpleMessage(
      "نوێکردنەوەی پڕۆفایل",
    ),
    "uploadFile": MessageLookupByLibrary.simpleMessage("بارکردنى فایل"),
    "uploadProduct": MessageLookupByLibrary.simpleMessage("بارکردنى بەرهەم"),
    "uploading": MessageLookupByLibrary.simpleMessage("باردەکات"),
    "url": MessageLookupByLibrary.simpleMessage("URL"),
    "useNow": MessageLookupByLibrary.simpleMessage("ئێستا بەکاربهێنە"),
    "username": MessageLookupByLibrary.simpleMessage("ناوی بەکارهێنەر"),
    "validUntilDate": m61,
    "vendorAdmin": MessageLookupByLibrary.simpleMessage(
      "بەڕێوەبەرایەتی فرۆشیار",
    ),
    "verifySMSCode": MessageLookupByLibrary.simpleMessage("پشتڕاستکردنەوە"),
    "video": MessageLookupByLibrary.simpleMessage("ڤیدیۆ"),
    "vietnamese": MessageLookupByLibrary.simpleMessage("ڤێتنامی"),
    "visitStore": MessageLookupByLibrary.simpleMessage("سەرەدانی فرۆشگا بکە"),
    "waitForLoad": MessageLookupByLibrary.simpleMessage(
      "چاوەڕێی دەرکەوتنی وێنە دەکرێت",
    ),
    "waitForPost": MessageLookupByLibrary.simpleMessage(
      "چاوەڕێ دەکرێت بۆ پۆستکردنی بەرهەمەکە",
    ),
    "warning": m64,
    "weFoundProducts": m66,
    "welcome": MessageLookupByLibrary.simpleMessage("بەخێرهاتی"),
    "womanCollections": MessageLookupByLibrary.simpleMessage(
      "کۆڵێکشنی ئافرەتان",
    ),
    "writeComment": MessageLookupByLibrary.simpleMessage("بۆچونەکەت بنوسە"),
    "writeYourNote": MessageLookupByLibrary.simpleMessage("تێبینیەکانت بنوسە"),
    "yes": MessageLookupByLibrary.simpleMessage("بەڵێ"),
    "youCanOnlyPurchase": MessageLookupByLibrary.simpleMessage(
      "تەنیا دەتوانی کڕین ئەنجام بدەیت",
    ),
    "youHaveBeenSaveAddressYourLocal": MessageLookupByLibrary.simpleMessage(
      "ناونیشانەکەت لە لۆکال زیادکردووە",
    ),
    "youHavePoints": m73,
    "youMightAlsoLike": MessageLookupByLibrary.simpleMessage(
      "دەکرێت حەزت لەمانەش بێت",
    ),
    "yourAddressExistYourLocal": MessageLookupByLibrary.simpleMessage(
      "ناونیشانەکەت پێشتر بەردەستە",
    ),
    "yourBagIsEmpty": MessageLookupByLibrary.simpleMessage(
      "سەبەتەى کڕینەکانت بەتاڵە",
    ),
    "yourEarningsThisMonth": MessageLookupByLibrary.simpleMessage(
      "پەیداکردنەکانت لەم مانگەدا",
    ),
    "yourNote": MessageLookupByLibrary.simpleMessage("تێبینیەکانت"),
    "yourOrders": MessageLookupByLibrary.simpleMessage("داواکارییەکانت"),
    "yourUsernameEmail": MessageLookupByLibrary.simpleMessage(
      "ناوی بەکارهێنەر یان ئیمەیڵەکەت",
    ),
    "zipCode": MessageLookupByLibrary.simpleMessage("کۆدی پۆستە"),
    "zipCodeIsRequired": MessageLookupByLibrary.simpleMessage(
      " کۆدی پۆستە داواکراوە",
    ),
  };
}
