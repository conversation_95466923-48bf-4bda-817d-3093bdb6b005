// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a uk locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'uk';

  static String m0(x) => "Активний протягом ${x}";

  static String m1(amount) => "Додати ${amount} балів";

  static String m2(attribute) => "Будь-який ${attribute}";

  static String m3(point) => "Доступні бали: ${point}";

  static String m4(name) => "Успішно зроблено ставку на \'${name}\'";

  static String m5(state) => "Адаптер Bluetooth ${state}";

  static String m6(amount) => "Купити зараз за ${amount}";

  static String m7(author) => "Автор: ${author}";

  static String m8(fieldName) => "${fieldName} не може бути порожнім";

  static String m9(fieldName) =>
      "${fieldName} не може бути коротшим за 3 символи";

  static String m10(currency) => "Валюту змінено на ${currency}";

  static String m11(number) => "Залишилось символів: ${number}";

  static String m12(priceRate, pointRate) =>
      "${priceRate} = ${pointRate} балів";

  static String m13(count) => "${count} товар";

  static String m14(count) => "${count} товарів";

  static String m15(count) => "${count} товар";

  static String m16(count) => "${count} товарів";

  static String m17(country) => "Країна ${country} не підтримується";

  static String m18(currency) => "${currency} не підтримується";

  static String m19(day) => "${day} днів тому";

  static String m20(total) => "~${total} км";

  static String m21(timeLeft) => "Закінчується через ${timeLeft}";

  static String m22(captcha) => "Введіть ${captcha} для підтвердження:";

  static String m23(message) => "Помилка: ${message}";

  static String m24(message) => "Помилка: ${message}";

  static String m25(time) => "Закінчується через ${time}";

  static String m26(total) => ">${total} км";

  static String m27(hour) => "${hour} годин тому";

  static String m28(currentBalance) =>
      "У вашому гаманці залишилось лише ${currentBalance}";

  static String m29(message) =>
      "Виникла проблема з програмою під час запиту даних. Зверніться до адміністратора для вирішення проблем: ${message}";

  static String m30(currency, amount) =>
      "Максимальна сума для цього способу оплати становить ${currency} ${amount}";

  static String m31(size) => "Максимальний розмір файлу: ${size} МБ";

  static String m32(name, formattedPrice) => "${name}: ${formattedPrice}";

  static String m33(currency, amount) =>
      "Мінімальна сума для цього способу оплати становить ${currency} ${amount}";

  static String m34(storeName, minOrderAmount) =>
      "Мінімальна сума замовлення для ${storeName} становить ${minOrderAmount}. Будь ласка, додайте ще кілька товарів з цього магазину!";

  static String m35(amount) =>
      "Цей купон вимагає мінімальної покупки на суму ${amount}.";

  static String m36(value) => "Мінімальна транзакція: ${value}";

  static String m37(minute) => "${minute} хвилин тому";

  static String m38(month) => "${month} місяців тому";

  static String m39(store) => "Більше від ${store}";

  static String m40(number) => "Необхідно купувати групами по ${number}";

  static String m41(itemCount) => "${itemCount} товарів";

  static String m42(price) => "Всього опцій: ${price}";

  static String m43(amount) => "Оплатити ${amount}";

  static String m44(name) => "${name} успішно додано до кошика";

  static String m45(total) => "Кількість: ${total}";

  static String m46(name) => "Отримано кошти від ${name}";

  static String m47(count) =>
      "Бажаєте видалити ${count} товарів зі списку бажань?";

  static String m48(percent) => "Знижка ${percent}%";

  static String m49(keyword) => "Результати пошуку для: \"${keyword}\"";

  static String m50(keyword, count) => "${keyword} (${count} товар)";

  static String m51(keyword, count) => "${keyword} (${count} товарів)";

  static String m52(second) => "${second} секунд тому";

  static String m53(totalCartQuantity) => "Кошик, ${totalCartQuantity} товарів";

  static String m54(numberOfUnitsSold) => "Продано: ${numberOfUnitsSold}";

  static String m55(fieldName) => "Поле ${fieldName} обов\'язкове";

  static String m56(total) => "${total} товарів";

  static String m57(name) => "Переказати кошти ${name}";

  static String m58(amount) => "Використайте ${amount} балів";

  static String m59(maxPointDiscount, maxPriceDiscount) =>
      "Використайте максимум ${maxPointDiscount} балів, щоб отримати знижку ${maxPriceDiscount} на це замовлення!";

  static String m60(time) => "Дійсний до: ${time}";

  static String m61(date) => "Дійсний до ${date}";

  static String m62(number) => "Версія ${number}";

  static String m63(balance) => "Баланс гаманця: ${balance}";

  static String m64(message) => "Попередження: ${message}";

  static String m65(defaultCurrency) =>
      "Вибрана валюта наразі недоступна для функції гаманця. Будь ласка, змініть її на ${defaultCurrency}";

  static String m66(length) => "Знайдено ${length} товарів";

  static String m67(week) => "Тиждень ${week}";

  static String m68(name) => "Ласкаво просимо, ${name}";

  static String m69(year) => "${year} років тому";

  static String m70(count) => "Ви обираєте ${count} товарів";

  static String m71(total) => "Вам призначено замовлення №${total}";

  static String m72(type) => "Ви пройшли ${type}";

  static String m73(point) => "У вас є ${point} балів";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "aboutUs": MessageLookupByLibrary.simpleMessage("Про нас"),
    "account": MessageLookupByLibrary.simpleMessage("Обліковий запис"),
    "accountApprovalTitle": MessageLookupByLibrary.simpleMessage(
      "На затвердженні",
    ),
    "accountDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "Видалення вашого облікового запису видаляє особисту інформацію з нашої бази даних. Ваша електронна адреса буде зарезервована назавжди, і її не можна буде використати повторно для реєстрації нового облікового запису",
    ),
    "accountIsPendingApproval": MessageLookupByLibrary.simpleMessage(
      "Обліковий запис очікує на затвердження",
    ),
    "accountNumber": MessageLookupByLibrary.simpleMessage("Номер рахунку"),
    "accountSetup": MessageLookupByLibrary.simpleMessage(
      "Налаштування облікового запису",
    ),
    "active": MessageLookupByLibrary.simpleMessage("Активний"),
    "activeFor": m0,
    "activeLongAgo": MessageLookupByLibrary.simpleMessage("Активний давно"),
    "activeNow": MessageLookupByLibrary.simpleMessage("Активний зараз"),
    "add": MessageLookupByLibrary.simpleMessage("Додати"),
    "addAName": MessageLookupByLibrary.simpleMessage("Додати ім\'я"),
    "addANewPost": MessageLookupByLibrary.simpleMessage("Додати новий допис"),
    "addASlug": MessageLookupByLibrary.simpleMessage("Додати ідентифікатор"),
    "addAmountPoints": m1,
    "addAnAttr": MessageLookupByLibrary.simpleMessage("Додати атрибут"),
    "addListing": MessageLookupByLibrary.simpleMessage("Додати оголошення"),
    "addMessage": MessageLookupByLibrary.simpleMessage("Додати повідомлення"),
    "addNew": MessageLookupByLibrary.simpleMessage("Додати новий"),
    "addNewAddress": MessageLookupByLibrary.simpleMessage("Додати нову адресу"),
    "addNewBlog": MessageLookupByLibrary.simpleMessage("Додати новий блог"),
    "addNewPost": MessageLookupByLibrary.simpleMessage("Створити новий допис"),
    "addOrUsePointsSuccessMsg": MessageLookupByLibrary.simpleMessage(
      "Щиро вітаю! Бали успішно додано або використано.",
    ),
    "addPoint": MessageLookupByLibrary.simpleMessage("Додати точку"),
    "addPoints": MessageLookupByLibrary.simpleMessage("Додати бали"),
    "addProduct": MessageLookupByLibrary.simpleMessage("Додати товар"),
    "addToCart": MessageLookupByLibrary.simpleMessage("Додати до кошика"),
    "addToCartMaximum": MessageLookupByLibrary.simpleMessage(
      "Перевищено максимальну кількість",
    ),
    "addToCartSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Успішно додано до кошика",
    ),
    "addToOrder": MessageLookupByLibrary.simpleMessage("Додати до замовлення"),
    "addToQuoteRequest": MessageLookupByLibrary.simpleMessage(
      "Додати до запиту на цінову пропозицію",
    ),
    "addToWishlist": MessageLookupByLibrary.simpleMessage(
      "Додати до списку бажань",
    ),
    "added": MessageLookupByLibrary.simpleMessage("Додано"),
    "addedSuccessfully": MessageLookupByLibrary.simpleMessage("Успішно додано"),
    "addingYourImage": MessageLookupByLibrary.simpleMessage(
      "Додавання зображення",
    ),
    "additionalInformation": MessageLookupByLibrary.simpleMessage(
      "Додаткова інформація",
    ),
    "additionalServices": MessageLookupByLibrary.simpleMessage(
      "Додаткові послуги",
    ),
    "address": MessageLookupByLibrary.simpleMessage("Адреса"),
    "adults": MessageLookupByLibrary.simpleMessage("Дорослі"),
    "advanceAmount": MessageLookupByLibrary.simpleMessage("Сума авансу"),
    "advancePayment": MessageLookupByLibrary.simpleMessage("Попередня оплата"),
    "afternoon": MessageLookupByLibrary.simpleMessage("Після обіду"),
    "agree": MessageLookupByLibrary.simpleMessage("Погоджуюсь"),
    "agreeWithPrivacy": MessageLookupByLibrary.simpleMessage(
      "Конфіденційність та умови",
    ),
    "albanian": MessageLookupByLibrary.simpleMessage("Албанська"),
    "all": MessageLookupByLibrary.simpleMessage("Всі"),
    "allBrands": MessageLookupByLibrary.simpleMessage("Усі бренди"),
    "allDeliveryOrders": MessageLookupByLibrary.simpleMessage("Всі замовлення"),
    "allOrders": MessageLookupByLibrary.simpleMessage("Останні продажі"),
    "allProducts": MessageLookupByLibrary.simpleMessage("Усі товари"),
    "allow": MessageLookupByLibrary.simpleMessage("Дозволити"),
    "allowCameraAccess": MessageLookupByLibrary.simpleMessage(
      "Дозволити доступ до камери?",
    ),
    "almostSoldOut": MessageLookupByLibrary.simpleMessage("Майже розпродано"),
    "amazing": MessageLookupByLibrary.simpleMessage("Чудово"),
    "amount": MessageLookupByLibrary.simpleMessage("Сума"),
    "anyAttr": m2,
    "appTrackingRequest": MessageLookupByLibrary.simpleMessage(
      "Цей ідентифікатор використовуватиметься для надання вам персоналізованої реклами. \n«Скасувати» обмежить можливість рекламної мережі надавати вам релевантну рекламу, але не зменшить кількість реклами, яку ви отримуєте.\nОскільки пристрій обмежено, відстеження вимкнено, і система не може показати діалогове вікно запиту. «Відкрити налаштування» та дозволити програмі відстежувати вашу активність у програмах і на веб-сайтах інших компаній?",
    ),
    "appTrackingTransparency": MessageLookupByLibrary.simpleMessage(
      "Прозорість відстеження програми",
    ),
    "appearance": MessageLookupByLibrary.simpleMessage("Зовнішній вигляд"),
    "apply": MessageLookupByLibrary.simpleMessage("Застосувати"),
    "appointmentStartInvalidDay": MessageLookupByLibrary.simpleMessage(
      "Вибачте, зустрічі не можуть розпочатися цього дня.",
    ),
    "approve": MessageLookupByLibrary.simpleMessage("Затвердити"),
    "approved": MessageLookupByLibrary.simpleMessage("Затверджено"),
    "approvedRequests": MessageLookupByLibrary.simpleMessage("Схвалені запити"),
    "arabic": MessageLookupByLibrary.simpleMessage("Арабська"),
    "areYouSure": MessageLookupByLibrary.simpleMessage("Ви впевнені?"),
    "areYouSureDeleteAccount": MessageLookupByLibrary.simpleMessage(
      "Ви впевнені, що хочете видалити свій обліковий запис?",
    ),
    "areYouSureLogOut": MessageLookupByLibrary.simpleMessage(
      "Ви впевнені, що хочете вийти?",
    ),
    "areYouWantToExit": MessageLookupByLibrary.simpleMessage(
      "Ви дійсно бажаєте вийти?",
    ),
    "assigned": MessageLookupByLibrary.simpleMessage("Призначено"),
    "atLeastThreeCharacters": MessageLookupByLibrary.simpleMessage(
      "Щонайменше 3 символи...",
    ),
    "attribute": MessageLookupByLibrary.simpleMessage("Атрибут"),
    "attributeAlreadyExists": MessageLookupByLibrary.simpleMessage(
      "Атрибут вже існує",
    ),
    "attributes": MessageLookupByLibrary.simpleMessage("Атрибути"),
    "auction": MessageLookupByLibrary.simpleMessage("Аукціон"),
    "auctionDates": MessageLookupByLibrary.simpleMessage("Дати аукціонів"),
    "auctionEnded": MessageLookupByLibrary.simpleMessage("Аукціон завершено"),
    "auctionEnds": MessageLookupByLibrary.simpleMessage("Аукціон завершується"),
    "auctionHistory": MessageLookupByLibrary.simpleMessage("Історія аукціонів"),
    "auctionStarts": MessageLookupByLibrary.simpleMessage("Початок аукціону"),
    "auctionStartsIn": MessageLookupByLibrary.simpleMessage(
      "Аукціон починається через",
    ),
    "auctionType": MessageLookupByLibrary.simpleMessage("Тип аукціону"),
    "audioDetected": MessageLookupByLibrary.simpleMessage(
      "Виявлено аудіофайл(и). Бажаєте додати до аудіоплеєра?",
    ),
    "availability": MessageLookupByLibrary.simpleMessage("Наявність"),
    "availabilityProduct": MessageLookupByLibrary.simpleMessage("Наявність: "),
    "availableForTiers": MessageLookupByLibrary.simpleMessage(
      "Доступно для рівнів",
    ),
    "availablePoints": m3,
    "averageRating": MessageLookupByLibrary.simpleMessage("Середній рейтинг"),
    "b2bKingRegisterMsg": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, зв\'яжіться з адміністратором для підтвердження реєстрації.",
    ),
    "back": MessageLookupByLibrary.simpleMessage("Назад"),
    "backOrder": MessageLookupByLibrary.simpleMessage("Зворотне замовлення"),
    "backToShop": MessageLookupByLibrary.simpleMessage(
      "Повернутися до магазину",
    ),
    "backToWallet": MessageLookupByLibrary.simpleMessage("Назад до гаманця"),
    "bagsCollections": MessageLookupByLibrary.simpleMessage("Колекції сумок"),
    "balance": MessageLookupByLibrary.simpleMessage("Баланс"),
    "bank": MessageLookupByLibrary.simpleMessage("Банк"),
    "bannerListType": MessageLookupByLibrary.simpleMessage(
      "Тип банерного списку",
    ),
    "bannerType": MessageLookupByLibrary.simpleMessage("Тип банера"),
    "bannerYoutubeURL": MessageLookupByLibrary.simpleMessage(
      "URL-адреса банера Youtube",
    ),
    "basicInformation": MessageLookupByLibrary.simpleMessage(
      "Основна інформація",
    ),
    "becomeADelivery": MessageLookupByLibrary.simpleMessage(
      "Станьте доставкою",
    ),
    "becomeAVendor": MessageLookupByLibrary.simpleMessage("Стати продавцем"),
    "becomeAVendorDelivery": MessageLookupByLibrary.simpleMessage(
      "Станьте постачальником/доставником",
    ),
    "benefits": MessageLookupByLibrary.simpleMessage("Переваги"),
    "bengali": MessageLookupByLibrary.simpleMessage("Бенгальська"),
    "bid": MessageLookupByLibrary.simpleMessage("Ставка"),
    "bidIncrement": MessageLookupByLibrary.simpleMessage("Збільшення ставки"),
    "bidSuccessMessage": m4,
    "billingAddress": MessageLookupByLibrary.simpleMessage("Адреса оплати"),
    "bleHasNotBeenEnabled": MessageLookupByLibrary.simpleMessage(
      "Bluetooth не увімкнено",
    ),
    "bleState": m5,
    "block": MessageLookupByLibrary.simpleMessage("Заблокувати"),
    "blockUser": MessageLookupByLibrary.simpleMessage(
      "Заблокувати користувача",
    ),
    "blog": MessageLookupByLibrary.simpleMessage("Блог"),
    "booked": MessageLookupByLibrary.simpleMessage("Вже заброньовано"),
    "booking": MessageLookupByLibrary.simpleMessage("Бронювання"),
    "bookingCancelled": MessageLookupByLibrary.simpleMessage(
      "Бронювання скасовано",
    ),
    "bookingConfirm": MessageLookupByLibrary.simpleMessage("Підтверджено"),
    "bookingError": MessageLookupByLibrary.simpleMessage(
      "Щось пішло не так. Спробуйте пізніше.",
    ),
    "bookingHistory": MessageLookupByLibrary.simpleMessage("Історія бронювань"),
    "bookingNow": MessageLookupByLibrary.simpleMessage("Забронювати зараз"),
    "bookingSuccess": MessageLookupByLibrary.simpleMessage(
      "Успішно заброньовано",
    ),
    "bookingSummary": MessageLookupByLibrary.simpleMessage(
      "Підсумок бронювання",
    ),
    "bookingUnavailable": MessageLookupByLibrary.simpleMessage(
      "Бронювання недоступне",
    ),
    "bosnian": MessageLookupByLibrary.simpleMessage("Боснійська"),
    "branch": MessageLookupByLibrary.simpleMessage("Філія"),
    "branchChangeWarning": MessageLookupByLibrary.simpleMessage(
      "На жаль, через зміну регіону ваш кошик буде очищено. Ми готові допомогти вам, якщо потрібна допомога",
    ),
    "brand": MessageLookupByLibrary.simpleMessage("Бренд"),
    "brands": MessageLookupByLibrary.simpleMessage("Бренди"),
    "brazil": MessageLookupByLibrary.simpleMessage("Португальська"),
    "bronze": MessageLookupByLibrary.simpleMessage("Бронза"),
    "bronzePriority": MessageLookupByLibrary.simpleMessage(
      "Бронзовий пріоритет",
    ),
    "burmese": MessageLookupByLibrary.simpleMessage("Бірманська"),
    "buyItNowPrice": MessageLookupByLibrary.simpleMessage(
      "Ціна «Купити зараз»",
    ),
    "buyNow": MessageLookupByLibrary.simpleMessage("Купити зараз"),
    "buyNowFor": m6,
    "by": MessageLookupByLibrary.simpleMessage("від"),
    "byAppointmentOnly": MessageLookupByLibrary.simpleMessage(
      "Тільки за записом",
    ),
    "byAuthor": m7,
    "byBrand": MessageLookupByLibrary.simpleMessage("За брендом"),
    "byCategory": MessageLookupByLibrary.simpleMessage("За категорією"),
    "byPrice": MessageLookupByLibrary.simpleMessage("За ціною"),
    "bySignup": MessageLookupByLibrary.simpleMessage(
      "Реєструючись, ви погоджуєтеся з нашими",
    ),
    "byTag": MessageLookupByLibrary.simpleMessage("За тегом"),
    "call": MessageLookupByLibrary.simpleMessage("Зателефонувати"),
    "callTo": MessageLookupByLibrary.simpleMessage("Зателефонувати"),
    "callToVendor": MessageLookupByLibrary.simpleMessage(
      "Зателефонувати продавцю",
    ),
    "canNotCreateOrder": MessageLookupByLibrary.simpleMessage(
      "Неможливо створити замовлення",
    ),
    "canNotCreateUser": MessageLookupByLibrary.simpleMessage(
      "Неможливо створити користувача",
    ),
    "canNotGetPayments": MessageLookupByLibrary.simpleMessage(
      "Неможливо отримати способи оплати",
    ),
    "canNotGetShipping": MessageLookupByLibrary.simpleMessage(
      "Неможливо отримати способи доставки",
    ),
    "canNotGetToken": MessageLookupByLibrary.simpleMessage(
      "Не вдається отримати інформацію про токен",
    ),
    "canNotLaunch": MessageLookupByLibrary.simpleMessage(
      "Не вдається запустити програму. Перевірте налаштування в config.dart",
    ),
    "canNotLoadThisLink": MessageLookupByLibrary.simpleMessage(
      "Не вдається завантажити це посилання",
    ),
    "canNotPlayVideo": MessageLookupByLibrary.simpleMessage(
      "На жаль, це відео неможливо відтворити",
    ),
    "canNotSaveOrder": MessageLookupByLibrary.simpleMessage(
      "Не вдається зберегти замовлення на веб-сайті",
    ),
    "canNotUpdateInfo": MessageLookupByLibrary.simpleMessage(
      "Не вдається оновити інформацію користувача",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Скасувати"),
    "cancelOrder": MessageLookupByLibrary.simpleMessage("Скасувати замовлення"),
    "cancelled": MessageLookupByLibrary.simpleMessage("Скасовано"),
    "cancelledRequests": MessageLookupByLibrary.simpleMessage(
      "Скасовані запити",
    ),
    "cannotBeEmpty": m8,
    "cannotDeleteAccount": MessageLookupByLibrary.simpleMessage(
      "Цей обліковий запис неможливо видалити",
    ),
    "cannotLessThreeLength": m9,
    "cannotSendMessage": MessageLookupByLibrary.simpleMessage(
      "Ви не можете надіслати повідомлення цьому користувачу",
    ),
    "cantFindThisOrderId": MessageLookupByLibrary.simpleMessage(
      "Не вдається знайти цей номер замовлення",
    ),
    "cantPickDateInThePast": MessageLookupByLibrary.simpleMessage(
      "Дата в минулому не дозволена",
    ),
    "card": MessageLookupByLibrary.simpleMessage("картка"),
    "cardHolder": MessageLookupByLibrary.simpleMessage("Власник картки"),
    "cardNumber": MessageLookupByLibrary.simpleMessage("Номер картки"),
    "cart": MessageLookupByLibrary.simpleMessage("Кошик"),
    "cartDiscount": MessageLookupByLibrary.simpleMessage("Знижка на кошик"),
    "cartNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Кошик недоступний. Будь ласка, додайте кілька товарів до кошика.",
    ),
    "cartNotReadyForCheckout": MessageLookupByLibrary.simpleMessage(
      "Ваш кошик ще обробляється. Будь ласка, зачекайте хвилинку.",
    ),
    "cash": MessageLookupByLibrary.simpleMessage("Готівка"),
    "categories": MessageLookupByLibrary.simpleMessage("Категорії"),
    "category": MessageLookupByLibrary.simpleMessage("Категорія"),
    "change": MessageLookupByLibrary.simpleMessage("Змінити"),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("Змінити мову"),
    "changePrinter": MessageLookupByLibrary.simpleMessage("Змінити принтер"),
    "changedCurrencyTo": m10,
    "characterRemain": m11,
    "chat": MessageLookupByLibrary.simpleMessage("Чат"),
    "chatGPT": MessageLookupByLibrary.simpleMessage("ChatGPT"),
    "chatListScreen": MessageLookupByLibrary.simpleMessage("Повідомлення"),
    "chatViaFacebook": MessageLookupByLibrary.simpleMessage(
      "Чат через Facebook Messenger",
    ),
    "chatViaWhatApp": MessageLookupByLibrary.simpleMessage(
      "Чат через WhatsApp",
    ),
    "chatWithBot": MessageLookupByLibrary.simpleMessage("Спілкування з ботом"),
    "chatWithStoreOwner": MessageLookupByLibrary.simpleMessage(
      "Спілкуйтеся з власником магазину",
    ),
    "checkConfirmLink": MessageLookupByLibrary.simpleMessage(
      "Перевірте електронну пошту для підтвердження",
    ),
    "checking": MessageLookupByLibrary.simpleMessage("Перевірка..."),
    "checkout": MessageLookupByLibrary.simpleMessage("Оформити замовлення"),
    "chinese": MessageLookupByLibrary.simpleMessage("Китайська"),
    "chineseSimplified": MessageLookupByLibrary.simpleMessage(
      "Китайська (спрощена)",
    ),
    "chineseTraditional": MessageLookupByLibrary.simpleMessage(
      "Китайська (традиційна)",
    ),
    "chooseBranch": MessageLookupByLibrary.simpleMessage("Оберіть філію"),
    "chooseCategory": MessageLookupByLibrary.simpleMessage(
      "Виберіть категорію",
    ),
    "chooseFromGallery": MessageLookupByLibrary.simpleMessage(
      "Вибрати з галереї",
    ),
    "chooseFromServer": MessageLookupByLibrary.simpleMessage(
      "Вибрати з сервера",
    ),
    "choosePlan": MessageLookupByLibrary.simpleMessage("Обрати план"),
    "chooseStaff": MessageLookupByLibrary.simpleMessage("Виберіть персонал"),
    "chooseType": MessageLookupByLibrary.simpleMessage("Виберіть тип"),
    "chooseYourPaymentMethod": MessageLookupByLibrary.simpleMessage(
      "Виберіть спосіб оплати",
    ),
    "city": MessageLookupByLibrary.simpleMessage("Місто"),
    "cityIsRequired": MessageLookupByLibrary.simpleMessage(
      "Поле міста обов\'язкове",
    ),
    "claim": MessageLookupByLibrary.simpleMessage("Претензія"),
    "claimed": MessageLookupByLibrary.simpleMessage("Заявлено"),
    "clear": MessageLookupByLibrary.simpleMessage("Очистити"),
    "clearCart": MessageLookupByLibrary.simpleMessage("Очистити кошик"),
    "clearCartAndAddNew": MessageLookupByLibrary.simpleMessage(
      "Очистити кошик і додати новий",
    ),
    "clearConversation": MessageLookupByLibrary.simpleMessage(
      "Очистити розмову",
    ),
    "close": MessageLookupByLibrary.simpleMessage("Закрити"),
    "closeNow": MessageLookupByLibrary.simpleMessage("Зараз зачинено"),
    "closed": MessageLookupByLibrary.simpleMessage("Зачинено"),
    "codExtraFee": MessageLookupByLibrary.simpleMessage(
      "Додаткова плата за післяплату",
    ),
    "color": MessageLookupByLibrary.simpleMessage("Колір"),
    "columns": MessageLookupByLibrary.simpleMessage("Стовпці"),
    "comment": MessageLookupByLibrary.simpleMessage("Коментар"),
    "commentFirst": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, напишіть коментар",
    ),
    "commentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Коментар успішно додано, зачекайте, поки ваш коментар буде схвалено",
    ),
    "complete": MessageLookupByLibrary.simpleMessage("Завершено"),
    "confirm": MessageLookupByLibrary.simpleMessage("Підтвердити"),
    "confirmAccountDeletion": MessageLookupByLibrary.simpleMessage(
      "Підтвердіть видалення облікового запису",
    ),
    "confirmClearCartWhenTopUp": MessageLookupByLibrary.simpleMessage(
      "Кошик буде очищено після поповнення",
    ),
    "confirmClearTheCart": MessageLookupByLibrary.simpleMessage(
      "Ви впевнені, що хочете очистити кошик?",
    ),
    "confirmDelete": MessageLookupByLibrary.simpleMessage(
      "Ви впевнені, що хочете це видалити? Цю дію неможливо скасувати",
    ),
    "confirmDeleteItem": MessageLookupByLibrary.simpleMessage(
      "Ви впевнені, що хочете видалити цей елемент?",
    ),
    "confirmPassword": MessageLookupByLibrary.simpleMessage(
      "Підтвердіть пароль",
    ),
    "confirmPasswordIsRequired": MessageLookupByLibrary.simpleMessage(
      "Необхідно підтвердити пароль",
    ),
    "confirmRemoveProductInCart": MessageLookupByLibrary.simpleMessage(
      "Ви впевнені, що хочете видалити цей товар?",
    ),
    "connect": MessageLookupByLibrary.simpleMessage("З\'єднати"),
    "contact": MessageLookupByLibrary.simpleMessage("Контакт"),
    "content": MessageLookupByLibrary.simpleMessage("Вміст"),
    "continueShopping": MessageLookupByLibrary.simpleMessage(
      "Продовжити покупки",
    ),
    "continueToPayment": MessageLookupByLibrary.simpleMessage(
      "Перейти до оплати",
    ),
    "continueToReview": MessageLookupByLibrary.simpleMessage(
      "Перейти до перегляду",
    ),
    "continueToSelectItem": MessageLookupByLibrary.simpleMessage(
      "Продовжити вибір товарів",
    ),
    "continueToShipping": MessageLookupByLibrary.simpleMessage(
      "Перейти до доставки",
    ),
    "continueWithShopify": MessageLookupByLibrary.simpleMessage(
      "Продовжити роботу з Shopify",
    ),
    "continues": MessageLookupByLibrary.simpleMessage("Продовжити"),
    "conversations": MessageLookupByLibrary.simpleMessage("Розмови"),
    "convertPoint": m12,
    "copied": MessageLookupByLibrary.simpleMessage("Скопійовано"),
    "copy": MessageLookupByLibrary.simpleMessage("Копіювати"),
    "copyright": MessageLookupByLibrary.simpleMessage(
      "© 2024 InspireUI. Усі права захищені",
    ),
    "countItem": m13,
    "countItems": m14,
    "countProduct": m15,
    "countProducts": m16,
    "country": MessageLookupByLibrary.simpleMessage("Країна"),
    "countryCodeIsRequired": MessageLookupByLibrary.simpleMessage(
      "Код країни обов\'язковий",
    ),
    "countryIsNotSupported": m17,
    "countryIsRequired": MessageLookupByLibrary.simpleMessage(
      "Поле країни обов\'язкове",
    ),
    "couponCode": MessageLookupByLibrary.simpleMessage("Код купона"),
    "couponHasBeenSavedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Купон успішно збережено",
    ),
    "couponInvalid": MessageLookupByLibrary.simpleMessage(
      "Код купона недійсний",
    ),
    "couponMsgSuccess": MessageLookupByLibrary.simpleMessage(
      "Вітаємо! Код купона успішно застосовано",
    ),
    "couponsDedicatedForYou": MessageLookupByLibrary.simpleMessage(
      "Купони, спеціально призначені для вас",
    ),
    "couponsManagement": MessageLookupByLibrary.simpleMessage(
      "Управління купонами",
    ),
    "create": MessageLookupByLibrary.simpleMessage("Створити"),
    "createAnAccount": MessageLookupByLibrary.simpleMessage(
      "Створити обліковий запис",
    ),
    "createNewPostSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Ваш допис успішно створено як чернетку. Будь ласка, перегляньте його в панелі адміністратора",
    ),
    "createPost": MessageLookupByLibrary.simpleMessage("Створити допис"),
    "createProduct": MessageLookupByLibrary.simpleMessage("Створити товар"),
    "createReviewSuccess": MessageLookupByLibrary.simpleMessage(
      "Дякуємо за відгук",
    ),
    "createReviewSuccessMsg": MessageLookupByLibrary.simpleMessage(
      "Ми щиро вдячні за ваш відгук і цінуємо ваш внесок у покращення нашого сервісу",
    ),
    "createVariants": MessageLookupByLibrary.simpleMessage(
      "Створити всі варіанти",
    ),
    "createdOn": MessageLookupByLibrary.simpleMessage("Створено:"),
    "currencies": MessageLookupByLibrary.simpleMessage("Валюти"),
    "currencyIsNotSupported": m18,
    "currentBid": MessageLookupByLibrary.simpleMessage("Поточна ставка"),
    "currentPassword": MessageLookupByLibrary.simpleMessage("Поточний пароль"),
    "currentlyWeOnlyHave": MessageLookupByLibrary.simpleMessage(
      "Наразі у нас є лише",
    ),
    "customer": MessageLookupByLibrary.simpleMessage("Клієнт"),
    "customerDetail": MessageLookupByLibrary.simpleMessage("Деталі клієнта"),
    "customerNote": MessageLookupByLibrary.simpleMessage("Примітка клієнта"),
    "cvv": MessageLookupByLibrary.simpleMessage("CVV"),
    "czech": MessageLookupByLibrary.simpleMessage("Чеська"),
    "danish": MessageLookupByLibrary.simpleMessage("Данська"),
    "darkTheme": MessageLookupByLibrary.simpleMessage("Темна тема"),
    "dashboard": MessageLookupByLibrary.simpleMessage("Панель керування"),
    "dataEmpty": MessageLookupByLibrary.simpleMessage("Дані відсутні"),
    "date": MessageLookupByLibrary.simpleMessage("Дата"),
    "dateASC": MessageLookupByLibrary.simpleMessage("Дата за зростанням"),
    "dateBooking": MessageLookupByLibrary.simpleMessage("Дата бронювання"),
    "dateDESC": MessageLookupByLibrary.simpleMessage("Дата за спаданням"),
    "dateEnd": MessageLookupByLibrary.simpleMessage("Дата закінчення"),
    "dateLatest": MessageLookupByLibrary.simpleMessage("Дата: найновіші"),
    "dateOldest": MessageLookupByLibrary.simpleMessage("Дата: найстаріші"),
    "dateStart": MessageLookupByLibrary.simpleMessage("Дата початку"),
    "dateTime": MessageLookupByLibrary.simpleMessage("Дата й час"),
    "dateWiseClose": MessageLookupByLibrary.simpleMessage("Дата закриття"),
    "daysAgo": m19,
    "debit": MessageLookupByLibrary.simpleMessage("Дебет"),
    "decline": MessageLookupByLibrary.simpleMessage("Відхилити"),
    "delete": MessageLookupByLibrary.simpleMessage("Видалити"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage(
      "Видалити обліковий запис",
    ),
    "deleteAccountMsg": MessageLookupByLibrary.simpleMessage(
      "Ви впевнені, що хочете видалити свій обліковий запис? Будь ласка, прочитайте, як вплине видалення облікового запису",
    ),
    "deleteAccountSuccess": MessageLookupByLibrary.simpleMessage(
      "Обліковий запис успішно видалено. Ваш сеанс завершено",
    ),
    "deleteAll": MessageLookupByLibrary.simpleMessage("Видалити все"),
    "deleteConversation": MessageLookupByLibrary.simpleMessage(
      "Видалити розмову",
    ),
    "delivered": MessageLookupByLibrary.simpleMessage("Доставлено"),
    "deliveredTo": MessageLookupByLibrary.simpleMessage("Доставлено"),
    "delivering": MessageLookupByLibrary.simpleMessage("ДОСТАВЛЯЄТЬСЯ"),
    "deliveryBoy": MessageLookupByLibrary.simpleMessage("Кур\'єр:"),
    "deliveryDate": MessageLookupByLibrary.simpleMessage("Дата доставки"),
    "deliveryDetails": MessageLookupByLibrary.simpleMessage("Деталі доставки"),
    "deliveryManagement": MessageLookupByLibrary.simpleMessage("Доставка"),
    "deliveryNotificationError": MessageLookupByLibrary.simpleMessage(
      "Немає даних.\nЦе замовлення видалено",
    ),
    "description": MessageLookupByLibrary.simpleMessage("Опис"),
    "descriptionEnterVoucher": MessageLookupByLibrary.simpleMessage(
      "Введіть або оберіть ваучер для замовлення",
    ),
    "didntReceiveCode": MessageLookupByLibrary.simpleMessage(
      "Не отримали код?",
    ),
    "direction": MessageLookupByLibrary.simpleMessage("Напрямок"),
    "disablePurchase": MessageLookupByLibrary.simpleMessage("Вимкнути покупку"),
    "discount": MessageLookupByLibrary.simpleMessage("Знижка"),
    "displayName": MessageLookupByLibrary.simpleMessage(
      "Ім\'я для відображення",
    ),
    "distance": m20,
    "doNotAnyTransactions": MessageLookupByLibrary.simpleMessage(
      "У вас ще немає транзакцій",
    ),
    "doYouWantToExitApp": MessageLookupByLibrary.simpleMessage(
      "Бажаєте вийти з програми?",
    ),
    "doYouWantToLeaveWithoutSubmit": MessageLookupByLibrary.simpleMessage(
      "Ви бажаєте вийти без надсилання відгуку?",
    ),
    "doYouWantToLogout": MessageLookupByLibrary.simpleMessage("Бажаєте вийти?"),
    "doYouWantToUnblock": MessageLookupByLibrary.simpleMessage(
      "Ви хочете розблокувати цього користувача?",
    ),
    "doesNotSupportApplePay": MessageLookupByLibrary.simpleMessage(
      "ApplePay не підтримується. Будь ласка, перевірте свій гаманець і картку",
    ),
    "done": MessageLookupByLibrary.simpleMessage("Готово"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage(
      "Немає облікового запису?",
    ),
    "download": MessageLookupByLibrary.simpleMessage("Завантажити"),
    "downloadApp": MessageLookupByLibrary.simpleMessage("Завантажити додаток"),
    "downloadingImages": MessageLookupByLibrary.simpleMessage(
      "Завантаження зображень...",
    ),
    "draft": MessageLookupByLibrary.simpleMessage("Чернетка"),
    "driverAssigned": MessageLookupByLibrary.simpleMessage("Призначено водія"),
    "duration": MessageLookupByLibrary.simpleMessage("Тривалість"),
    "dutch": MessageLookupByLibrary.simpleMessage("Нідерландська"),
    "earnings": MessageLookupByLibrary.simpleMessage("Заробіток"),
    "edit": MessageLookupByLibrary.simpleMessage("Редагувати:"),
    "editProductInfo": MessageLookupByLibrary.simpleMessage(
      "Редагувати інформацію про товар",
    ),
    "editWithoutColon": MessageLookupByLibrary.simpleMessage("Редагувати"),
    "egypt": MessageLookupByLibrary.simpleMessage("Єгипетська"),
    "email": MessageLookupByLibrary.simpleMessage("Електронна пошта"),
    "emailAddressInvalid": MessageLookupByLibrary.simpleMessage(
      "Неправильна електронна адреса",
    ),
    "emailAlreadyInUse": MessageLookupByLibrary.simpleMessage(
      "Ця електронна адреса вже використовується!",
    ),
    "emailDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "Видалення облікового запису скасує вашу підписку на всі списки розсилки",
    ),
    "emailDoesNotExist": MessageLookupByLibrary.simpleMessage(
      "Обліковий запис електронної пошти не існує",
    ),
    "emailIsRequired": MessageLookupByLibrary.simpleMessage(
      "Поле електронної пошти обов\'язкове",
    ),
    "emailSubscription": MessageLookupByLibrary.simpleMessage(
      "Підписка на електронну пошту",
    ),
    "emptyBookingHistoryMsg": MessageLookupByLibrary.simpleMessage(
      "Схоже, ви ще не зробили жодного бронювання.\nПочніть досліджувати та зробіть своє перше бронювання!",
    ),
    "emptyCart": MessageLookupByLibrary.simpleMessage("Кошик порожній"),
    "emptyCartSubtitle": MessageLookupByLibrary.simpleMessage(
      "Схоже, ви ще нічого не додали до кошика. Почніть робити покупки.",
    ),
    "emptyCartSubtitle02": MessageLookupByLibrary.simpleMessage(
      "Ой! Ваш кошик порожній.\n\nГотові знайти щось особливе?",
    ),
    "emptyComment": MessageLookupByLibrary.simpleMessage(
      "Коментар не може бути порожнім",
    ),
    "emptySearch": MessageLookupByLibrary.simpleMessage(
      "Ви ще нічого не шукали. Почніть зараз - ми допоможемо вам",
    ),
    "emptyShippingMsg": MessageLookupByLibrary.simpleMessage(
      "Варіанти доставки відсутні. Будь ласка, переконайтеся, що вашу адресу введено правильно, або зв\'яжіться з нами, якщо вам потрібна допомога",
    ),
    "emptyUsername": MessageLookupByLibrary.simpleMessage(
      "Ім\'я користувача/електронна пошта порожні",
    ),
    "emptyWishlist": MessageLookupByLibrary.simpleMessage(
      "Список бажань порожній",
    ),
    "emptyWishlistSubtitle": MessageLookupByLibrary.simpleMessage(
      "Торкніться серця біля товару, щоб додати його до вибраного. Ми збережемо його тут!",
    ),
    "emptyWishlistSubtitle02": MessageLookupByLibrary.simpleMessage(
      "Ваш список бажань поки що порожній.\nПочніть додавати товари прямо зараз!",
    ),
    "enableForCheckout": MessageLookupByLibrary.simpleMessage(
      "Увімкнути для оформлення замовлення",
    ),
    "enableForLogin": MessageLookupByLibrary.simpleMessage(
      "Увімкнути для входу",
    ),
    "enableForWallet": MessageLookupByLibrary.simpleMessage(
      "Увімкнути для гаманця",
    ),
    "enableVacationMode": MessageLookupByLibrary.simpleMessage(
      "Увімкнути режим відпустки",
    ),
    "endDateCantBeAfterFirstDate": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, виберіть дату після першої дати",
    ),
    "endsIn": m21,
    "english": MessageLookupByLibrary.simpleMessage("Англійська"),
    "enterAmount": MessageLookupByLibrary.simpleMessage("Введіть суму"),
    "enterCaptcha": m22,
    "enterDescription": MessageLookupByLibrary.simpleMessage("Введіть опис"),
    "enterEmailEachRecipient": MessageLookupByLibrary.simpleMessage(
      "Введіть електронну адресу для кожного отримувача",
    ),
    "enterPoint": MessageLookupByLibrary.simpleMessage("Введіть точку"),
    "enterPrice": MessageLookupByLibrary.simpleMessage("Введіть ціну"),
    "enterSentCode": MessageLookupByLibrary.simpleMessage(
      "Введіть код, надісланий на",
    ),
    "enterVoucherCode": MessageLookupByLibrary.simpleMessage(
      "Введіть код ваучера",
    ),
    "enterYourEmail": MessageLookupByLibrary.simpleMessage(
      "Введіть електронну пошту",
    ),
    "enterYourEmailOrUsername": MessageLookupByLibrary.simpleMessage(
      "Введіть свою електронну адресу або ім\'я користувача",
    ),
    "enterYourFirstName": MessageLookupByLibrary.simpleMessage(
      "Введіть своє ім\'я",
    ),
    "enterYourLastName": MessageLookupByLibrary.simpleMessage(
      "Введіть своє прізвище",
    ),
    "enterYourMobile": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть номер мобільного телефону",
    ),
    "enterYourNote": MessageLookupByLibrary.simpleMessage(
      "Введіть свою нотатку",
    ),
    "enterYourPassword": MessageLookupByLibrary.simpleMessage("Введіть пароль"),
    "enterYourPhone": MessageLookupByLibrary.simpleMessage(
      "Введіть свій номер телефону, щоб почати",
    ),
    "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Введіть свій номер телефону",
    ),
    "enterYourUsername": MessageLookupByLibrary.simpleMessage(
      "Введіть ваше ім\'я користувача",
    ),
    "error": m23,
    "errorAmountTransfer": MessageLookupByLibrary.simpleMessage(
      "Введена сума перевищує поточний баланс гаманця. Будь ласка, спробуйте ще раз!",
    ),
    "errorEmailFormat": MessageLookupByLibrary.simpleMessage(
      "Введіть дійсну адресу електронної пошти",
    ),
    "errorMsg": m24,
    "errorOnGettingPost": MessageLookupByLibrary.simpleMessage(
      "Помилка при отриманні допису!",
    ),
    "errorPasswordFormat": MessageLookupByLibrary.simpleMessage(
      "Введіть пароль щонайменше з 8 символів",
    ),
    "errorTitle": MessageLookupByLibrary.simpleMessage("Помилка"),
    "evening": MessageLookupByLibrary.simpleMessage("Вечір"),
    "events": MessageLookupByLibrary.simpleMessage("Події"),
    "expectedDeliveryDate": MessageLookupByLibrary.simpleMessage(
      "Очікувана дата доставки",
    ),
    "expired": MessageLookupByLibrary.simpleMessage("Термін дії закінчився"),
    "expiredDate": MessageLookupByLibrary.simpleMessage(
      "Дата закінчення терміну дії",
    ),
    "expiredDateHint": MessageLookupByLibrary.simpleMessage("ММ/РР"),
    "expiringInTime": m25,
    "exploreNow": MessageLookupByLibrary.simpleMessage("Досліджувати зараз"),
    "external": MessageLookupByLibrary.simpleMessage("Зовнішній"),
    "extraServices": MessageLookupByLibrary.simpleMessage("Додаткові послуги"),
    "failToAssign": MessageLookupByLibrary.simpleMessage(
      "Не вдалося призначити користувача",
    ),
    "failedToGenerateLink": MessageLookupByLibrary.simpleMessage(
      "Не вдалося створити посилання",
    ),
    "failedToLoadAppConfig": MessageLookupByLibrary.simpleMessage(
      "Не вдалося завантажити конфігурацію програми. Спробуйте ще раз або перезапустіть програму",
    ),
    "failedToLoadImage": MessageLookupByLibrary.simpleMessage(
      "Не вдалося завантажити зображення",
    ),
    "fair": MessageLookupByLibrary.simpleMessage("Задовільно"),
    "favorite": MessageLookupByLibrary.simpleMessage("Улюблене"),
    "fax": MessageLookupByLibrary.simpleMessage("Факс"),
    "feature": MessageLookupByLibrary.simpleMessage("Функція"),
    "featureNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Функція недоступна",
    ),
    "featureProducts": MessageLookupByLibrary.simpleMessage(
      "Рекомендовані товари",
    ),
    "featured": MessageLookupByLibrary.simpleMessage("Рекомендовані"),
    "features": MessageLookupByLibrary.simpleMessage("Особливості"),
    "fileIsTooBig": MessageLookupByLibrary.simpleMessage(
      "Файл занадто великий. Виберіть менший файл!",
    ),
    "fileUploadFailed": MessageLookupByLibrary.simpleMessage(
      "Не вдалося завантажити файл!",
    ),
    "files": MessageLookupByLibrary.simpleMessage("Файли"),
    "filter": MessageLookupByLibrary.simpleMessage("Фільтр"),
    "fingerprintsTouchID": MessageLookupByLibrary.simpleMessage(
      "Відбитки пальців, Touch ID",
    ),
    "finishSetup": MessageLookupByLibrary.simpleMessage(
      "Завершити налаштування",
    ),
    "finnish": MessageLookupByLibrary.simpleMessage("Фінська"),
    "firstComment": MessageLookupByLibrary.simpleMessage(
      "Будьте першим, хто прокоментує цей допис!",
    ),
    "firstName": MessageLookupByLibrary.simpleMessage("Ім\'я"),
    "firstNameIsRequired": MessageLookupByLibrary.simpleMessage(
      "Поле імені обов\'язкове",
    ),
    "firstRenewal": MessageLookupByLibrary.simpleMessage("Перше продовження"),
    "fixedCartDiscount": MessageLookupByLibrary.simpleMessage(
      "Фіксована знижка на кошик",
    ),
    "fixedProductDiscount": MessageLookupByLibrary.simpleMessage(
      "Фіксована знижка на товар",
    ),
    "forThisProduct": MessageLookupByLibrary.simpleMessage("для цього товару"),
    "free": MessageLookupByLibrary.simpleMessage("Безкоштовно"),
    "freeOfCharge": MessageLookupByLibrary.simpleMessage("Безкоштовно"),
    "french": MessageLookupByLibrary.simpleMessage("Французька"),
    "friday": MessageLookupByLibrary.simpleMessage("П\'ятниця"),
    "from": MessageLookupByLibrary.simpleMessage("Від"),
    "fullName": MessageLookupByLibrary.simpleMessage("Повне ім\'я"),
    "gallery": MessageLookupByLibrary.simpleMessage("Галерея"),
    "generalError": MessageLookupByLibrary.simpleMessage(
      "Щось пішло не так. Спробуйте ще раз.",
    ),
    "generalSetting": MessageLookupByLibrary.simpleMessage(
      "Загальні налаштування",
    ),
    "generatingLink": MessageLookupByLibrary.simpleMessage(
      "Створення посилання...",
    ),
    "german": MessageLookupByLibrary.simpleMessage("Німецька"),
    "getNotification": MessageLookupByLibrary.simpleMessage(
      "Отримувати сповіщення",
    ),
    "getNotified": MessageLookupByLibrary.simpleMessage(
      "Отримувати сповіщення!",
    ),
    "getPasswordLink": MessageLookupByLibrary.simpleMessage(
      "Отримати посилання для відновлення пароля",
    ),
    "getStarted": MessageLookupByLibrary.simpleMessage("Почати"),
    "goBack": MessageLookupByLibrary.simpleMessage("Повернутися"),
    "goBackHomePage": MessageLookupByLibrary.simpleMessage(
      "Повернутися на головну сторінку",
    ),
    "goBackToAddress": MessageLookupByLibrary.simpleMessage(
      "Повернутися до адреси",
    ),
    "goBackToReview": MessageLookupByLibrary.simpleMessage(
      "Повернутися до відгуків",
    ),
    "goBackToShipping": MessageLookupByLibrary.simpleMessage(
      "Повернутися до доставки",
    ),
    "gold": MessageLookupByLibrary.simpleMessage("золото"),
    "goldPriority": MessageLookupByLibrary.simpleMessage("Золотий пріоритет"),
    "good": MessageLookupByLibrary.simpleMessage("Добре"),
    "graphqlAuthError": MessageLookupByLibrary.simpleMessage(
      "Не вдалося виконати автентифікацію. Будь ласка, увійдіть ще раз.",
    ),
    "graphqlAuthzError": MessageLookupByLibrary.simpleMessage(
      "У вас немає дозволу на виконання цієї дії.",
    ),
    "graphqlError": MessageLookupByLibrary.simpleMessage(
      "Щось пішло не так під час спроби виконати цю дію. Будь ласка, перевірте ще раз.",
    ),
    "graphqlValidationError": MessageLookupByLibrary.simpleMessage(
      "Надано недійсні дані. Будь ласка, перевірте введені дані.",
    ),
    "greaterDistance": m26,
    "greek": MessageLookupByLibrary.simpleMessage("Грецька"),
    "grossSales": MessageLookupByLibrary.simpleMessage("Валові продажі"),
    "grouped": MessageLookupByLibrary.simpleMessage("Згруповані"),
    "guests": MessageLookupByLibrary.simpleMessage("Гості"),
    "hasBeenDeleted": MessageLookupByLibrary.simpleMessage("було видалено"),
    "hebrew": MessageLookupByLibrary.simpleMessage("Іврит"),
    "hideAbout": MessageLookupByLibrary.simpleMessage("Приховати інформацію"),
    "hideAddress": MessageLookupByLibrary.simpleMessage("Приховати адресу"),
    "hideEmail": MessageLookupByLibrary.simpleMessage(
      "Приховати електронну пошту",
    ),
    "hideMap": MessageLookupByLibrary.simpleMessage("Приховати карту"),
    "hidePhone": MessageLookupByLibrary.simpleMessage("Приховати телефон"),
    "hidePolicy": MessageLookupByLibrary.simpleMessage("Приховати політику"),
    "hindi": MessageLookupByLibrary.simpleMessage("Хінді"),
    "history": MessageLookupByLibrary.simpleMessage("Історія"),
    "historyTransaction": MessageLookupByLibrary.simpleMessage("Історія"),
    "home": MessageLookupByLibrary.simpleMessage("Головна"),
    "horizontal": MessageLookupByLibrary.simpleMessage("Горизонтальний"),
    "hour": MessageLookupByLibrary.simpleMessage("Година"),
    "hoursAgo": m27,
    "howToEarnPoints": MessageLookupByLibrary.simpleMessage(
      "Як заробляти бали?",
    ),
    "hungarian": MessageLookupByLibrary.simpleMessage("Угорська"),
    "hungary": MessageLookupByLibrary.simpleMessage("Угорська"),
    "iAgree": MessageLookupByLibrary.simpleMessage("Я погоджуюся з"),
    "imIn": MessageLookupByLibrary.simpleMessage("Я в"),
    "imageFeature": MessageLookupByLibrary.simpleMessage("Зображення"),
    "imageGallery": MessageLookupByLibrary.simpleMessage("Галерея зображень"),
    "imageGenerate": MessageLookupByLibrary.simpleMessage(
      "Генерування зображення",
    ),
    "imageNetwork": MessageLookupByLibrary.simpleMessage("Мережа зображень"),
    "images": MessageLookupByLibrary.simpleMessage("Зображення"),
    "inStock": MessageLookupByLibrary.simpleMessage("В наявності"),
    "incorrectPassword": MessageLookupByLibrary.simpleMessage(
      "Неправильний пароль",
    ),
    "india": MessageLookupByLibrary.simpleMessage("Індійська"),
    "indonesian": MessageLookupByLibrary.simpleMessage("Індонезійська"),
    "informationTable": MessageLookupByLibrary.simpleMessage(
      "Інформаційна таблиця",
    ),
    "installDigitsPlugin": MessageLookupByLibrary.simpleMessage(
      "Встановіть плагін DIGITS: Wordpress Mobile Number Signup and Login",
    ),
    "instantlyClose": MessageLookupByLibrary.simpleMessage("Миттєво закрити"),
    "insufficientBalanceMessage": m28,
    "invalidAddress": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть дійсну адресу",
    ),
    "invalidAddressFormat": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть повну адресу з назвою вулиці та номером будинку",
    ),
    "invalidCity": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть дійсну назву міста",
    ),
    "invalidCityFormat": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть дійсну назву міста без спеціальних символів",
    ),
    "invalidCountry": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, виберіть дійсну країну",
    ),
    "invalidCountryCode": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, виберіть дійсний код країни",
    ),
    "invalidCountryCodeFormat": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, виберіть дійсний код країни зі списку",
    ),
    "invalidCountryFormat": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, виберіть країну зі списку",
    ),
    "invalidEmail": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть дійсну адресу електронної пошти",
    ),
    "invalidEmailFormat": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть дійсний формат електронної пошти (наприклад, <EMAIL>)",
    ),
    "invalidPhone": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть дійсний номер телефону",
    ),
    "invalidPhoneFormat": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть дійсний формат номера телефону",
    ),
    "invalidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Недійсний номер телефону",
    ),
    "invalidPostalCode": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть дійсний поштовий індекс",
    ),
    "invalidPostalCodeFormat": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть дійсний формат поштового індексу для вашої країни",
    ),
    "invalidProvince": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть дійсну назву області/штату",
    ),
    "invalidProvinceFormat": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть дійсну назву провінції/штату",
    ),
    "invalidSMSCode": MessageLookupByLibrary.simpleMessage(
      "Недійсний код підтвердження SMS",
    ),
    "invalidYearOfBirth": MessageLookupByLibrary.simpleMessage(
      "Недійсний рік народження",
    ),
    "invoice": MessageLookupByLibrary.simpleMessage("Рахунок"),
    "isEverythingSet": MessageLookupByLibrary.simpleMessage(
      "Все налаштовано...?",
    ),
    "isTyping": MessageLookupByLibrary.simpleMessage("друкує..."),
    "italian": MessageLookupByLibrary.simpleMessage("Італійська"),
    "item": MessageLookupByLibrary.simpleMessage("Елемент"),
    "itemCondition": MessageLookupByLibrary.simpleMessage("Стан товару"),
    "itemConditionNew": MessageLookupByLibrary.simpleMessage("новий"),
    "itemTotal": MessageLookupByLibrary.simpleMessage("Всього:"),
    "items": MessageLookupByLibrary.simpleMessage("Товари"),
    "itsOrdered": MessageLookupByLibrary.simpleMessage("Замовлено!"),
    "iwantToCreateAccount": MessageLookupByLibrary.simpleMessage(
      "Я хочу створити обліковий запис",
    ),
    "japanese": MessageLookupByLibrary.simpleMessage("Японська"),
    "kannada": MessageLookupByLibrary.simpleMessage("Каннада"),
    "keep": MessageLookupByLibrary.simpleMessage("Зберегти"),
    "khmer": MessageLookupByLibrary.simpleMessage("Кхмерська"),
    "korean": MessageLookupByLibrary.simpleMessage("Корейська"),
    "kurdish": MessageLookupByLibrary.simpleMessage("Курдська"),
    "language": MessageLookupByLibrary.simpleMessage("Мови"),
    "languageSuccess": MessageLookupByLibrary.simpleMessage(
      "Мову успішно оновлено",
    ),
    "lao": MessageLookupByLibrary.simpleMessage("Лаоська"),
    "lastName": MessageLookupByLibrary.simpleMessage("Прізвище"),
    "lastNameIsRequired": MessageLookupByLibrary.simpleMessage(
      "Поле прізвища обов\'язкове",
    ),
    "lastTransactions": MessageLookupByLibrary.simpleMessage(
      "Останні транзакції",
    ),
    "latestProducts": MessageLookupByLibrary.simpleMessage("Найновіші товари"),
    "layout": MessageLookupByLibrary.simpleMessage("Макети"),
    "lightTheme": MessageLookupByLibrary.simpleMessage("Світла тема"),
    "link": MessageLookupByLibrary.simpleMessage("Посилання"),
    "list": MessageLookupByLibrary.simpleMessage("список"),
    "listBannerType": MessageLookupByLibrary.simpleMessage("Тип банера списку"),
    "listBannerVideo": MessageLookupByLibrary.simpleMessage(
      "Список банерних відео",
    ),
    "listMessages": MessageLookupByLibrary.simpleMessage("Сповіщення"),
    "listTile": MessageLookupByLibrary.simpleMessage("Плитка списку"),
    "listening": MessageLookupByLibrary.simpleMessage("Прослуховування..."),
    "loadFail": MessageLookupByLibrary.simpleMessage("Не вдалося завантажити!"),
    "loadFailed": MessageLookupByLibrary.simpleMessage(
      "Завантаження не вдалося!",
    ),
    "loading": MessageLookupByLibrary.simpleMessage("Завантаження..."),
    "loadingLink": MessageLookupByLibrary.simpleMessage(
      "Завантаження посилання...",
    ),
    "location": MessageLookupByLibrary.simpleMessage("Місцезнаходження"),
    "lockScreenAndSecurity": MessageLookupByLibrary.simpleMessage(
      "Екран блокування та безпека",
    ),
    "login": MessageLookupByLibrary.simpleMessage("Увійти"),
    "loginCanceled": MessageLookupByLibrary.simpleMessage("Вхід скасовано"),
    "loginErrorServiceProvider": m29,
    "loginFailed": MessageLookupByLibrary.simpleMessage("Помилка входу!"),
    "loginInvalid": MessageLookupByLibrary.simpleMessage(
      "Вам не дозволено використовувати цю програму.",
    ),
    "loginRequired": MessageLookupByLibrary.simpleMessage("Потрібен вхід"),
    "loginSuccess": MessageLookupByLibrary.simpleMessage("Вхід успішний!"),
    "loginToComment": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, увійдіть, щоб коментувати",
    ),
    "loginToContinue": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, увійдіть, щоб продовжити",
    ),
    "loginToReview": MessageLookupByLibrary.simpleMessage(
      "Увійдіть, щоб залишити відгук",
    ),
    "loginToYourAccount": MessageLookupByLibrary.simpleMessage(
      "Увійдіть до свого облікового запису",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("Вийти"),
    "logoutFailed": MessageLookupByLibrary.simpleMessage("Не вдалося вийти"),
    "logoutSuccess": MessageLookupByLibrary.simpleMessage("Вихід успішний"),
    "loyaltyVoucher": MessageLookupByLibrary.simpleMessage("Ваучер лояльності"),
    "malay": MessageLookupByLibrary.simpleMessage("Малайська"),
    "manCollections": MessageLookupByLibrary.simpleMessage("Чоловічі колекції"),
    "manageApiKey": MessageLookupByLibrary.simpleMessage(
      "Керування API-ключем",
    ),
    "manageStock": MessageLookupByLibrary.simpleMessage("Керувати запасами"),
    "map": MessageLookupByLibrary.simpleMessage("Карта"),
    "marathi": MessageLookupByLibrary.simpleMessage("Маратхі"),
    "markAsRead": MessageLookupByLibrary.simpleMessage(
      "Позначити як прочитане",
    ),
    "markAsShipped": MessageLookupByLibrary.simpleMessage(
      "Позначити як відправлене",
    ),
    "markAsUnread": MessageLookupByLibrary.simpleMessage(
      "Позначити як непрочитане",
    ),
    "maxAmountForPayment": m30,
    "maximumFileSizeMb": m31,
    "maybeLater": MessageLookupByLibrary.simpleMessage("Можливо пізніше"),
    "menuOrder": MessageLookupByLibrary.simpleMessage("Порядок меню"),
    "menuServiceItems": m32,
    "menus": MessageLookupByLibrary.simpleMessage("Меню"),
    "message": MessageLookupByLibrary.simpleMessage("Повідомлення"),
    "messageTo": MessageLookupByLibrary.simpleMessage("Надіслати повідомлення"),
    "minAmountForPayment": m33,
    "minOrderAmount": m34,
    "minTotalCouponInvalidMsg": m35,
    "minTransaction": m36,
    "minimumQuantityIs": MessageLookupByLibrary.simpleMessage(
      "Мінімальна кількість:",
    ),
    "minutesAgo": m37,
    "mobile": MessageLookupByLibrary.simpleMessage("Мобільний"),
    "mobileIsRequired": MessageLookupByLibrary.simpleMessage(
      "Номер телефону обов\'язковий",
    ),
    "mobileNumberInUse": MessageLookupByLibrary.simpleMessage(
      "Цей номер телефону вже використовується!",
    ),
    "mobileNumberIsNotRegistered": MessageLookupByLibrary.simpleMessage(
      "Номер телефону не зареєстрований!",
    ),
    "mobileVerification": MessageLookupByLibrary.simpleMessage(
      "Мобільна перевірка",
    ),
    "momentAgo": MessageLookupByLibrary.simpleMessage("хвилину тому"),
    "monday": MessageLookupByLibrary.simpleMessage("Понеділок"),
    "monthsAgo": m38,
    "more": MessageLookupByLibrary.simpleMessage("...більше"),
    "moreFromStore": m39,
    "moreInformation": MessageLookupByLibrary.simpleMessage(
      "Більше інформації",
    ),
    "morning": MessageLookupByLibrary.simpleMessage("Ранок"),
    "multipleSellersDetected": MessageLookupByLibrary.simpleMessage(
      "Виявлено кількох продавців",
    ),
    "multipleSellersDetectedAndDisableMultiVendorCheckoutContent":
        MessageLookupByLibrary.simpleMessage(
          "Ви намагаєтесь додати товар від нового продавця до кошика. Зверніть увагу, що ви можете купувати товари лише в одного продавця за раз",
        ),
    "multipleSellersDetectedAndEnableMultiVendorCheckoutContent":
        MessageLookupByLibrary.simpleMessage(
          "Ви намагаєтесь додати товар від нового продавця до кошика. Бажаєте продовжити?",
        ),
    "mustBeBoughtInGroupsOf": m40,
    "mustSelectOneItem": MessageLookupByLibrary.simpleMessage(
      "Потрібно вибрати 1 елемент",
    ),
    "myCart": MessageLookupByLibrary.simpleMessage("Мій кошик"),
    "myCoupons": MessageLookupByLibrary.simpleMessage("Мої купони"),
    "myOrder": MessageLookupByLibrary.simpleMessage("Моє замовлення"),
    "myPoints": MessageLookupByLibrary.simpleMessage("Мої бали"),
    "myProducts": MessageLookupByLibrary.simpleMessage("Мої товари"),
    "myProductsEmpty": MessageLookupByLibrary.simpleMessage(
      "У вас немає товарів. Спробуйте створити!",
    ),
    "myQRCode": MessageLookupByLibrary.simpleMessage("Мій QR-код"),
    "myQRCodeNote": MessageLookupByLibrary.simpleMessage(
      "Надайте цей код співробітникам, щоб",
    ),
    "myRating": MessageLookupByLibrary.simpleMessage("Моя оцінка"),
    "myReviews": MessageLookupByLibrary.simpleMessage("Мої відгуки"),
    "myWallet": MessageLookupByLibrary.simpleMessage("Мій гаманець"),
    "myWishList": MessageLookupByLibrary.simpleMessage("Мій список бажань"),
    "nItems": m41,
    "name": MessageLookupByLibrary.simpleMessage("Ім\'я"),
    "nameOnCard": MessageLookupByLibrary.simpleMessage("Ім\'я на картці"),
    "nearbyPlaces": MessageLookupByLibrary.simpleMessage("Місця поблизу"),
    "needHelp": MessageLookupByLibrary.simpleMessage("Потрібна допомога?"),
    "needToLoginAgain": MessageLookupByLibrary.simpleMessage(
      "Вам потрібно буде увійти знову, щоб оновлення набуло чинності",
    ),
    "netherlands": MessageLookupByLibrary.simpleMessage("Нідерланди"),
    "networkError": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, перевірте свою мережу ще раз",
    ),
    "networkServerError": MessageLookupByLibrary.simpleMessage(
      "Помилка сервера. Будь ласка, спробуйте пізніше.",
    ),
    "networkTimeout": MessageLookupByLibrary.simpleMessage(
      "Час очікування з’єднання минув. Спробуйте ще раз.",
    ),
    "newAppConfig": MessageLookupByLibrary.simpleMessage(
      "Доступний новий вміст!",
    ),
    "newPassword": MessageLookupByLibrary.simpleMessage("Новий пароль"),
    "newVariation": MessageLookupByLibrary.simpleMessage("Новий варіант"),
    "next": MessageLookupByLibrary.simpleMessage("Далі"),
    "niceName": MessageLookupByLibrary.simpleMessage("Гарне ім\'я"),
    "no": MessageLookupByLibrary.simpleMessage("Ні"),
    "noAddressHaveBeenSaved": MessageLookupByLibrary.simpleMessage(
      "Жодної адреси ще не збережено",
    ),
    "noBackHistoryItem": MessageLookupByLibrary.simpleMessage(
      "Немає попередніх елементів",
    ),
    "noBlog": MessageLookupByLibrary.simpleMessage("Упс, блог більше не існує"),
    "noCameraPermissionIsGranted": MessageLookupByLibrary.simpleMessage(
      "Дозвіл на камеру не надано. Надайте його в налаштуваннях пристрою",
    ),
    "noComments": MessageLookupByLibrary.simpleMessage("Коментарів немає"),
    "noConversation": MessageLookupByLibrary.simpleMessage("Розмови ще немає"),
    "noConversationDescription": MessageLookupByLibrary.simpleMessage(
      "З\'явиться, коли хтось почне з вами спілкуватися",
    ),
    "noData": MessageLookupByLibrary.simpleMessage("Більше немає даних"),
    "noFavoritesYet": MessageLookupByLibrary.simpleMessage(
      "Ще немає вибраного",
    ),
    "noFileToDownload": MessageLookupByLibrary.simpleMessage(
      "Немає файлів для завантаження",
    ),
    "noForwardHistoryItem": MessageLookupByLibrary.simpleMessage(
      "Немає наступних елементів",
    ),
    "noInternetConnection": MessageLookupByLibrary.simpleMessage(
      "Немає підключення до Інтернету",
    ),
    "noListingNearby": MessageLookupByLibrary.simpleMessage(
      "Поблизу немає оголошень!",
    ),
    "noOrders": MessageLookupByLibrary.simpleMessage("Немає замовлень"),
    "noPaymentMethodsAvailable": MessageLookupByLibrary.simpleMessage(
      "Способи оплати недоступні",
    ),
    "noPermissionForCurrentRole": MessageLookupByLibrary.simpleMessage(
      "На жаль, цей товар недоступний для вашої поточної ролі",
    ),
    "noPermissionToViewProduct": MessageLookupByLibrary.simpleMessage(
      "Цей товар доступний лише для користувачів із певними ролями. Будь ласка, увійдіть з відповідними обліковими даними для доступу до цього товару або зв\'яжіться з нами для отримання додаткової інформації",
    ),
    "noPermissionToViewProductMsg": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, увійдіть з відповідними обліковими даними для доступу до цього товару або зв\'яжіться з нами для отримання додаткової інформації",
    ),
    "noPost": MessageLookupByLibrary.simpleMessage(
      "Упс, ця сторінка більше не існує!",
    ),
    "noPrinters": MessageLookupByLibrary.simpleMessage("Немає принтерів"),
    "noProduct": MessageLookupByLibrary.simpleMessage("Немає товару"),
    "noResultFound": MessageLookupByLibrary.simpleMessage(
      "Результатів не знайдено",
    ),
    "noReviews": MessageLookupByLibrary.simpleMessage("Немає відгуків"),
    "noSlotAvailable": MessageLookupByLibrary.simpleMessage(
      "Немає доступних слотів",
    ),
    "noStoreNearby": MessageLookupByLibrary.simpleMessage(
      "Поблизу немає магазинів!",
    ),
    "noSuggestionSearch": MessageLookupByLibrary.simpleMessage(
      "Немає пропозицій",
    ),
    "noThanks": MessageLookupByLibrary.simpleMessage("Ні, дякую"),
    "noTransactionsMsg": MessageLookupByLibrary.simpleMessage(
      "На жаль, транзакцій не знайдено!",
    ),
    "noVideoFound": MessageLookupByLibrary.simpleMessage(
      "На жаль, відео не знайдено",
    ),
    "none": MessageLookupByLibrary.simpleMessage("Немає"),
    "normal": MessageLookupByLibrary.simpleMessage("нормальний"),
    "notFindResult": MessageLookupByLibrary.simpleMessage(
      "На жаль, ми не знайшли жодного результату",
    ),
    "notFound": MessageLookupByLibrary.simpleMessage("Не знайдено"),
    "notRated": MessageLookupByLibrary.simpleMessage("Не оцінено"),
    "note": MessageLookupByLibrary.simpleMessage("Примітки до замовлення"),
    "noteMessage": MessageLookupByLibrary.simpleMessage("Примітка"),
    "noteOptional": MessageLookupByLibrary.simpleMessage(
      "Примітка (необов\'язково)",
    ),
    "noteTransfer": MessageLookupByLibrary.simpleMessage(
      "Примітка (необов\'язково)",
    ),
    "notice": MessageLookupByLibrary.simpleMessage("Повідомлення"),
    "notifications": MessageLookupByLibrary.simpleMessage("Сповіщення"),
    "notifyLatestOffer": MessageLookupByLibrary.simpleMessage(
      "Повідомляти про останні пропозиції та наявність товарів",
    ),
    "ofThisProduct": MessageLookupByLibrary.simpleMessage("цього товару"),
    "ok": MessageLookupByLibrary.simpleMessage("OK"),
    "on": MessageLookupByLibrary.simpleMessage("Увімк."),
    "onSale": MessageLookupByLibrary.simpleMessage("Розпродаж"),
    "onVacation": MessageLookupByLibrary.simpleMessage("У відпустці"),
    "oneEachRecipient": MessageLookupByLibrary.simpleMessage(
      "По одному кожному отримувачу",
    ),
    "online": MessageLookupByLibrary.simpleMessage("Онлайн"),
    "open24Hours": MessageLookupByLibrary.simpleMessage("Відчинено цілодобово"),
    "openMap": MessageLookupByLibrary.simpleMessage("Відкрити карту"),
    "openNow": MessageLookupByLibrary.simpleMessage("Зараз відчинено"),
    "openSettings": MessageLookupByLibrary.simpleMessage(
      "Відкрийте налаштування",
    ),
    "openingHours": MessageLookupByLibrary.simpleMessage("Години роботи"),
    "optional": MessageLookupByLibrary.simpleMessage("Необов\'язково"),
    "options": MessageLookupByLibrary.simpleMessage("Опції"),
    "optionsTotal": m42,
    "or": MessageLookupByLibrary.simpleMessage("або"),
    "orLoginWith": MessageLookupByLibrary.simpleMessage("або увійдіть через"),
    "orderConfirmation": MessageLookupByLibrary.simpleMessage(
      "Підтвердження замовлення",
    ),
    "orderConfirmationMsg": MessageLookupByLibrary.simpleMessage(
      "Ви впевнені, що хочете створити замовлення?",
    ),
    "orderDate": MessageLookupByLibrary.simpleMessage("Дата замовлення"),
    "orderDetail": MessageLookupByLibrary.simpleMessage("Деталі замовлення"),
    "orderHistory": MessageLookupByLibrary.simpleMessage("Історія замовлень"),
    "orderId": MessageLookupByLibrary.simpleMessage("Номер замовлення:"),
    "orderIdWithoutColon": MessageLookupByLibrary.simpleMessage(
      "Номер замовлення",
    ),
    "orderNo": MessageLookupByLibrary.simpleMessage("Замовлення №"),
    "orderNotes": MessageLookupByLibrary.simpleMessage(
      "Примітки до замовлення",
    ),
    "orderNumber": MessageLookupByLibrary.simpleMessage("Номер замовлення"),
    "orderStatusCanceledReversal": MessageLookupByLibrary.simpleMessage(
      "Скасовано повернення",
    ),
    "orderStatusCancelled": MessageLookupByLibrary.simpleMessage("Скасовано"),
    "orderStatusChargeBack": MessageLookupByLibrary.simpleMessage(
      "Повернення коштів",
    ),
    "orderStatusCompleted": MessageLookupByLibrary.simpleMessage("Завершено"),
    "orderStatusDenied": MessageLookupByLibrary.simpleMessage("Відхилено"),
    "orderStatusExpired": MessageLookupByLibrary.simpleMessage(
      "Термін дії закінчився",
    ),
    "orderStatusFailed": MessageLookupByLibrary.simpleMessage("Не вдалося"),
    "orderStatusOnHold": MessageLookupByLibrary.simpleMessage("На утриманні"),
    "orderStatusPending": MessageLookupByLibrary.simpleMessage("В очікуванні"),
    "orderStatusPendingPayment": MessageLookupByLibrary.simpleMessage(
      "Очікує оплати",
    ),
    "orderStatusProcessed": MessageLookupByLibrary.simpleMessage("Оброблено"),
    "orderStatusProcessing": MessageLookupByLibrary.simpleMessage("Обробка"),
    "orderStatusRefunded": MessageLookupByLibrary.simpleMessage("Повернуто"),
    "orderStatusReversed": MessageLookupByLibrary.simpleMessage("Повернуто"),
    "orderStatusShipped": MessageLookupByLibrary.simpleMessage("Відправлено"),
    "orderStatusVoided": MessageLookupByLibrary.simpleMessage("Анульовано"),
    "orderSuccessMsg1": MessageLookupByLibrary.simpleMessage(
      "Ви можете перевірити стан свого замовлення за допомогою нашої функції відстеження статусу доставки. Ви отримаєте електронного листа з підтвердженням замовлення з деталями замовлення та посиланням для відстеження його виконання.",
    ),
    "orderSuccessMsg2": MessageLookupByLibrary.simpleMessage(
      "Ви можете увійти до свого облікового запису за допомогою електронної пошти та пароля, визначених раніше. У своєму обліковому записі ви можете редагувати дані профілю, перевіряти історію транзакцій, редагувати підписку на розсилку.",
    ),
    "orderSuccessTitle1": MessageLookupByLibrary.simpleMessage(
      "Ви успішно розмістили замовлення",
    ),
    "orderSuccessTitle2": MessageLookupByLibrary.simpleMessage(
      "Ваш обліковий запис",
    ),
    "orderSummary": MessageLookupByLibrary.simpleMessage("Підсумок замовлення"),
    "orderTotal": MessageLookupByLibrary.simpleMessage(
      "Загальна сума замовлення",
    ),
    "orderTracking": MessageLookupByLibrary.simpleMessage(
      "Відстеження замовлення",
    ),
    "orders": MessageLookupByLibrary.simpleMessage("Замовлення"),
    "otpVerification": MessageLookupByLibrary.simpleMessage("Перевірка OTP"),
    "ourBankDetails": MessageLookupByLibrary.simpleMessage(
      "Наші банківські реквізити",
    ),
    "outOfStock": MessageLookupByLibrary.simpleMessage("Немає в наявності"),
    "pageView": MessageLookupByLibrary.simpleMessage("Перегляд сторінки"),
    "paid": MessageLookupByLibrary.simpleMessage("Оплачено"),
    "paidStatus": MessageLookupByLibrary.simpleMessage("Статус оплати"),
    "password": MessageLookupByLibrary.simpleMessage("Пароль"),
    "passwordIsRequired": MessageLookupByLibrary.simpleMessage(
      "Пароль обов\'язковий",
    ),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "Паролі не співпадають",
    ),
    "pasteYourImageUrl": MessageLookupByLibrary.simpleMessage(
      "Вставте URL-адресу зображення",
    ),
    "payByWallet": MessageLookupByLibrary.simpleMessage("Оплата гаманцем"),
    "payNow": MessageLookupByLibrary.simpleMessage("Оплатити зараз"),
    "payWithAmount": m43,
    "payment": MessageLookupByLibrary.simpleMessage("Оплата"),
    "paymentDetailsChangedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Платіжні дані успішно змінено",
    ),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("Спосіб оплати"),
    "paymentMethodIsNotSupported": MessageLookupByLibrary.simpleMessage(
      "Цей спосіб оплати не підтримується",
    ),
    "paymentMethods": MessageLookupByLibrary.simpleMessage("Способи оплати"),
    "paymentSettings": MessageLookupByLibrary.simpleMessage(
      "Налаштування оплати",
    ),
    "paymentSuccessful": MessageLookupByLibrary.simpleMessage("Оплата успішна"),
    "pending": MessageLookupByLibrary.simpleMessage("В очікуванні"),
    "pendingReviews": MessageLookupByLibrary.simpleMessage(
      "Очікують на розгляд",
    ),
    "persian": MessageLookupByLibrary.simpleMessage("Перська"),
    "phone": MessageLookupByLibrary.simpleMessage("Телефон"),
    "phoneEmpty": MessageLookupByLibrary.simpleMessage("Телефон не вказано"),
    "phoneHintFormat": MessageLookupByLibrary.simpleMessage(
      "Формат: +380123456789",
    ),
    "phoneIsRequired": MessageLookupByLibrary.simpleMessage(
      "Поле телефону обов\'язкове",
    ),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("Номер телефону"),
    "phoneNumberVerification": MessageLookupByLibrary.simpleMessage(
      "Перевірка номера телефону",
    ),
    "pickADate": MessageLookupByLibrary.simpleMessage("Виберіть дату та час"),
    "pickVoucherToApply": MessageLookupByLibrary.simpleMessage(
      "Виберіть ваучер для застосування.",
    ),
    "picking": MessageLookupByLibrary.simpleMessage("КОМПЛЕКТУЄТЬСЯ"),
    "placeMyOrder": MessageLookupByLibrary.simpleMessage(
      "Розмістити замовлення",
    ),
    "platinum": MessageLookupByLibrary.simpleMessage("Платина"),
    "platinumPriority": MessageLookupByLibrary.simpleMessage(
      "Платиновий пріоритет",
    ),
    "playAll": MessageLookupByLibrary.simpleMessage("Відтворити все"),
    "pleaseAddPrice": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, додайте ціну",
    ),
    "pleaseAgreeTerms": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, погодьтеся з нашими умовами",
    ),
    "pleaseAllowAccessCameraGallery": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, надайте доступ до камери та галереї",
    ),
    "pleaseCheckInternet": MessageLookupByLibrary.simpleMessage(
      "Перевірте підключення до Інтернету!",
    ),
    "pleaseChooseBranch": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, оберіть філію",
    ),
    "pleaseChooseCategory": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, виберіть категорію",
    ),
    "pleaseEnterProductName": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть назву товару",
    ),
    "pleaseFillCode": MessageLookupByLibrary.simpleMessage("Введіть код"),
    "pleaseFillUpAllCellsProperly": MessageLookupByLibrary.simpleMessage(
      "* Будь ласка, правильно заповніть всі поля",
    ),
    "pleaseIncreaseOrDecreaseTheQuantity": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, збільште або зменште кількість, щоб продовжити",
    ),
    "pleaseInput": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, заповніть всі поля",
    ),
    "pleaseInputFillAllFields": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, заповніть всі поля",
    ),
    "pleaseSelectADate": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, виберіть дату бронювання",
    ),
    "pleaseSelectALocation": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, виберіть місцезнаходження",
    ),
    "pleaseSelectAllAttributes": MessageLookupByLibrary.simpleMessage(
      "Виберіть варіант для кожного атрибута товару",
    ),
    "pleaseSelectAttr": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, виберіть принаймні 1 параметр для кожного активного атрибута",
    ),
    "pleaseSelectImages": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, виберіть зображення",
    ),
    "pleaseSelectRequiredOptions": MessageLookupByLibrary.simpleMessage(
      "Виберіть необхідні опції!",
    ),
    "pleaseSignInBeforeUploading": MessageLookupByLibrary.simpleMessage(
      "Увійдіть до свого облікового запису перед завантаженням файлів",
    ),
    "point": MessageLookupByLibrary.simpleMessage("Бали"),
    "pointHistory": MessageLookupByLibrary.simpleMessage("Історія очок"),
    "pointMsgConfigNotFound": MessageLookupByLibrary.simpleMessage(
      "На сервері не знайдено конфігурації точок знижки",
    ),
    "pointMsgEnter": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, введіть точку знижки",
    ),
    "pointMsgMaximumDiscountPoint": MessageLookupByLibrary.simpleMessage(
      "Максимальна точка знижки",
    ),
    "pointMsgNotEnough": MessageLookupByLibrary.simpleMessage(
      "У вас недостатньо точок знижки. Ваша загальна точка знижки",
    ),
    "pointMsgOverMaximumDiscountPoint": MessageLookupByLibrary.simpleMessage(
      "Ви досягли максимальної точки знижки",
    ),
    "pointMsgOverTotalBill": MessageLookupByLibrary.simpleMessage(
      "Загальна вартість знижки перевищує суму рахунку",
    ),
    "pointMsgRemove": MessageLookupByLibrary.simpleMessage(
      "Точку знижки видалено",
    ),
    "pointMsgSuccess": MessageLookupByLibrary.simpleMessage(
      "Точку знижки успішно застосовано",
    ),
    "pointRewardMessage": MessageLookupByLibrary.simpleMessage(
      "Існує правило знижки для застосування балів до кошика",
    ),
    "points": MessageLookupByLibrary.simpleMessage("Очки"),
    "pointsAddedMsg": MessageLookupByLibrary.simpleMessage(
      "Бали додано на рахунок користувача.",
    ),
    "pointsAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Бали успішно додано",
    ),
    "pointsRedeemedMsg": MessageLookupByLibrary.simpleMessage(
      "Бали було списано з облікового запису користувача.",
    ),
    "pointsRedeemedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Бали успішно використано",
    ),
    "polish": MessageLookupByLibrary.simpleMessage("Польська"),
    "poor": MessageLookupByLibrary.simpleMessage("Погано"),
    "popular": MessageLookupByLibrary.simpleMessage("Популярні"),
    "popularity": MessageLookupByLibrary.simpleMessage("Популярність"),
    "posAddressToolTip": MessageLookupByLibrary.simpleMessage(
      "Ця адреса буде збережена на вашому локальному пристрої. Це НЕ адреса користувача",
    ),
    "postContent": MessageLookupByLibrary.simpleMessage("Вміст"),
    "postFail": MessageLookupByLibrary.simpleMessage(
      "Не вдалося створити ваш допис",
    ),
    "postImageFeature": MessageLookupByLibrary.simpleMessage(
      "Зображення допису",
    ),
    "postManagement": MessageLookupByLibrary.simpleMessage(
      "Керування дописами",
    ),
    "postProduct": MessageLookupByLibrary.simpleMessage("Опублікувати товар"),
    "postSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Ваш допис успішно створено",
    ),
    "postTitle": MessageLookupByLibrary.simpleMessage("Заголовок"),
    "prepaid": MessageLookupByLibrary.simpleMessage("Передплачено"),
    "prev": MessageLookupByLibrary.simpleMessage("Попередній"),
    "preview": MessageLookupByLibrary.simpleMessage("Попередній перегляд"),
    "price": MessageLookupByLibrary.simpleMessage("Ціна"),
    "priceHighToLow": MessageLookupByLibrary.simpleMessage(
      "Ціна: від високої до низької",
    ),
    "priceLowToHigh": MessageLookupByLibrary.simpleMessage(
      "Ціна: від низької до високої",
    ),
    "prices": MessageLookupByLibrary.simpleMessage("Ціни"),
    "printReceipt": MessageLookupByLibrary.simpleMessage("Друк чека"),
    "printer": MessageLookupByLibrary.simpleMessage("Принтер"),
    "printerNotFound": MessageLookupByLibrary.simpleMessage(
      "Принтер не знайдено",
    ),
    "printerSelection": MessageLookupByLibrary.simpleMessage("Вибір принтера"),
    "printing": MessageLookupByLibrary.simpleMessage("Друк..."),
    "privacyAndTerm": MessageLookupByLibrary.simpleMessage(
      "Конфіденційність та умови",
    ),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage(
      "Політика конфіденційності",
    ),
    "privacyTerms": MessageLookupByLibrary.simpleMessage(
      "Конфіденційність і умови",
    ),
    "private": MessageLookupByLibrary.simpleMessage("Приватний"),
    "processing": MessageLookupByLibrary.simpleMessage("Обробка..."),
    "product": MessageLookupByLibrary.simpleMessage("Товар"),
    "productAddToCart": m44,
    "productAdded": MessageLookupByLibrary.simpleMessage("Товар додано"),
    "productCreateReview": MessageLookupByLibrary.simpleMessage(
      "Ваш товар з\'явиться після перевірки",
    ),
    "productExpired": MessageLookupByLibrary.simpleMessage(
      "На жаль, цей товар недоступний, оскільки термін його дії закінчився",
    ),
    "productName": MessageLookupByLibrary.simpleMessage("Назва товару"),
    "productNameCanNotEmpty": MessageLookupByLibrary.simpleMessage(
      "Назва товару не може бути порожньою",
    ),
    "productNeedAtLeastOneVariation": MessageLookupByLibrary.simpleMessage(
      "Змінна типу продукту потребує принаймні одного варіанту",
    ),
    "productNeedNameAndPrice": MessageLookupByLibrary.simpleMessage(
      "Простий тип продукту потребує назви та звичайної ціни",
    ),
    "productOutOfStock": MessageLookupByLibrary.simpleMessage(
      "Товар відсутній на складі",
    ),
    "productOverview": MessageLookupByLibrary.simpleMessage("Огляд товару"),
    "productRating": MessageLookupByLibrary.simpleMessage("Ваша оцінка"),
    "productReview": MessageLookupByLibrary.simpleMessage("Відгук про товар"),
    "productType": MessageLookupByLibrary.simpleMessage("Тип товару"),
    "products": MessageLookupByLibrary.simpleMessage("Товари"),
    "promptPayID": MessageLookupByLibrary.simpleMessage("ID PromptPay:"),
    "promptPayName": MessageLookupByLibrary.simpleMessage("Назва PromptPay:"),
    "promptPayType": MessageLookupByLibrary.simpleMessage("Тип PromptPay:"),
    "publish": MessageLookupByLibrary.simpleMessage("Опублікувати"),
    "pullToLoadMore": MessageLookupByLibrary.simpleMessage(
      "Потягніть, щоб завантажити більше",
    ),
    "pullToRefresh": MessageLookupByLibrary.simpleMessage(
      "Потягніть, щоб оновити",
    ),
    "pullUpLoad": MessageLookupByLibrary.simpleMessage(
      "Потягніть угору, щоб завантажити більше",
    ),
    "qRCodeMsgSuccess": MessageLookupByLibrary.simpleMessage(
      "QR-код успішно збережено",
    ),
    "qRCodeSaveFailure": MessageLookupByLibrary.simpleMessage(
      "Не вдалося зберегти QR-код",
    ),
    "qty": MessageLookupByLibrary.simpleMessage("Кількість"),
    "qtyTotal": m45,
    "quantity": MessageLookupByLibrary.simpleMessage("Кількість"),
    "quantityProductExceedInStock": MessageLookupByLibrary.simpleMessage(
      "Поточна кількість перевищує наявну на складі",
    ),
    "random": MessageLookupByLibrary.simpleMessage("Випадково"),
    "rankDetails": MessageLookupByLibrary.simpleMessage("Деталі рангу"),
    "rankDetailsMsg": MessageLookupByLibrary.simpleMessage(
      "Ви зараз є членом цього рангу",
    ),
    "rate": MessageLookupByLibrary.simpleMessage("Оцінити"),
    "rateProduct": MessageLookupByLibrary.simpleMessage("Оцінити товар"),
    "rateTheApp": MessageLookupByLibrary.simpleMessage("Оцініть програму"),
    "rateThisApp": MessageLookupByLibrary.simpleMessage("Оцініть цю програму"),
    "rateThisAppDescription": MessageLookupByLibrary.simpleMessage(
      "Якщо вам подобається ця програма, будь ласка, оцініть її!\nЦе дійсно допомагає нам, і це не займе більше хвилини.",
    ),
    "rating": MessageLookupByLibrary.simpleMessage("Рейтинг"),
    "ratingFirst": MessageLookupByLibrary.simpleMessage(
      "Оцініть перед тим, як надіслати коментар",
    ),
    "reOrder": MessageLookupByLibrary.simpleMessage("Повторне замовлення"),
    "readReviews": MessageLookupByLibrary.simpleMessage("Відгуки"),
    "readyToPick": MessageLookupByLibrary.simpleMessage("ГОТОВО ДО ВИДАЧІ"),
    "received": MessageLookupByLibrary.simpleMessage("Отримано"),
    "receivedMoney": MessageLookupByLibrary.simpleMessage("Отримані кошти"),
    "receivedMoneyFrom": m46,
    "receiver": MessageLookupByLibrary.simpleMessage("Одержувач"),
    "recent": MessageLookupByLibrary.simpleMessage("Нещодавні"),
    "recentSearches": MessageLookupByLibrary.simpleMessage("Історія"),
    "recentView": MessageLookupByLibrary.simpleMessage("Нещодавно переглянуті"),
    "recentlyViewed": MessageLookupByLibrary.simpleMessage(
      "Нещодавно переглянуті",
    ),
    "recommended": MessageLookupByLibrary.simpleMessage("Рекомендовано"),
    "recurringTotals": MessageLookupByLibrary.simpleMessage(
      "Періодичні підсумки",
    ),
    "redeem": MessageLookupByLibrary.simpleMessage("Викупити"),
    "redeemPoints": MessageLookupByLibrary.simpleMessage("Обміняти бали"),
    "redeemRewards": MessageLookupByLibrary.simpleMessage(
      "Активувати винагороди",
    ),
    "redeemed": MessageLookupByLibrary.simpleMessage("Викуплений"),
    "refresh": MessageLookupByLibrary.simpleMessage("Оновити"),
    "refreshCompleted": MessageLookupByLibrary.simpleMessage(
      "Оновлення завершено",
    ),
    "refreshing": MessageLookupByLibrary.simpleMessage("Освіжає..."),
    "refund": MessageLookupByLibrary.simpleMessage("Повернення коштів"),
    "refundOrderFailed": MessageLookupByLibrary.simpleMessage(
      "Запит на повернення коштів за замовлення не вдався",
    ),
    "refundOrderSuccess": MessageLookupByLibrary.simpleMessage(
      "Запит на повернення коштів за замовлення успішно надіслано!",
    ),
    "refundRequest": MessageLookupByLibrary.simpleMessage(
      "Запит на повернення коштів",
    ),
    "refundRequested": MessageLookupByLibrary.simpleMessage(
      "Запит на повернення коштів",
    ),
    "refunds": MessageLookupByLibrary.simpleMessage("Повернення коштів"),
    "regenerateResponse": MessageLookupByLibrary.simpleMessage(
      "Згенерувати відповідь знову",
    ),
    "registerAs": MessageLookupByLibrary.simpleMessage("Зареєструватися як"),
    "registerAsVendor": MessageLookupByLibrary.simpleMessage(
      "Зареєструватися як продавець",
    ),
    "registerErrorSyncAccount": MessageLookupByLibrary.simpleMessage(
      "Не вдалося синхронізувати обліковий запис. Будь ласка, увійдіть, щоб продовжити",
    ),
    "registerFailed": MessageLookupByLibrary.simpleMessage(
      "Помилка реєстрації",
    ),
    "registerInvalid": MessageLookupByLibrary.simpleMessage(
      "Недійсний або прострочений запит. Спробуйте ще раз",
    ),
    "registerSuccess": MessageLookupByLibrary.simpleMessage(
      "Реєстрація успішна",
    ),
    "regularPrice": MessageLookupByLibrary.simpleMessage("Звичайна ціна"),
    "relatedLayoutTitle": MessageLookupByLibrary.simpleMessage(
      "Те, що вам може сподобатися",
    ),
    "releaseToLoadMore": MessageLookupByLibrary.simpleMessage(
      "Відпустіть, щоб завантажити більше",
    ),
    "releaseToRefresh": MessageLookupByLibrary.simpleMessage(
      "Відпустіть для оновлення",
    ),
    "remainingAmountCod": MessageLookupByLibrary.simpleMessage(
      "Залишок суми до сплати при доставці",
    ),
    "remove": MessageLookupByLibrary.simpleMessage("Видалити"),
    "removeFromWishList": MessageLookupByLibrary.simpleMessage(
      "Видалити зі списку бажань",
    ),
    "removeWishlist": MessageLookupByLibrary.simpleMessage(
      "Видалити зі списку бажань",
    ),
    "removeWishlistContent": m47,
    "requestBooking": MessageLookupByLibrary.simpleMessage(
      "Запит на бронювання",
    ),
    "requestTooMany": MessageLookupByLibrary.simpleMessage(
      "Ви запитали забагато кодів за короткий час. Будь ласка, спробуйте пізніше",
    ),
    "resend": MessageLookupByLibrary.simpleMessage("НАДІСЛАТИ ПОВТОРНО"),
    "reservePrice": MessageLookupByLibrary.simpleMessage("Резервна ціна"),
    "reset": MessageLookupByLibrary.simpleMessage("Скинути"),
    "resetPassword": MessageLookupByLibrary.simpleMessage("Скинути пароль"),
    "resetYourPassword": MessageLookupByLibrary.simpleMessage("Скинути пароль"),
    "results": MessageLookupByLibrary.simpleMessage("Результати"),
    "retry": MessageLookupByLibrary.simpleMessage("Повторити"),
    "reverse": MessageLookupByLibrary.simpleMessage("ЗВОРОТНИЙ"),
    "review": MessageLookupByLibrary.simpleMessage("Попередній перегляд"),
    "reviewApproval": MessageLookupByLibrary.simpleMessage("Перевірка відгуку"),
    "reviewPendingApproval": MessageLookupByLibrary.simpleMessage(
      "Ваш відгук надіслано та очікує схвалення!",
    ),
    "reviewSent": MessageLookupByLibrary.simpleMessage("Ваш відгук надіслано!"),
    "reviews": MessageLookupByLibrary.simpleMessage("Відгуки"),
    "rewards": MessageLookupByLibrary.simpleMessage("Нагороди"),
    "romanian": MessageLookupByLibrary.simpleMessage("Румунська"),
    "russian": MessageLookupByLibrary.simpleMessage("Російська"),
    "sale": m48,
    "salePrice": MessageLookupByLibrary.simpleMessage("Ціна зі знижкою"),
    "saturday": MessageLookupByLibrary.simpleMessage("Субота"),
    "save": MessageLookupByLibrary.simpleMessage("Зберегти"),
    "saveAddress": MessageLookupByLibrary.simpleMessage("Зберегти адресу"),
    "saveAddressSuccess": MessageLookupByLibrary.simpleMessage(
      "Вашу адресу збережено",
    ),
    "saveForLater": MessageLookupByLibrary.simpleMessage("Зберегти на потім"),
    "saveQRCode": MessageLookupByLibrary.simpleMessage("Зберегти QR-код"),
    "saveToWishList": MessageLookupByLibrary.simpleMessage(
      "Зберегти до списку бажань",
    ),
    "scanPoints": MessageLookupByLibrary.simpleMessage("Точки сканування"),
    "scanQRCode": MessageLookupByLibrary.simpleMessage("Відскануйте QR-код"),
    "scannerCannotScan": MessageLookupByLibrary.simpleMessage(
      "Цей елемент неможливо відсканувати",
    ),
    "scannerLoginFirst": MessageLookupByLibrary.simpleMessage(
      "Щоб сканувати замовлення, потрібно спочатку увійти",
    ),
    "scannerOrderAvailable": MessageLookupByLibrary.simpleMessage(
      "Це замовлення недоступне для вашого облікового запису",
    ),
    "search": MessageLookupByLibrary.simpleMessage("Пошук"),
    "searchByCountryNameOrDialCode": MessageLookupByLibrary.simpleMessage(
      "Пошук за назвою країни або телефонним кодом",
    ),
    "searchByName": MessageLookupByLibrary.simpleMessage("Пошук за ім\'ям..."),
    "searchEmptyDataMessage": MessageLookupByLibrary.simpleMessage(
      "Ой! На жаль, немає результатів, що відповідають вашому пошуку",
    ),
    "searchForItems": MessageLookupByLibrary.simpleMessage("Пошук товарів"),
    "searchInput": MessageLookupByLibrary.simpleMessage(
      "Введіть текст для пошуку",
    ),
    "searchOrderId": MessageLookupByLibrary.simpleMessage(
      "Пошук за номером замовлення...",
    ),
    "searchPlace": MessageLookupByLibrary.simpleMessage("Пошук місця"),
    "searchResultFor": m49,
    "searchResultItem": m50,
    "searchResultItems": m51,
    "searchingAddress": MessageLookupByLibrary.simpleMessage("Пошук адреси"),
    "secondsAgo": m52,
    "seeAll": MessageLookupByLibrary.simpleMessage("Переглянути все"),
    "seeNewAppConfig": MessageLookupByLibrary.simpleMessage(
      "Продовжуйте переглядати новий вміст у своїй програмі",
    ),
    "seeOrder": MessageLookupByLibrary.simpleMessage("Переглянути замовлення"),
    "seeReviews": MessageLookupByLibrary.simpleMessage("Переглянути відгуки"),
    "select": MessageLookupByLibrary.simpleMessage("Вибрати"),
    "selectAddress": MessageLookupByLibrary.simpleMessage("Виберіть адресу"),
    "selectAll": MessageLookupByLibrary.simpleMessage("Вибрати все"),
    "selectDate": MessageLookupByLibrary.simpleMessage("Виберіть дату"),
    "selectDates": MessageLookupByLibrary.simpleMessage("Виберіть дати"),
    "selectFileCancelled": MessageLookupByLibrary.simpleMessage(
      "Вибір файлу скасовано!",
    ),
    "selectImage": MessageLookupByLibrary.simpleMessage("Виберіть зображення"),
    "selectItem": MessageLookupByLibrary.simpleMessage("Оберіть товар"),
    "selectNone": MessageLookupByLibrary.simpleMessage("Не вибирати"),
    "selectPrinter": MessageLookupByLibrary.simpleMessage("Вибрати принтер"),
    "selectRole": MessageLookupByLibrary.simpleMessage("Оберіть роль"),
    "selectStore": MessageLookupByLibrary.simpleMessage("Обрати магазин"),
    "selectTheColor": MessageLookupByLibrary.simpleMessage("Виберіть колір"),
    "selectTheFile": MessageLookupByLibrary.simpleMessage("Обрати файл"),
    "selectThePoint": MessageLookupByLibrary.simpleMessage("Виберіть бали"),
    "selectTheQuantity": MessageLookupByLibrary.simpleMessage(
      "Виберіть кількість",
    ),
    "selectTheSize": MessageLookupByLibrary.simpleMessage("Виберіть розмір"),
    "selectType": MessageLookupByLibrary.simpleMessage("Виберіть тип"),
    "selectVoucher": MessageLookupByLibrary.simpleMessage("Обрати ваучер"),
    "send": MessageLookupByLibrary.simpleMessage("Надіслати"),
    "sendBack": MessageLookupByLibrary.simpleMessage("Повернути"),
    "sendSMSCode": MessageLookupByLibrary.simpleMessage("Отримати код"),
    "sendSMStoVendor": MessageLookupByLibrary.simpleMessage(
      "Надіслати SMS продавцю",
    ),
    "sendTo": MessageLookupByLibrary.simpleMessage(
      "Обліковий запис для переказу (електронна пошта)",
    ),
    "separateMultipleEmailWithComma": MessageLookupByLibrary.simpleMessage(
      "Розділяйте кілька адрес комами",
    ),
    "serbian": MessageLookupByLibrary.simpleMessage("Сербська"),
    "sessionExpired": MessageLookupByLibrary.simpleMessage("Сеанс закінчився"),
    "setAnAddressInSettingPage": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, вкажіть адресу в налаштуваннях",
    ),
    "settings": MessageLookupByLibrary.simpleMessage("Налаштування"),
    "setup": MessageLookupByLibrary.simpleMessage("Налаштування"),
    "share": MessageLookupByLibrary.simpleMessage("Поділитися"),
    "shareProductData": MessageLookupByLibrary.simpleMessage(
      "Поділитися даними про товар",
    ),
    "shareProductLink": MessageLookupByLibrary.simpleMessage(
      "Поділитися посиланням на товар",
    ),
    "shipped": MessageLookupByLibrary.simpleMessage("Відправлено"),
    "shipping": MessageLookupByLibrary.simpleMessage("Доставка"),
    "shippingAddress": MessageLookupByLibrary.simpleMessage("Адреса доставки"),
    "shippingFee": MessageLookupByLibrary.simpleMessage("Вартість доставки"),
    "shippingMethod": MessageLookupByLibrary.simpleMessage("Спосіб доставки"),
    "shop": MessageLookupByLibrary.simpleMessage("Магазин"),
    "shopEmail": MessageLookupByLibrary.simpleMessage(
      "Електронна пошта магазину",
    ),
    "shopName": MessageLookupByLibrary.simpleMessage("Назва магазину"),
    "shopOrders": MessageLookupByLibrary.simpleMessage("Замовлення в магазині"),
    "shopPhone": MessageLookupByLibrary.simpleMessage("Телефон магазину"),
    "shopSlug": MessageLookupByLibrary.simpleMessage("Ідентифікатор магазину"),
    "shopifyCustomerAccountLoginDescription": MessageLookupByLibrary.simpleMessage(
      "Використовуйте свій обліковий запис Shopify для входу та доступу до своїх замовлень, адрес тощо.",
    ),
    "shopifyCustomerAccountLoginTitle": MessageLookupByLibrary.simpleMessage(
      "Увійти за допомогою облікового запису клієнта Shopify",
    ),
    "shoppingCartItems": m53,
    "shortDescription": MessageLookupByLibrary.simpleMessage("Короткий опис"),
    "showAllMyOrdered": MessageLookupByLibrary.simpleMessage(
      "Показати всі мої замовлення",
    ),
    "showDetails": MessageLookupByLibrary.simpleMessage("Показати деталі"),
    "showGallery": MessageLookupByLibrary.simpleMessage("Показати галерею"),
    "showLess": MessageLookupByLibrary.simpleMessage("Показати менше"),
    "showMore": MessageLookupByLibrary.simpleMessage("Показати більше"),
    "signIn": MessageLookupByLibrary.simpleMessage("Увійти"),
    "signInWithEmail": MessageLookupByLibrary.simpleMessage(
      "Увійти через електронну пошту",
    ),
    "signUp": MessageLookupByLibrary.simpleMessage("Зареєструватися"),
    "signup": MessageLookupByLibrary.simpleMessage("Зареєструватися"),
    "silver": MessageLookupByLibrary.simpleMessage("Срібло"),
    "silverPriority": MessageLookupByLibrary.simpleMessage("Срібний пріоритет"),
    "simple": MessageLookupByLibrary.simpleMessage("Простий"),
    "simpleList": MessageLookupByLibrary.simpleMessage("Простий список"),
    "size": MessageLookupByLibrary.simpleMessage("Розмір"),
    "sizeGuide": MessageLookupByLibrary.simpleMessage("Розмірна сітка"),
    "skip": MessageLookupByLibrary.simpleMessage("Пропустити"),
    "sku": MessageLookupByLibrary.simpleMessage("SKU"),
    "slovak": MessageLookupByLibrary.simpleMessage("Словацька"),
    "smsCodeExpired": MessageLookupByLibrary.simpleMessage(
      "Термін дії SMS-коду закінчився. Будь ласка, надішліть код підтвердження ще раз, щоб повторити спробу",
    ),
    "sold": m54,
    "soldBy": MessageLookupByLibrary.simpleMessage("Продавець"),
    "somethingWrong": MessageLookupByLibrary.simpleMessage(
      "Щось пішло не так. Будь ласка, спробуйте пізніше",
    ),
    "sortBy": MessageLookupByLibrary.simpleMessage("Сортувати за"),
    "sortCode": MessageLookupByLibrary.simpleMessage("Код сортування"),
    "spanish": MessageLookupByLibrary.simpleMessage("Іспанська"),
    "speechNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Мовлення недоступне",
    ),
    "spendAtLeast": MessageLookupByLibrary.simpleMessage(
      "Витратити щонайменше",
    ),
    "startExploring": MessageLookupByLibrary.simpleMessage("Почати перегляд"),
    "startPrice": MessageLookupByLibrary.simpleMessage("Початкова ціна"),
    "startShopping": MessageLookupByLibrary.simpleMessage("Почати покупки"),
    "startingBid": MessageLookupByLibrary.simpleMessage("Початкова ставка"),
    "state": MessageLookupByLibrary.simpleMessage("Область"),
    "stateIsRequired": MessageLookupByLibrary.simpleMessage(
      "Поле області обов\'язкове",
    ),
    "stateProvince": MessageLookupByLibrary.simpleMessage("Область/провінція"),
    "status": MessageLookupByLibrary.simpleMessage("Статус"),
    "stock": MessageLookupByLibrary.simpleMessage("Запас"),
    "stockQuantity": MessageLookupByLibrary.simpleMessage(
      "Кількість на складі",
    ),
    "stop": MessageLookupByLibrary.simpleMessage("Стоп"),
    "store": MessageLookupByLibrary.simpleMessage("Магазин"),
    "storeAddress": MessageLookupByLibrary.simpleMessage("Адреса магазину"),
    "storeBanner": MessageLookupByLibrary.simpleMessage("Банер"),
    "storeBrand": MessageLookupByLibrary.simpleMessage("Бренд магазину"),
    "storeClosed": MessageLookupByLibrary.simpleMessage(
      "Магазин зараз зачинено",
    ),
    "storeEmail": MessageLookupByLibrary.simpleMessage("Email магазину"),
    "storeInformation": MessageLookupByLibrary.simpleMessage(
      "Інформація про магазин",
    ),
    "storeListBanner": MessageLookupByLibrary.simpleMessage(
      "Банер списку магазинів",
    ),
    "storeLocation": MessageLookupByLibrary.simpleMessage(
      "Розташування магазину",
    ),
    "storeLocatorSearchPlaceholder": MessageLookupByLibrary.simpleMessage(
      "Введіть адресу або місто",
    ),
    "storeLogo": MessageLookupByLibrary.simpleMessage("Логотип магазину"),
    "storeMobileBanner": MessageLookupByLibrary.simpleMessage(
      "Мобільний банер магазину",
    ),
    "storeSettings": MessageLookupByLibrary.simpleMessage(
      "Налаштування магазину",
    ),
    "storeSliderBanner": MessageLookupByLibrary.simpleMessage(
      "Слайдер-банер магазину",
    ),
    "storeStaticBanner": MessageLookupByLibrary.simpleMessage(
      "Статичний банер магазину",
    ),
    "storeVacation": MessageLookupByLibrary.simpleMessage("Відпустка магазину"),
    "stores": MessageLookupByLibrary.simpleMessage("Магазини"),
    "street": MessageLookupByLibrary.simpleMessage("Вулиця"),
    "street2": MessageLookupByLibrary.simpleMessage("Вулиця 2"),
    "streetIsRequired": MessageLookupByLibrary.simpleMessage(
      "Поле назви вулиці обов\'язкове",
    ),
    "streetName": MessageLookupByLibrary.simpleMessage("Назва вулиці"),
    "streetNameApartment": MessageLookupByLibrary.simpleMessage("Квартира"),
    "streetNameBlock": MessageLookupByLibrary.simpleMessage("Блок"),
    "subTitleOrderConfirmed": MessageLookupByLibrary.simpleMessage(
      "Дякуємо за ваше замовлення. Ми швидко обробляємо його. Незабаром ви отримаєте електронного листа з підтвердженням",
    ),
    "submit": MessageLookupByLibrary.simpleMessage("Надіслати"),
    "submitYourPost": MessageLookupByLibrary.simpleMessage(
      "Надіслати ваш допис",
    ),
    "subtotal": MessageLookupByLibrary.simpleMessage("Проміжний підсумок"),
    "successful": MessageLookupByLibrary.simpleMessage("Успішний"),
    "sunday": MessageLookupByLibrary.simpleMessage("Неділя"),
    "support": MessageLookupByLibrary.simpleMessage("Підтримка"),
    "swahili": MessageLookupByLibrary.simpleMessage("Суахілі"),
    "swedish": MessageLookupByLibrary.simpleMessage("Шведська"),
    "systemError": MessageLookupByLibrary.simpleMessage(
      "Системна помилка. Зверніться до служби підтримки.",
    ),
    "tag": MessageLookupByLibrary.simpleMessage("Тег"),
    "tagNotExist": MessageLookupByLibrary.simpleMessage("Цей тег не існує"),
    "tags": MessageLookupByLibrary.simpleMessage("Теги"),
    "takePicture": MessageLookupByLibrary.simpleMessage("Зробити фото"),
    "tamil": MessageLookupByLibrary.simpleMessage("Тамільська"),
    "tapSelectLocation": MessageLookupByLibrary.simpleMessage(
      "Торкніться, щоб вибрати це місце",
    ),
    "tapTheMicToTalk": MessageLookupByLibrary.simpleMessage(
      "Торкніться мікрофона, щоб говорити",
    ),
    "tax": MessageLookupByLibrary.simpleMessage("Податок"),
    "teraWallet": MessageLookupByLibrary.simpleMessage("TeraWallet"),
    "terrible": MessageLookupByLibrary.simpleMessage("Жахливо"),
    "thailand": MessageLookupByLibrary.simpleMessage("Тайська"),
    "theFieldIsRequired": m55,
    "thisDateIsNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Ця дата недоступна",
    ),
    "thisFeatureDoesNotSupportTheCurrentLanguage":
        MessageLookupByLibrary.simpleMessage(
          "Ця функція не підтримує поточну мову",
        ),
    "thisIsCustomerRole": MessageLookupByLibrary.simpleMessage(
      "Це роль клієнта",
    ),
    "thisIsDeliveryrRole": MessageLookupByLibrary.simpleMessage(
      "Це роль доставки",
    ),
    "thisIsVendorRole": MessageLookupByLibrary.simpleMessage(
      "Це роль продавця",
    ),
    "thisItemIsSold": MessageLookupByLibrary.simpleMessage("Цей товар продано"),
    "thisPlatformNotSupportWebview": MessageLookupByLibrary.simpleMessage(
      "Ця платформа не підтримує веб-перегляд",
    ),
    "thisProductNotSupport": MessageLookupByLibrary.simpleMessage(
      "Цей продукт не підтримується",
    ),
    "thursday": MessageLookupByLibrary.simpleMessage("Четвер"),
    "tickets": MessageLookupByLibrary.simpleMessage("Квитки"),
    "time": MessageLookupByLibrary.simpleMessage("Час"),
    "timeLeft": MessageLookupByLibrary.simpleMessage("Час залишився"),
    "title": MessageLookupByLibrary.simpleMessage("Заголовок"),
    "titleAToZ": MessageLookupByLibrary.simpleMessage("Назва: від А до Я"),
    "titleFirst": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, додайте назву",
    ),
    "titleZToA": MessageLookupByLibrary.simpleMessage("Назва: від Я до А"),
    "to": MessageLookupByLibrary.simpleMessage("До"),
    "toRate": MessageLookupByLibrary.simpleMessage("Оцінити"),
    "tooManyFailedLogin": MessageLookupByLibrary.simpleMessage(
      "Забагато невдалих спроб входу. Будь ласка, спробуйте пізніше",
    ),
    "topUp": MessageLookupByLibrary.simpleMessage("Поповнити"),
    "topUpProductNotFound": MessageLookupByLibrary.simpleMessage(
      "Товар для поповнення не знайдено",
    ),
    "total": MessageLookupByLibrary.simpleMessage("Всього"),
    "totalAmount": MessageLookupByLibrary.simpleMessage("Загальна сума"),
    "totalCartValue": MessageLookupByLibrary.simpleMessage(
      "Загальна вартість замовлення повинна бути не менше",
    ),
    "totalPoints": MessageLookupByLibrary.simpleMessage("Всього балів"),
    "totalPrice": MessageLookupByLibrary.simpleMessage("Загальна сума"),
    "totalProducts": m56,
    "totalTax": MessageLookupByLibrary.simpleMessage("Загальний податок"),
    "trackingNumberIs": MessageLookupByLibrary.simpleMessage(
      "Номер відстеження:",
    ),
    "trackingPage": MessageLookupByLibrary.simpleMessage(
      "Сторінка відстеження",
    ),
    "transactionCancelled": MessageLookupByLibrary.simpleMessage(
      "Транзакцію скасовано",
    ),
    "transactionDetail": MessageLookupByLibrary.simpleMessage(
      "Деталі транзакції",
    ),
    "transactionFailded": MessageLookupByLibrary.simpleMessage(
      "Транзакція не вдалася",
    ),
    "transactionFailed": MessageLookupByLibrary.simpleMessage(
      "Транзакція не вдалася",
    ),
    "transactionFee": MessageLookupByLibrary.simpleMessage(
      "Комісія за транзакцію",
    ),
    "transactionResult": MessageLookupByLibrary.simpleMessage(
      "Результат транзакції",
    ),
    "transfer": MessageLookupByLibrary.simpleMessage("Переказ"),
    "transferConfirm": MessageLookupByLibrary.simpleMessage(
      "Підтвердження переказу",
    ),
    "transferErrorMessage": MessageLookupByLibrary.simpleMessage(
      "Ви не можете переказати кошти цьому користувачу",
    ),
    "transferFailed": MessageLookupByLibrary.simpleMessage("Переказ не вдався"),
    "transferMoneyTo": m57,
    "transferSuccess": MessageLookupByLibrary.simpleMessage("Переказ успішний"),
    "tuesday": MessageLookupByLibrary.simpleMessage("Вівторок"),
    "turkish": MessageLookupByLibrary.simpleMessage("Турецька"),
    "turnOnBle": MessageLookupByLibrary.simpleMessage("Увімкніть Bluetooth"),
    "typeAMessage": MessageLookupByLibrary.simpleMessage(
      "Введіть повідомлення...",
    ),
    "typeYourMessage": MessageLookupByLibrary.simpleMessage(
      "Введіть ваше повідомлення тут...",
    ),
    "typing": MessageLookupByLibrary.simpleMessage("Введення..."),
    "ukrainian": MessageLookupByLibrary.simpleMessage("Українська"),
    "unavailable": MessageLookupByLibrary.simpleMessage("Недоступний"),
    "unblock": MessageLookupByLibrary.simpleMessage("Розблокувати"),
    "unblockUser": MessageLookupByLibrary.simpleMessage(
      "Розблокувати користувача",
    ),
    "undo": MessageLookupByLibrary.simpleMessage("Скасувати"),
    "unexpectedError": MessageLookupByLibrary.simpleMessage(
      "Сталася неочікувана помилка. Спробуйте ще раз.",
    ),
    "unknownError": MessageLookupByLibrary.simpleMessage(
      "Щось пішло не так, виникла невідома помилка",
    ),
    "unpaid": MessageLookupByLibrary.simpleMessage("Неоплачено"),
    "upRankNote1": MessageLookupByLibrary.simpleMessage("Заробляйте більше"),
    "upRankNote2": MessageLookupByLibrary.simpleMessage(
      "бали, щоб піднятися на цьому ранзі.",
    ),
    "update": MessageLookupByLibrary.simpleMessage("Оновити"),
    "updateFailed": MessageLookupByLibrary.simpleMessage("Помилка оновлення!"),
    "updateInfo": MessageLookupByLibrary.simpleMessage("Оновити інформацію"),
    "updatePassword": MessageLookupByLibrary.simpleMessage("Оновити пароль"),
    "updateStatus": MessageLookupByLibrary.simpleMessage("Оновити статус"),
    "updateSuccess": MessageLookupByLibrary.simpleMessage("Успішно оновлено!"),
    "updateUserFailed": MessageLookupByLibrary.simpleMessage(
      "Не вдалося оновити користувача",
    ),
    "updateUserInfor": MessageLookupByLibrary.simpleMessage("Оновити профіль"),
    "uploadFile": MessageLookupByLibrary.simpleMessage("Завантажити файл"),
    "uploadImage": MessageLookupByLibrary.simpleMessage(
      "Завантажити зображення",
    ),
    "uploadProduct": MessageLookupByLibrary.simpleMessage("Завантажити товар"),
    "uploading": MessageLookupByLibrary.simpleMessage("Завантаження"),
    "url": MessageLookupByLibrary.simpleMessage("URL"),
    "useAmountPoints": m58,
    "useMaximumPointDiscount": m59,
    "useNow": MessageLookupByLibrary.simpleMessage("Використати зараз"),
    "usePoint": MessageLookupByLibrary.simpleMessage("Використовуйте точку"),
    "useThisImage": MessageLookupByLibrary.simpleMessage(
      "Використовувати це зображення",
    ),
    "used": MessageLookupByLibrary.simpleMessage("Використано"),
    "userExists": MessageLookupByLibrary.simpleMessage(
      "Це ім\'я користувача/електронна адреса вже використовується",
    ),
    "userHasBeenBlocked": MessageLookupByLibrary.simpleMessage(
      "Користувача заблоковано",
    ),
    "userNameInCorrect": MessageLookupByLibrary.simpleMessage(
      "Ім\'я користувача або пароль неправильні",
    ),
    "userNotFound": MessageLookupByLibrary.simpleMessage(
      "Користувача не знайдено",
    ),
    "username": MessageLookupByLibrary.simpleMessage("Ім\'я користувача"),
    "usernameAlreadyInUse": MessageLookupByLibrary.simpleMessage(
      "Це ім\'я користувача вже використовується!",
    ),
    "usernameAndPasswordRequired": MessageLookupByLibrary.simpleMessage(
      "Потрібні ім\'я користувача та пароль",
    ),
    "usernameInvalid": MessageLookupByLibrary.simpleMessage(
      "Недійсне ім\'я користувача",
    ),
    "usernameIsRequired": MessageLookupByLibrary.simpleMessage(
      "Ім\'я користувача обов\'язкове",
    ),
    "vacationMessage": MessageLookupByLibrary.simpleMessage(
      "Повідомлення про відпустку",
    ),
    "vacationType": MessageLookupByLibrary.simpleMessage("Тип відпустки"),
    "validUntil": m60,
    "validUntilDate": m61,
    "variable": MessageLookupByLibrary.simpleMessage("Змінний"),
    "variation": MessageLookupByLibrary.simpleMessage("Варіація"),
    "vendor": MessageLookupByLibrary.simpleMessage("Продавець"),
    "vendorAdmin": MessageLookupByLibrary.simpleMessage(
      "Адміністратор продавця",
    ),
    "vendorInfo": MessageLookupByLibrary.simpleMessage(
      "Інформація про продавця",
    ),
    "verificationCode": MessageLookupByLibrary.simpleMessage(
      "Код підтвердження (6 цифр)",
    ),
    "verifySMSCode": MessageLookupByLibrary.simpleMessage("Перевірити"),
    "version": m62,
    "viaWallet": MessageLookupByLibrary.simpleMessage("Через гаманець"),
    "video": MessageLookupByLibrary.simpleMessage("Відео"),
    "vietnamese": MessageLookupByLibrary.simpleMessage("В\'єтнамська"),
    "view": MessageLookupByLibrary.simpleMessage("Переглянути"),
    "viewAll": MessageLookupByLibrary.simpleMessage("Переглянути всі"),
    "viewCart": MessageLookupByLibrary.simpleMessage("Переглянути кошик"),
    "viewDetail": MessageLookupByLibrary.simpleMessage("Переглянути деталі"),
    "viewMore": MessageLookupByLibrary.simpleMessage("Дивитися більше"),
    "viewOnGoogleMaps": MessageLookupByLibrary.simpleMessage(
      "Переглянути на Google Maps",
    ),
    "viewOrder": MessageLookupByLibrary.simpleMessage("Переглянути замовлення"),
    "viewPointHistory": MessageLookupByLibrary.simpleMessage(
      "Історія точки огляду",
    ),
    "viewRecentTransactions": MessageLookupByLibrary.simpleMessage(
      "Переглянути останні транзакції",
    ),
    "visible": MessageLookupByLibrary.simpleMessage("Видимий"),
    "visitStore": MessageLookupByLibrary.simpleMessage("Відвідати магазин"),
    "waitForLoad": MessageLookupByLibrary.simpleMessage(
      "Очікування завантаження зображення",
    ),
    "waitForPost": MessageLookupByLibrary.simpleMessage(
      "Очікування публікації товару",
    ),
    "waiting": MessageLookupByLibrary.simpleMessage("Очікування"),
    "waitingForConfirmation": MessageLookupByLibrary.simpleMessage(
      "Очікування підтвердження",
    ),
    "walletBalance": MessageLookupByLibrary.simpleMessage("Баланс гаманця"),
    "walletBalanceWithValue": m63,
    "walletName": MessageLookupByLibrary.simpleMessage("Назва гаманця"),
    "warning": m64,
    "warningCurrencyMessageForWallet": m65,
    "weFoundBlogs": MessageLookupByLibrary.simpleMessage("Знайдено блог(и)"),
    "weFoundProducts": m66,
    "weNeedCameraAccessTo": MessageLookupByLibrary.simpleMessage(
      "Нам потрібен доступ до камери для сканування QR-коду або штрих-коду",
    ),
    "weSentAnOTPTo": MessageLookupByLibrary.simpleMessage(
      "Код автентифікації надіслано на",
    ),
    "weWillSendYouNotification": MessageLookupByLibrary.simpleMessage(
      "Ми надсилатимемо вам сповіщення про нові продукти та пропозиції. Ви завжди можете змінити це налаштування в налаштуваннях",
    ),
    "webView": MessageLookupByLibrary.simpleMessage("Веб-перегляд"),
    "website": MessageLookupByLibrary.simpleMessage("Веб-сайт"),
    "wednesday": MessageLookupByLibrary.simpleMessage("Середа"),
    "week": m67,
    "welcome": MessageLookupByLibrary.simpleMessage("Ласкаво просимо"),
    "welcomeBack": MessageLookupByLibrary.simpleMessage(
      "Ласкаво просимо назад",
    ),
    "welcomeRegister": MessageLookupByLibrary.simpleMessage(
      "Почніть свої покупки з нами",
    ),
    "welcomeUser": m68,
    "whichLanguageDoYouPrefer": MessageLookupByLibrary.simpleMessage(
      "Яку мову ви обираєте?",
    ),
    "wholesaleRegisterMsg": MessageLookupByLibrary.simpleMessage(
      "Будь ласка, зверніться до адміністратора для підтвердження вашої реєстрації",
    ),
    "willNotSendAndReceiveMessage": MessageLookupByLibrary.simpleMessage(
      "Ви не зможете надсилати та отримувати повідомлення від цього користувача",
    ),
    "winningBid": MessageLookupByLibrary.simpleMessage("Переможна ставка"),
    "withdrawAmount": MessageLookupByLibrary.simpleMessage("Сума для зняття"),
    "withdrawRequest": MessageLookupByLibrary.simpleMessage(
      "Запит на зняття коштів",
    ),
    "withdrawal": MessageLookupByLibrary.simpleMessage("Зняття коштів"),
    "womanCollections": MessageLookupByLibrary.simpleMessage("Жіночі колекції"),
    "writeComment": MessageLookupByLibrary.simpleMessage(
      "Напишіть свій коментар",
    ),
    "writeTitle": MessageLookupByLibrary.simpleMessage("Напишіть свою назву"),
    "writeYourNote": MessageLookupByLibrary.simpleMessage(
      "Напишіть свою примітку",
    ),
    "yearsAgo": m69,
    "yes": MessageLookupByLibrary.simpleMessage("Так"),
    "youAreOur": MessageLookupByLibrary.simpleMessage("Ти наш"),
    "youAreSelecting": m70,
    "youCanOnlyOrderSingleStore": MessageLookupByLibrary.simpleMessage(
      "Можна замовляти тільки в одному магазині",
    ),
    "youCanOnlyPurchase": MessageLookupByLibrary.simpleMessage(
      "Ви можете придбати лише",
    ),
    "youDontHaveAnyCoupons": MessageLookupByLibrary.simpleMessage(
      "У вас немає жодних купонів.",
    ),
    "youDontHavePermissionToCreatePost": MessageLookupByLibrary.simpleMessage(
      "У вас немає дозволу на створення допису",
    ),
    "youHave": MessageLookupByLibrary.simpleMessage("Ви маєте"),
    "youHaveAssignedToOrder": m71,
    "youHaveBeenSaveAddressYourLocal": MessageLookupByLibrary.simpleMessage(
      "Ви зберегли адресу у своєму місцезнаходженні",
    ),
    "youHaveNoPost": MessageLookupByLibrary.simpleMessage(
      "У вас немає дописів",
    ),
    "youHavePassed": m72,
    "youHavePoints": m73,
    "youMightAlsoLike": MessageLookupByLibrary.simpleMessage(
      "Вам також може сподобатися",
    ),
    "youNeedToLoginCheckout": MessageLookupByLibrary.simpleMessage(
      "Для оформлення замовлення потрібно увійти в систему",
    ),
    "youNotBeAsked": MessageLookupByLibrary.simpleMessage(
      "Наступного разу вас не запитають після завершення",
    ),
    "yourAccountIsUnderReview": MessageLookupByLibrary.simpleMessage(
      "Ваш обліковий запис перевіряється. Будь ласка, зв\'яжіться з адміністратором, якщо вам потрібна допомога",
    ),
    "yourAddressExistYourLocal": MessageLookupByLibrary.simpleMessage(
      "Ваша адреса існує у вашому місцезнаходженні",
    ),
    "yourAddressHasBeenSaved": MessageLookupByLibrary.simpleMessage(
      "Адресу збережено у вашому локальному сховищі",
    ),
    "yourApplicationIsUnderReview": MessageLookupByLibrary.simpleMessage(
      "Ваша заявка розглядається",
    ),
    "yourBagIsEmpty": MessageLookupByLibrary.simpleMessage(
      "Ваш кошик порожній",
    ),
    "yourBookingDetail": MessageLookupByLibrary.simpleMessage(
      "Деталі вашого бронювання",
    ),
    "yourEarningsThisMonth": MessageLookupByLibrary.simpleMessage(
      "Ваш заробіток цього місяця",
    ),
    "yourNote": MessageLookupByLibrary.simpleMessage("Ваша примітка"),
    "yourOrderHasBeenAdded": MessageLookupByLibrary.simpleMessage(
      "Ваше замовлення додано",
    ),
    "yourOrderIsConfirmed": MessageLookupByLibrary.simpleMessage(
      "Ваше замовлення підтверджено!",
    ),
    "yourOrderIsEmpty": MessageLookupByLibrary.simpleMessage(
      "Ваше замовлення порожнє",
    ),
    "yourOrderIsEmptyMsg": MessageLookupByLibrary.simpleMessage(
      "Схоже, ви ще нічого не додали.\nПочніть покупки, щоб заповнити кошик",
    ),
    "yourOrders": MessageLookupByLibrary.simpleMessage("Ваші замовлення"),
    "yourProductIsUnderReview": MessageLookupByLibrary.simpleMessage(
      "Ваш товар перебуває на розгляді",
    ),
    "yourUsernameEmail": MessageLookupByLibrary.simpleMessage(
      "Ваше ім\'я користувача або електронна пошта",
    ),
    "zipCode": MessageLookupByLibrary.simpleMessage("Поштовий індекс"),
    "zipCodeIsRequired": MessageLookupByLibrary.simpleMessage(
      "Поле поштового індексу обов\'язкове",
    ),
  };
}
