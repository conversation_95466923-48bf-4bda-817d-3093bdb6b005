// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  static String m0(x) => "Đang hoạt động ${x}";

  static String m1(amount) => "Thêm ${amount} điểm";

  static String m2(attribute) => "Bất kỳ ${attribute}";

  static String m3(point) => "Điểm hiện có của bạn: ${point}";

  static String m4(name) => "Đã đặt giá thầu thành công cho \'${name}\'";

  static String m5(state) => "Bluetooth ${state}";

  static String m6(amount) => "Mua ngay với giá ${amount}";

  static String m7(author) => "Tác giả: ${author}";

  static String m8(fieldName) => "${fieldName} không được để trống.";

  static String m9(fieldName) => "Chiều dài${fieldName} không được nhỏ hơn 3.";

  static String m10(currency) => "Đã thay đổi đơn vị tiền tệ thành ${currency}";

  static String m11(number) => "${number} ký tự còn lại";

  static String m12(priceRate, pointRate) => "${priceRate} = ${pointRate} Điểm";

  static String m13(count) => " ${count} mục";

  static String m14(count) => "${count} mục";

  static String m15(count) => "${count} sản phẩm";

  static String m16(count) => "${count} sản phẩm";

  static String m17(country) => "${country} quốc gia không được hỗ trợ";

  static String m18(currency) => "${currency} không được hỗ trợ";

  static String m19(day) => "${day} ngày trước";

  static String m20(total) => "~${total} km";

  static String m21(timeLeft) => "Kết thúc sau ${timeLeft}";

  static String m22(captcha) => "Nhập ${captcha} để xác nhận:";

  static String m23(message) => "Lỗi: ${message}";

  static String m24(message) => "Lỗi: ${message}";

  static String m25(time) => "Hết hạn sau ${time}";

  static String m26(total) => ">${total} km";

  static String m27(hour) => "${hour} giờ trước";

  static String m28(currentBalance) =>
      "Bạn chỉ còn ${currentBalance} trong ví của bạn";

  static String m29(message) =>
      "Có lỗi với ứng dụng trong quá trình yêu cầu dữ liệu, vui lòng liên hệ với quản trị viên để khắc phục: ${message}";

  static String m30(currency, amount) =>
      "Số tiền tối đa để sử dụng khoản thanh toán này là ${currency} ${amount}";

  static String m31(size) => "Kích thước tệp tối đa: ${size} MB";

  static String m32(name, formattedPrice) => "${name}: ${formattedPrice}";

  static String m33(currency, amount) =>
      "Số tiền tối thiểu để sử dụng khoản thanh toán này là ${currency} ${amount}";

  static String m34(storeName, minOrderAmount) =>
      "Số lượng đặt hàng tối thiểu cho ${storeName} là ${minOrderAmount}. Vui lòng thêm một vài mặt hàng nữa từ cửa hàng này!";

  static String m35(amount) =>
      "Phiếu giảm giá này yêu cầu giá trị mua tối thiểu là ${amount}.";

  static String m36(value) => "Giao dịch tối thiểu: ${value}";

  static String m37(minute) => "${minute} phút trước";

  static String m38(month) => "${month} tháng trước";

  static String m39(store) => "Thêm từ ${store}";

  static String m40(number) => "phải mua theo nhóm ${number}";

  static String m41(itemCount) => "${itemCount} sản phẩm";

  static String m42(price) => "Tổng số tùy chọn: ${price}";

  static String m43(amount) => "Thanh toán ${amount}";

  static String m44(name) => "${name} đã được thêm vào giỏ hàng thành công";

  static String m45(total) => "SL: ${total}";

  static String m46(name) => "Nhận tiền từ ${name}";

  static String m47(count) =>
      "Bạn có muốn xóa ${count} sản phẩm khỏi danh sách mong muốn của mình không?";

  static String m48(percent) => "Giảm ${percent}%";

  static String m49(keyword) => "Kết quả tìm kiếm cho: \'${keyword}\'";

  static String m50(keyword, count) => "${keyword} (${count} mục)";

  static String m51(keyword, count) => "${keyword} (${count} mục)";

  static String m52(second) => "${second} giây trước";

  static String m53(totalCartQuantity) =>
      "Giỏ hàng, ${totalCartQuantity} sản phẩm";

  static String m54(numberOfUnitsSold) => "Đã bán: ${numberOfUnitsSold}";

  static String m55(fieldName) => "Trường ${fieldName} là bắt buộc";

  static String m56(total) => "${total} sản phẩm";

  static String m57(name) => "Chuyển tiền đến ${name}";

  static String m58(amount) => "Sử dụng ${amount} điểm";

  static String m59(maxPointDiscount, maxPriceDiscount) =>
      "Sử dụng tối đa ${maxPointDiscount} Điểm để được giảm giá ${maxPriceDiscount} cho đơn hàng này!";

  static String m60(time) => "Có hiệu lực đến: ${time}";

  static String m61(date) => "Có giá trị đến ${date}";

  static String m62(number) => "Phiên bản ${number}";

  static String m63(balance) => "Số dư ví: ${balance}";

  static String m64(message) => "Cảnh báo: ${message}";

  static String m65(defaultCurrency) =>
      "Đơn vị tiền tệ đang chọn không khả dụng cho tính năng Ví, vui lòng đổi sang ${defaultCurrency}";

  static String m66(length) => "Tìm thấy ${length} sản phẩm";

  static String m67(week) => "Tuần ${week}";

  static String m68(name) => "Chào mừng ${name}";

  static String m69(year) => "${year} năm trước";

  static String m70(count) => "Bạn đang chọn ${count} mục";

  static String m71(total) => "Bạn đã được phân công đơn hàng #${total}";

  static String m72(type) => "Bạn đã vượt qua ${type}";

  static String m73(point) => "Bạn có ${point} điểm";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "aboutUs": MessageLookupByLibrary.simpleMessage("Về chúng tôi"),
    "account": MessageLookupByLibrary.simpleMessage("Tài khoản"),
    "accountApprovalTitle": MessageLookupByLibrary.simpleMessage(
      "Chờ phê duyệt",
    ),
    "accountDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "Xóa tài khoản sẽ xóa thông tin cá nhân khỏi cơ sở dữ liệu của chúng tôi. Email của bạn sẽ được giữ lại vĩnh viễn và không thể sử dụng lại để đăng ký tài khoản mới.",
    ),
    "accountIsPendingApproval": MessageLookupByLibrary.simpleMessage(
      "Tài khoản đang chờ phê duyệt.",
    ),
    "accountNumber": MessageLookupByLibrary.simpleMessage("Số tài khoản"),
    "accountSetup": MessageLookupByLibrary.simpleMessage("Thiết lập tài khoản"),
    "active": MessageLookupByLibrary.simpleMessage("Đang hoạt động"),
    "activeFor": m0,
    "activeLongAgo": MessageLookupByLibrary.simpleMessage("Hoạt động lâu rồi"),
    "activeNow": MessageLookupByLibrary.simpleMessage("Đang hoạt động"),
    "add": MessageLookupByLibrary.simpleMessage("Thêm vào"),
    "addAName": MessageLookupByLibrary.simpleMessage("Thêm tên"),
    "addANewPost": MessageLookupByLibrary.simpleMessage("Thêm bài viết mới"),
    "addASlug": MessageLookupByLibrary.simpleMessage("Thêm đường dẫn"),
    "addAmountPoints": m1,
    "addAnAttr": MessageLookupByLibrary.simpleMessage("Thêm thuộc tính"),
    "addListing": MessageLookupByLibrary.simpleMessage("Thêm danh sách"),
    "addMessage": MessageLookupByLibrary.simpleMessage("thêm một tin nhắn"),
    "addNew": MessageLookupByLibrary.simpleMessage("Thêm mới"),
    "addNewAddress": MessageLookupByLibrary.simpleMessage("Thêm địa chỉ mới"),
    "addNewBlog": MessageLookupByLibrary.simpleMessage("Thêm blog mới"),
    "addNewPost": MessageLookupByLibrary.simpleMessage("Tạo bài viết mới"),
    "addOrUsePointsSuccessMsg": MessageLookupByLibrary.simpleMessage(
      "Xin chúc mừng! Điểm đã được thêm hoặc đổi thành công.",
    ),
    "addPoint": MessageLookupByLibrary.simpleMessage("Thêm Điểm"),
    "addPoints": MessageLookupByLibrary.simpleMessage("Thêm Điểm"),
    "addProduct": MessageLookupByLibrary.simpleMessage("Thêm sản phẩm"),
    "addToCart": MessageLookupByLibrary.simpleMessage("Thêm vào giỏ hàng"),
    "addToCartMaximum": MessageLookupByLibrary.simpleMessage(
      "Đã vượt quá số lượng tối đa",
    ),
    "addToCartSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Đã thêm vào giỏ hàng thành công",
    ),
    "addToOrder": MessageLookupByLibrary.simpleMessage("Thêm vào đơn hàng"),
    "addToQuoteRequest": MessageLookupByLibrary.simpleMessage(
      "Thêm vào yêu cầu báo giá",
    ),
    "addToWishlist": MessageLookupByLibrary.simpleMessage(
      "Thêm vào danh sách yêu thích",
    ),
    "added": MessageLookupByLibrary.simpleMessage("Đã thêm"),
    "addedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Thêm thành công",
    ),
    "addingYourImage": MessageLookupByLibrary.simpleMessage(
      "Thêm hình ảnh của bạn",
    ),
    "additionalInformation": MessageLookupByLibrary.simpleMessage(
      "Thông tin bổ sung",
    ),
    "additionalServices": MessageLookupByLibrary.simpleMessage(
      "Dịch vụ bổ sung",
    ),
    "address": MessageLookupByLibrary.simpleMessage("Địa chỉ"),
    "adults": MessageLookupByLibrary.simpleMessage("Người lớn"),
    "advanceAmount": MessageLookupByLibrary.simpleMessage("Số tiền ứng trước"),
    "advancePayment": MessageLookupByLibrary.simpleMessage("Thanh toán trước"),
    "afternoon": MessageLookupByLibrary.simpleMessage("Chiều"),
    "agree": MessageLookupByLibrary.simpleMessage("Đồng ý"),
    "agreeWithPrivacy": MessageLookupByLibrary.simpleMessage(
      "Đồng ý với điều khoản",
    ),
    "albanian": MessageLookupByLibrary.simpleMessage("Tiếng Albania"),
    "all": MessageLookupByLibrary.simpleMessage("Tất cả"),
    "allBrands": MessageLookupByLibrary.simpleMessage("Tất cả thương hiệu"),
    "allDeliveryOrders": MessageLookupByLibrary.simpleMessage(
      "Tất cả đơn hàng",
    ),
    "allOrders": MessageLookupByLibrary.simpleMessage("Bán hàng mới nhất"),
    "allProducts": MessageLookupByLibrary.simpleMessage("Tất cả sản phẩm"),
    "allow": MessageLookupByLibrary.simpleMessage("Cho phép"),
    "allowCameraAccess": MessageLookupByLibrary.simpleMessage(
      "Cho phép truy cập Máy ảnh?",
    ),
    "almostSoldOut": MessageLookupByLibrary.simpleMessage("Sắp hết hàng"),
    "amazing": MessageLookupByLibrary.simpleMessage("Tuyệt vời"),
    "amount": MessageLookupByLibrary.simpleMessage("Số tiền"),
    "anyAttr": m2,
    "appTrackingRequest": MessageLookupByLibrary.simpleMessage(
      "Mã định danh này sẽ được sử dụng để phân phối quảng cáo được cá nhân hóa cho bạn. \n\"Hủy\" sẽ hạn chế khả năng phân phối quảng cáo có liên quan đến bạn của Mạng quảng cáo nhưng sẽ không làm giảm số lượng quảng cáo bạn nhận được. \nVì thiết bị bị hạn chế, nên tính năng theo dõi bị vô hiệu hóa và hệ thống không thể hiển thị hộp thoại yêu cầu. \"Mở Cài đặt\" và cho phép ứng dụng theo dõi hoạt động của bạn trên các ứng dụng và trang web của các công ty khác?",
    ),
    "appTrackingTransparency": MessageLookupByLibrary.simpleMessage(
      "Tính minh bạch theo dõi ứng dụng",
    ),
    "appearance": MessageLookupByLibrary.simpleMessage("Giao diện"),
    "apply": MessageLookupByLibrary.simpleMessage("Áp dụng"),
    "appointmentStartInvalidDay": MessageLookupByLibrary.simpleMessage(
      "Rất tiếc, không thể bắt đầu cuộc hẹn vào ngày này.",
    ),
    "approve": MessageLookupByLibrary.simpleMessage("Phê duyệt"),
    "approved": MessageLookupByLibrary.simpleMessage("Đã duyệt"),
    "approvedRequests": MessageLookupByLibrary.simpleMessage(
      "Yêu cầu được phê duyệt",
    ),
    "arabic": MessageLookupByLibrary.simpleMessage("Tiếng Ả Rập"),
    "areYouSure": MessageLookupByLibrary.simpleMessage("Bạn có chắc không?"),
    "areYouSureDeleteAccount": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc chắn xóa tài khoản của mình không?",
    ),
    "areYouSureLogOut": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc chắn bạn muốn thoát?",
    ),
    "areYouWantToExit": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc bạn muốn thoát?",
    ),
    "assigned": MessageLookupByLibrary.simpleMessage("Đã phân công"),
    "atLeastThreeCharacters": MessageLookupByLibrary.simpleMessage(
      "Ít nhất 3 ký tự...",
    ),
    "attribute": MessageLookupByLibrary.simpleMessage("Thuộc tính"),
    "attributeAlreadyExists": MessageLookupByLibrary.simpleMessage(
      "Thuộc tính đã tồn tại",
    ),
    "attributes": MessageLookupByLibrary.simpleMessage("Thuộc tính"),
    "auction": MessageLookupByLibrary.simpleMessage("Đấu giá"),
    "auctionDates": MessageLookupByLibrary.simpleMessage("Ngày đấu giá"),
    "auctionEnded": MessageLookupByLibrary.simpleMessage(
      "Cuộc đấu giá đã kết thúc",
    ),
    "auctionEnds": MessageLookupByLibrary.simpleMessage("Đấu giá kết thúc"),
    "auctionHistory": MessageLookupByLibrary.simpleMessage("Lịch sử đấu giá"),
    "auctionStarts": MessageLookupByLibrary.simpleMessage(
      "Cuộc đấu giá bắt đầu",
    ),
    "auctionStartsIn": MessageLookupByLibrary.simpleMessage(
      "Cuộc đấu giá bắt đầu vào",
    ),
    "auctionType": MessageLookupByLibrary.simpleMessage("Loại đấu giá"),
    "audioDetected": MessageLookupByLibrary.simpleMessage(
      "Đã phát hiện nội dung âm thanh. Bạn có muốn thêm vào trình phát âm thanh không?",
    ),
    "availability": MessageLookupByLibrary.simpleMessage("Tình trạng"),
    "availabilityProduct": MessageLookupByLibrary.simpleMessage("Tình trạng: "),
    "availableForTiers": MessageLookupByLibrary.simpleMessage(
      "Có sẵn cho các tầng",
    ),
    "availablePoints": m3,
    "averageRating": MessageLookupByLibrary.simpleMessage(
      "Xếp hạng trung bình",
    ),
    "b2bKingRegisterMsg": MessageLookupByLibrary.simpleMessage(
      "Vui lòng liên hệ với người quản trị để chấp thuận đăng ký của bạn.",
    ),
    "back": MessageLookupByLibrary.simpleMessage("Quay lại"),
    "backOrder": MessageLookupByLibrary.simpleMessage("Đặt hàng trước"),
    "backToShop": MessageLookupByLibrary.simpleMessage("Quay lại cửa hàng"),
    "backToWallet": MessageLookupByLibrary.simpleMessage("Quay lại ví"),
    "bagsCollections": MessageLookupByLibrary.simpleMessage(
      "Bộ sưu tập túi xách",
    ),
    "balance": MessageLookupByLibrary.simpleMessage("Số dư"),
    "bank": MessageLookupByLibrary.simpleMessage("NGÂN HÀNG"),
    "bannerListType": MessageLookupByLibrary.simpleMessage(
      "Loại danh sách banner",
    ),
    "bannerType": MessageLookupByLibrary.simpleMessage("Loại banner"),
    "bannerYoutubeURL": MessageLookupByLibrary.simpleMessage(
      "URL Youtube của banner",
    ),
    "basicInformation": MessageLookupByLibrary.simpleMessage(
      "Thông tin cơ bản",
    ),
    "becomeADelivery": MessageLookupByLibrary.simpleMessage(
      "Trở thành người giao hàng",
    ),
    "becomeAVendor": MessageLookupByLibrary.simpleMessage(
      "Trở thành nhà cung cấp",
    ),
    "becomeAVendorDelivery": MessageLookupByLibrary.simpleMessage(
      "Trở thành Nhà cung cấp/Giao hàng",
    ),
    "benefits": MessageLookupByLibrary.simpleMessage("Những lợi ích"),
    "bengali": MessageLookupByLibrary.simpleMessage("Tiếng Bengali"),
    "bid": MessageLookupByLibrary.simpleMessage("Đấu thầu"),
    "bidIncrement": MessageLookupByLibrary.simpleMessage("Tăng giá thầu"),
    "bidSuccessMessage": m4,
    "billingAddress": MessageLookupByLibrary.simpleMessage(
      "Địa chỉ thanh toán",
    ),
    "bleHasNotBeenEnabled": MessageLookupByLibrary.simpleMessage(
      "Bluetooth chưa được bật",
    ),
    "bleState": m5,
    "block": MessageLookupByLibrary.simpleMessage("Khối"),
    "blockUser": MessageLookupByLibrary.simpleMessage("Chặn người dùng"),
    "blog": MessageLookupByLibrary.simpleMessage("Blog"),
    "booked": MessageLookupByLibrary.simpleMessage("Đã đặt"),
    "booking": MessageLookupByLibrary.simpleMessage("Đặt trước"),
    "bookingCancelled": MessageLookupByLibrary.simpleMessage("Đã hủy đặt"),
    "bookingConfirm": MessageLookupByLibrary.simpleMessage("Đã xác nhận"),
    "bookingError": MessageLookupByLibrary.simpleMessage(
      "Có lỗi xảy ra. Vui lòng thử lại sau.",
    ),
    "bookingHistory": MessageLookupByLibrary.simpleMessage("Lịch sử đặt chỗ"),
    "bookingNow": MessageLookupByLibrary.simpleMessage("Đặt ngay"),
    "bookingSuccess": MessageLookupByLibrary.simpleMessage("Đặt thành công"),
    "bookingSummary": MessageLookupByLibrary.simpleMessage("Tóm tắt đặt chỗ"),
    "bookingUnavailable": MessageLookupByLibrary.simpleMessage(
      "Không có đặt chỗ",
    ),
    "bosnian": MessageLookupByLibrary.simpleMessage("Tiếng Bosnia"),
    "branch": MessageLookupByLibrary.simpleMessage("Chi nhánh"),
    "branchChangeWarning": MessageLookupByLibrary.simpleMessage(
      "Rất tiếc, giỏ hàng sẽ trống do thay đổi khu vực. Chúng tôi rất vui được liên hệ với bạn nếu bạn cần hỗ trợ.",
    ),
    "brand": MessageLookupByLibrary.simpleMessage("Nhãn hiệu"),
    "brands": MessageLookupByLibrary.simpleMessage("Nhãn hiệu"),
    "brazil": MessageLookupByLibrary.simpleMessage("Tiếng Bồ Đào Nha"),
    "bronze": MessageLookupByLibrary.simpleMessage("Đóng"),
    "bronzePriority": MessageLookupByLibrary.simpleMessage("Ưu tiên đồng"),
    "burmese": MessageLookupByLibrary.simpleMessage("Tiếng Miến Điện"),
    "buyItNowPrice": MessageLookupByLibrary.simpleMessage("Mua ngay giá"),
    "buyNow": MessageLookupByLibrary.simpleMessage("Mua ngay"),
    "buyNowFor": m6,
    "by": MessageLookupByLibrary.simpleMessage("bởi"),
    "byAppointmentOnly": MessageLookupByLibrary.simpleMessage(
      "Chỉ theo lịch hẹn",
    ),
    "byAuthor": m7,
    "byBrand": MessageLookupByLibrary.simpleMessage("Theo thương hiệu"),
    "byCategory": MessageLookupByLibrary.simpleMessage("Theo danh mục"),
    "byPrice": MessageLookupByLibrary.simpleMessage("Theo giá"),
    "bySignup": MessageLookupByLibrary.simpleMessage(
      "Bằng cách đăng ký, bạn đồng ý với",
    ),
    "byTag": MessageLookupByLibrary.simpleMessage("Theo thẻ"),
    "call": MessageLookupByLibrary.simpleMessage("Gọi"),
    "callTo": MessageLookupByLibrary.simpleMessage("Gọi cho"),
    "callToVendor": MessageLookupByLibrary.simpleMessage(
      "Gọi cho chủ cửa hàng",
    ),
    "canNotCreateOrder": MessageLookupByLibrary.simpleMessage(
      "Không thể tạo đơn hàng",
    ),
    "canNotCreateUser": MessageLookupByLibrary.simpleMessage(
      "Không thể tạo người dùng.",
    ),
    "canNotGetPayments": MessageLookupByLibrary.simpleMessage(
      "Không thể nhận phương thức thanh toán",
    ),
    "canNotGetShipping": MessageLookupByLibrary.simpleMessage(
      "Không thể nhận phương thức vận chuyển",
    ),
    "canNotGetToken": MessageLookupByLibrary.simpleMessage(
      "Không thể nhận thông tin mã thông báo.",
    ),
    "canNotLaunch": MessageLookupByLibrary.simpleMessage(
      "Không thể khởi chạy ứng dụng này. Đảm bảo cài đặt của bạn trong config.dart là chính xác",
    ),
    "canNotLoadThisLink": MessageLookupByLibrary.simpleMessage(
      "Không thể tải liên kết này",
    ),
    "canNotPlayVideo": MessageLookupByLibrary.simpleMessage(
      "Rất tiếc, video này không thể phát được.",
    ),
    "canNotSaveOrder": MessageLookupByLibrary.simpleMessage(
      "Không thể lưu đơn đặt hàng vào trang web",
    ),
    "canNotUpdateInfo": MessageLookupByLibrary.simpleMessage(
      "Không thể cập nhật thông tin người dùng.",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Hủy"),
    "cancelOrder": MessageLookupByLibrary.simpleMessage("Hủy đơn hàng"),
    "cancelled": MessageLookupByLibrary.simpleMessage("Đã hủy"),
    "cancelledRequests": MessageLookupByLibrary.simpleMessage("Yêu cầu đã hủy"),
    "cannotBeEmpty": m8,
    "cannotDeleteAccount": MessageLookupByLibrary.simpleMessage(
      "Không thể xóa tài khoản này",
    ),
    "cannotLessThreeLength": m9,
    "cannotSendMessage": MessageLookupByLibrary.simpleMessage(
      "Bạn không thể gửi tin nhắn cho người dùng này",
    ),
    "cantFindThisOrderId": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy ID đơn hàng này",
    ),
    "cantPickDateInThePast": MessageLookupByLibrary.simpleMessage(
      "Không được chọn ngày trong quá khứ",
    ),
    "card": MessageLookupByLibrary.simpleMessage("Thẻ"),
    "cardHolder": MessageLookupByLibrary.simpleMessage("Chủ thẻ"),
    "cardNumber": MessageLookupByLibrary.simpleMessage("Số thẻ"),
    "cart": MessageLookupByLibrary.simpleMessage("Giỏ hàng"),
    "cartDiscount": MessageLookupByLibrary.simpleMessage("Giảm giá giỏ hàng"),
    "cartNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Giỏ hàng không khả dụng. Vui lòng thêm một số mặt hàng vào giỏ hàng của bạn.",
    ),
    "cartNotReadyForCheckout": MessageLookupByLibrary.simpleMessage(
      "Giỏ hàng của bạn vẫn đang được xử lý. Vui lòng đợi trong giây lát.",
    ),
    "cash": MessageLookupByLibrary.simpleMessage("Tiền mặt"),
    "categories": MessageLookupByLibrary.simpleMessage("Danh mục"),
    "category": MessageLookupByLibrary.simpleMessage("Danh mục"),
    "change": MessageLookupByLibrary.simpleMessage("Thay đổi"),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("Thay đổi ngôn ngữ"),
    "changePrinter": MessageLookupByLibrary.simpleMessage("Đổi máy in"),
    "changedCurrencyTo": m10,
    "characterRemain": m11,
    "chat": MessageLookupByLibrary.simpleMessage("Trò chuyện"),
    "chatGPT": MessageLookupByLibrary.simpleMessage("Chat GPT"),
    "chatListScreen": MessageLookupByLibrary.simpleMessage(
      "Danh sách trò chuyện",
    ),
    "chatViaFacebook": MessageLookupByLibrary.simpleMessage(
      "Trò chuyện qua Facebook Messenger",
    ),
    "chatViaWhatApp": MessageLookupByLibrary.simpleMessage(
      "Trò chuyện qua WhatsApp",
    ),
    "chatWithBot": MessageLookupByLibrary.simpleMessage("Trò chuyện với Bot"),
    "chatWithStoreOwner": MessageLookupByLibrary.simpleMessage(
      "Trò chuyện với chủ cửa hàng",
    ),
    "checkConfirmLink": MessageLookupByLibrary.simpleMessage(
      "Kiểm tra email của bạn để lấy liên kết xác nhận",
    ),
    "checking": MessageLookupByLibrary.simpleMessage("Đang kiểm tra..."),
    "checkout": MessageLookupByLibrary.simpleMessage("Thanh toán"),
    "chinese": MessageLookupByLibrary.simpleMessage("Tiếng Trung"),
    "chineseSimplified": MessageLookupByLibrary.simpleMessage(
      "Tiếng Trung (Giản thể)",
    ),
    "chineseTraditional": MessageLookupByLibrary.simpleMessage(
      "Tiếng Trung (Phồn thể)",
    ),
    "chooseBranch": MessageLookupByLibrary.simpleMessage("Chọn chi nhánh"),
    "chooseCategory": MessageLookupByLibrary.simpleMessage("Chọn danh mục"),
    "chooseFromGallery": MessageLookupByLibrary.simpleMessage(
      "Chọn từ thư viện",
    ),
    "chooseFromServer": MessageLookupByLibrary.simpleMessage("Chọn từ máy chủ"),
    "choosePlan": MessageLookupByLibrary.simpleMessage("Chọn gói"),
    "chooseStaff": MessageLookupByLibrary.simpleMessage("Chọn nhân viên"),
    "chooseType": MessageLookupByLibrary.simpleMessage("Chọn loại"),
    "chooseYourPaymentMethod": MessageLookupByLibrary.simpleMessage(
      "Chọn phương thức thanh toán",
    ),
    "city": MessageLookupByLibrary.simpleMessage("Thành phố"),
    "cityIsRequired": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập thành phố",
    ),
    "claim": MessageLookupByLibrary.simpleMessage("Khẳng định"),
    "claimed": MessageLookupByLibrary.simpleMessage("Đã xác nhận quyền sở hữu"),
    "clear": MessageLookupByLibrary.simpleMessage("Xóa"),
    "clearCart": MessageLookupByLibrary.simpleMessage("Xóa giỏ hàng"),
    "clearCartAndAddNew": MessageLookupByLibrary.simpleMessage(
      "Xóa giỏ hàng và thêm mới",
    ),
    "clearConversation": MessageLookupByLibrary.simpleMessage(
      "Xoá cuộc trò chuyện",
    ),
    "close": MessageLookupByLibrary.simpleMessage("Đóng"),
    "closeNow": MessageLookupByLibrary.simpleMessage("Đóng cửa"),
    "closed": MessageLookupByLibrary.simpleMessage("Đã đóng"),
    "codExtraFee": MessageLookupByLibrary.simpleMessage("Phí bổ sung COD"),
    "color": MessageLookupByLibrary.simpleMessage("Màu sắc"),
    "columns": MessageLookupByLibrary.simpleMessage("Cột"),
    "comment": MessageLookupByLibrary.simpleMessage("Bình luận"),
    "commentFirst": MessageLookupByLibrary.simpleMessage(
      "Vui lòng viết bình luận của bạn",
    ),
    "commentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Đã bình luận thành công, vui lòng đợi phê duyệt",
    ),
    "complete": MessageLookupByLibrary.simpleMessage("Hoàn thành"),
    "confirm": MessageLookupByLibrary.simpleMessage("Xác nhận"),
    "confirmAccountDeletion": MessageLookupByLibrary.simpleMessage(
      "Xác nhận xóa tài khoản",
    ),
    "confirmClearCartWhenTopUp": MessageLookupByLibrary.simpleMessage(
      "Giỏ hàng sẽ được xóa khi nạp tiền.",
    ),
    "confirmClearTheCart": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc chắn muốn xóa giỏ hàng không?",
    ),
    "confirmDelete": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc chắn muốn xóa cái này không? Hành động này không thể được hoàn tác.",
    ),
    "confirmDeleteItem": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc chắn muốn xóa mục này không?",
    ),
    "confirmPassword": MessageLookupByLibrary.simpleMessage(
      "Xác nhận mật khẩu",
    ),
    "confirmPasswordIsRequired": MessageLookupByLibrary.simpleMessage(
      "Trường Xác nhận mật khẩu là bắt buộc",
    ),
    "confirmRemoveProductInCart": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc chắn muốn loại bỏ sản phẩm này?",
    ),
    "connect": MessageLookupByLibrary.simpleMessage("Kết nối"),
    "contact": MessageLookupByLibrary.simpleMessage("Liên hệ"),
    "content": MessageLookupByLibrary.simpleMessage("Nội dung"),
    "continueShopping": MessageLookupByLibrary.simpleMessage(
      "Tiếp tục mua sắm",
    ),
    "continueToPayment": MessageLookupByLibrary.simpleMessage(
      "Tiếp tục đến thanh toán",
    ),
    "continueToReview": MessageLookupByLibrary.simpleMessage(
      "Tiếp tục xem lại",
    ),
    "continueToSelectItem": MessageLookupByLibrary.simpleMessage(
      "Tiếp tục chọn mục",
    ),
    "continueToShipping": MessageLookupByLibrary.simpleMessage(
      "Tiếp tục đến vận chuyển",
    ),
    "continueWithShopify": MessageLookupByLibrary.simpleMessage(
      "Tiếp tục với Shopify",
    ),
    "continues": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
    "conversations": MessageLookupByLibrary.simpleMessage("Cuộc trò chuyện"),
    "convertPoint": m12,
    "copied": MessageLookupByLibrary.simpleMessage("Sao chép"),
    "copy": MessageLookupByLibrary.simpleMessage("Sao chép"),
    "copyright": MessageLookupByLibrary.simpleMessage(
      "© 2024 InspireUI Mọi quyền được bảo lưu.",
    ),
    "countItem": m13,
    "countItems": m14,
    "countProduct": m15,
    "countProducts": m16,
    "country": MessageLookupByLibrary.simpleMessage("Quốc gia"),
    "countryCodeIsRequired": MessageLookupByLibrary.simpleMessage(
      "Mã quốc gia không được bỏ trống",
    ),
    "countryIsNotSupported": m17,
    "countryIsRequired": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn quốc gia",
    ),
    "couponCode": MessageLookupByLibrary.simpleMessage("Mã giảm giá"),
    "couponHasBeenSavedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Đã lưu phiếu giảm giá thành công.",
    ),
    "couponInvalid": MessageLookupByLibrary.simpleMessage(
      "Mã giảm giá không hợp lệ",
    ),
    "couponMsgSuccess": MessageLookupByLibrary.simpleMessage(
      "Áp dụng mã giảm giá thành công",
    ),
    "couponsDedicatedForYou": MessageLookupByLibrary.simpleMessage(
      "Phiếu giảm giá dành riêng cho bạn",
    ),
    "couponsManagement": MessageLookupByLibrary.simpleMessage(
      "Quản lý phiếu giảm giá",
    ),
    "create": MessageLookupByLibrary.simpleMessage("Tạo nên"),
    "createAnAccount": MessageLookupByLibrary.simpleMessage("Tạo tài khoản"),
    "createNewPostSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Bài viết của bạn đã được tạo thành công dưới dạng bản nháp. Vui lòng xem trang quản trị.",
    ),
    "createPost": MessageLookupByLibrary.simpleMessage("Tạo bài viết"),
    "createProduct": MessageLookupByLibrary.simpleMessage("Tạo sản phẩm"),
    "createReviewSuccess": MessageLookupByLibrary.simpleMessage(
      "Cám ơn, vì phản hồi của bạn",
    ),
    "createReviewSuccessMsg": MessageLookupByLibrary.simpleMessage(
      "Chúng tôi thực sự đánh giá cao ý kiến đóng góp của bạn và đánh giá cao sự đóng góp của bạn trong việc giúp chúng tôi cải thiện",
    ),
    "createVariants": MessageLookupByLibrary.simpleMessage(
      "Tạo tất cả biến thể",
    ),
    "createdOn": MessageLookupByLibrary.simpleMessage("Tạo lúc:"),
    "currencies": MessageLookupByLibrary.simpleMessage("Tiền tệ"),
    "currencyIsNotSupported": m18,
    "currentBid": MessageLookupByLibrary.simpleMessage("Giá thầu hiện tại"),
    "currentPassword": MessageLookupByLibrary.simpleMessage(
      "Mật khẩu hiện tại",
    ),
    "currentlyWeOnlyHave": MessageLookupByLibrary.simpleMessage(
      "Hiện tại chúng tôi chỉ có",
    ),
    "customer": MessageLookupByLibrary.simpleMessage("Khách hàng"),
    "customerDetail": MessageLookupByLibrary.simpleMessage(
      "Thông tin khách hàng",
    ),
    "customerNote": MessageLookupByLibrary.simpleMessage(
      "Ghi chú của khách hàng",
    ),
    "cvv": MessageLookupByLibrary.simpleMessage("CVV"),
    "czech": MessageLookupByLibrary.simpleMessage("Tiếng Séc"),
    "danish": MessageLookupByLibrary.simpleMessage("người Đan Mạch"),
    "darkTheme": MessageLookupByLibrary.simpleMessage("Giao diện tối"),
    "dashboard": MessageLookupByLibrary.simpleMessage("Bảng điều khiển"),
    "dataEmpty": MessageLookupByLibrary.simpleMessage("Không có dữ liệu"),
    "date": MessageLookupByLibrary.simpleMessage("Ngày"),
    "dateASC": MessageLookupByLibrary.simpleMessage("Ngày tăng dần"),
    "dateBooking": MessageLookupByLibrary.simpleMessage("Ngày đặt"),
    "dateDESC": MessageLookupByLibrary.simpleMessage("Ngày giảm dần"),
    "dateEnd": MessageLookupByLibrary.simpleMessage("Ngày kết thúc"),
    "dateLatest": MessageLookupByLibrary.simpleMessage("Ngày: Mới nhất"),
    "dateOldest": MessageLookupByLibrary.simpleMessage("Ngày: Cũ nhất"),
    "dateStart": MessageLookupByLibrary.simpleMessage("Ngày bắt đầu"),
    "dateTime": MessageLookupByLibrary.simpleMessage("Ngày giờ"),
    "dateWiseClose": MessageLookupByLibrary.simpleMessage("Đóng theo ngày"),
    "daysAgo": m19,
    "debit": MessageLookupByLibrary.simpleMessage("Ghi nợ"),
    "decline": MessageLookupByLibrary.simpleMessage("Từ chối"),
    "delete": MessageLookupByLibrary.simpleMessage("Xóa"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("Xóa tài khoản"),
    "deleteAccountMsg": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc chắn muốn xóa tài khoản của mình không? Vui lòng đọc tác động của việc xóa tài khoản.",
    ),
    "deleteAccountSuccess": MessageLookupByLibrary.simpleMessage(
      "Đã xóa tài khoản thành công. Phiên của bạn đã hết hạn.",
    ),
    "deleteAll": MessageLookupByLibrary.simpleMessage("Xóa tất cả"),
    "deleteConversation": MessageLookupByLibrary.simpleMessage(
      "Xóa cuộc trò chuyện",
    ),
    "delivered": MessageLookupByLibrary.simpleMessage("Đã giao hàng"),
    "deliveredTo": MessageLookupByLibrary.simpleMessage("Đã giao cho"),
    "delivering": MessageLookupByLibrary.simpleMessage("Đang giao hàng"),
    "deliveryBoy": MessageLookupByLibrary.simpleMessage("Nhân viên giao hàng:"),
    "deliveryDate": MessageLookupByLibrary.simpleMessage("Ngày giao hàng"),
    "deliveryDetails": MessageLookupByLibrary.simpleMessage(
      "Chi tiết giao hàng",
    ),
    "deliveryManagement": MessageLookupByLibrary.simpleMessage(
      "Quản lý giao hàng",
    ),
    "deliveryNotificationError": MessageLookupByLibrary.simpleMessage(
      "Không có dữ liệu.\nĐơn hàng này đã bị xóa.",
    ),
    "description": MessageLookupByLibrary.simpleMessage("Mô tả"),
    "descriptionEnterVoucher": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập hoặc chọn voucher cho đơn hàng của bạn.",
    ),
    "didntReceiveCode": MessageLookupByLibrary.simpleMessage(
      "Không nhận được mã? ",
    ),
    "direction": MessageLookupByLibrary.simpleMessage("Hướng"),
    "disablePurchase": MessageLookupByLibrary.simpleMessage(
      "Vô hiệu hóa mua hàng",
    ),
    "discount": MessageLookupByLibrary.simpleMessage("Giảm giá"),
    "displayName": MessageLookupByLibrary.simpleMessage("Tên hiển thị"),
    "distance": m20,
    "doNotAnyTransactions": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa có giao dịch nào",
    ),
    "doYouWantToExitApp": MessageLookupByLibrary.simpleMessage(
      "Bạn muốn thoát ứng dụng",
    ),
    "doYouWantToLeaveWithoutSubmit": MessageLookupByLibrary.simpleMessage(
      "Bạn có muốn rời đi mà không gửi đánh giá của mình không?",
    ),
    "doYouWantToLogout": MessageLookupByLibrary.simpleMessage(
      "Bạn có muốn đăng xuất không?",
    ),
    "doYouWantToUnblock": MessageLookupByLibrary.simpleMessage(
      "Bạn có muốn bỏ chặn người dùng này không?",
    ),
    "doesNotSupportApplePay": MessageLookupByLibrary.simpleMessage(
      "ApplePay không được hỗ trợ. Vui lòng kiểm tra ví và thẻ của bạn",
    ),
    "done": MessageLookupByLibrary.simpleMessage("Hoàn thành"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage(
      "Chưa có tài khoản?",
    ),
    "download": MessageLookupByLibrary.simpleMessage("Tải xuống"),
    "downloadApp": MessageLookupByLibrary.simpleMessage("Tải ứng dụng"),
    "downloadingImages": MessageLookupByLibrary.simpleMessage(
      "Đang tải xuống hình ảnh...",
    ),
    "draft": MessageLookupByLibrary.simpleMessage("Bản nháp"),
    "driverAssigned": MessageLookupByLibrary.simpleMessage(
      "Người lái xe được chỉ định",
    ),
    "duration": MessageLookupByLibrary.simpleMessage("Thời lượng"),
    "dutch": MessageLookupByLibrary.simpleMessage("Tiếng Hà Lan"),
    "earnings": MessageLookupByLibrary.simpleMessage("Thu nhập"),
    "edit": MessageLookupByLibrary.simpleMessage("Sửa: "),
    "editProductInfo": MessageLookupByLibrary.simpleMessage(
      "Chỉnh sửa thông tin sản phẩm",
    ),
    "editWithoutColon": MessageLookupByLibrary.simpleMessage("chỉnh sửa"),
    "egypt": MessageLookupByLibrary.simpleMessage("Ai Cập"),
    "email": MessageLookupByLibrary.simpleMessage("Email"),
    "emailAddressInvalid": MessageLookupByLibrary.simpleMessage(
      "địa chỉ email không hợp lệ",
    ),
    "emailAlreadyInUse": MessageLookupByLibrary.simpleMessage(
      "Email đã được sử dụng!",
    ),
    "emailDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "Xóa tài khoản sẽ hủy đăng ký bạn khỏi danh sách gửi thư.",
    ),
    "emailDoesNotExist": MessageLookupByLibrary.simpleMessage(
      "Tài khoản email này không tồn tại. Vui lòng thử lại.",
    ),
    "emailIsRequired": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập email",
    ),
    "emailSubscription": MessageLookupByLibrary.simpleMessage("Đăng ký email"),
    "emptyBookingHistoryMsg": MessageLookupByLibrary.simpleMessage(
      "Có vẻ như bạn chưa thực hiện bất kỳ lượt đặt chỗ nào.\nBắt đầu khám phá và thực hiện đặt phòng đầu tiên của bạn!",
    ),
    "emptyCart": MessageLookupByLibrary.simpleMessage("Giỏ hàng trống"),
    "emptyCartSubtitle": MessageLookupByLibrary.simpleMessage(
      "Có vẻ như bạn chưa thêm sản phẩm nào vào giỏ hàng. Hãy bắt đầu mua sắm ngay!",
    ),
    "emptyCartSubtitle02": MessageLookupByLibrary.simpleMessage(
      "Ối! Xe đẩy của bạn có vẻ hơi nhẹ.\n\nSẵn sàng để mua sắm một cái gì đó tuyệt vời?",
    ),
    "emptyComment": MessageLookupByLibrary.simpleMessage(
      "Bình luận không được để trống",
    ),
    "emptySearch": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa tìm kiếm mục nào. Hãy bắt đầu ngay - chúng tôi sẽ giúp bạn.",
    ),
    "emptyShippingMsg": MessageLookupByLibrary.simpleMessage(
      "Không có tùy chọn vận chuyển có sẵn. Vui lòng đảm bảo rằng địa chỉ của bạn đã được nhập chính xác, hoặc liên hệ với chúng tôi nếu bạn cần bất kỳ trợ giúp nào.",
    ),
    "emptyUsername": MessageLookupByLibrary.simpleMessage(
      "Tên đăng nhập/Email trống",
    ),
    "emptyWishlist": MessageLookupByLibrary.simpleMessage(
      "Danh sách yêu thích trống",
    ),
    "emptyWishlistSubtitle": MessageLookupByLibrary.simpleMessage(
      "Nhấn vào biểu tượng trái tim bên cạnh sản phẩm bạn yêu thích. Chúng tôi sẽ lưu chúng ở đây cho bạn",
    ),
    "emptyWishlistSubtitle02": MessageLookupByLibrary.simpleMessage(
      "Danh sách mong muốn của bạn hiện đang trống.\nBắt đầu thêm sản phẩm ngay bây giờ!",
    ),
    "enableForCheckout": MessageLookupByLibrary.simpleMessage(
      "Kích hoạt để thanh toán",
    ),
    "enableForLogin": MessageLookupByLibrary.simpleMessage(
      "Kích hoạt để đăng nhập",
    ),
    "enableForWallet": MessageLookupByLibrary.simpleMessage("Kích hoạt cho ví"),
    "enableVacationMode": MessageLookupByLibrary.simpleMessage(
      "Bật chế độ nghỉ",
    ),
    "endDateCantBeAfterFirstDate": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn ngày sau ngày bắt đầu",
    ),
    "endsIn": m21,
    "english": MessageLookupByLibrary.simpleMessage("Tiếng Anh"),
    "enterAmount": MessageLookupByLibrary.simpleMessage("Nhập số tiền"),
    "enterCaptcha": m22,
    "enterDescription": MessageLookupByLibrary.simpleMessage("Nhập mô tả"),
    "enterEmailEachRecipient": MessageLookupByLibrary.simpleMessage(
      "Nhập địa chỉ email cho mỗi người nhận",
    ),
    "enterPoint": MessageLookupByLibrary.simpleMessage("Nhập điểm"),
    "enterPrice": MessageLookupByLibrary.simpleMessage("Nhập giá"),
    "enterSentCode": MessageLookupByLibrary.simpleMessage("Nhập mã đã gửi đến"),
    "enterVoucherCode": MessageLookupByLibrary.simpleMessage(
      "Nhập Mã Phiếu Mua Hàng",
    ),
    "enterYourEmail": MessageLookupByLibrary.simpleMessage(
      "Nhập email của bạn",
    ),
    "enterYourEmailOrUsername": MessageLookupByLibrary.simpleMessage(
      "Nhập email hoặc tên đăng nhập của bạn",
    ),
    "enterYourFirstName": MessageLookupByLibrary.simpleMessage(
      "Nhập tên của bạn",
    ),
    "enterYourLastName": MessageLookupByLibrary.simpleMessage(
      "Nhập họ của bạn",
    ),
    "enterYourMobile": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập số điện thoại của bạn",
    ),
    "enterYourNote": MessageLookupByLibrary.simpleMessage(
      "Nhập ghi chú của bạn",
    ),
    "enterYourPassword": MessageLookupByLibrary.simpleMessage(
      "Nhập mật khẩu của bạn",
    ),
    "enterYourPhone": MessageLookupByLibrary.simpleMessage(
      "Nhập số điện thoại của bạn để bắt đầu.",
    ),
    "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Nhập số điện thoại của bạn",
    ),
    "enterYourUsername": MessageLookupByLibrary.simpleMessage(
      "Nhập tên người dùng của bạn",
    ),
    "error": m23,
    "errorAmountTransfer": MessageLookupByLibrary.simpleMessage(
      "Số tiền nhập vào lớn hơn số dư hiện tại. Vui lòng thử lại!",
    ),
    "errorEmailFormat": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập địa chỉ email hợp lệ.",
    ),
    "errorMsg": m24,
    "errorOnGettingPost": MessageLookupByLibrary.simpleMessage(
      "Lỗi khi lấy bài viết!",
    ),
    "errorPasswordFormat": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập mật khẩu có ít nhất 8 ký tự",
    ),
    "errorTitle": MessageLookupByLibrary.simpleMessage("Lỗi"),
    "evening": MessageLookupByLibrary.simpleMessage("Tối"),
    "events": MessageLookupByLibrary.simpleMessage("Sự kiện"),
    "expectedDeliveryDate": MessageLookupByLibrary.simpleMessage(
      "Ngày giao hàng dự kiến",
    ),
    "expired": MessageLookupByLibrary.simpleMessage("Đã hết hạn"),
    "expiredDate": MessageLookupByLibrary.simpleMessage("Ngày hết hạn"),
    "expiredDateHint": MessageLookupByLibrary.simpleMessage("MM/YY"),
    "expiringInTime": m25,
    "exploreNow": MessageLookupByLibrary.simpleMessage("Khám phá ngay bây giờ"),
    "external": MessageLookupByLibrary.simpleMessage("Bên ngoài"),
    "extraServices": MessageLookupByLibrary.simpleMessage("Dịch vụ bổ sung"),
    "failToAssign": MessageLookupByLibrary.simpleMessage(
      "Không chỉ định được người dùng",
    ),
    "failedToGenerateLink": MessageLookupByLibrary.simpleMessage(
      "Không tạo được liên kết",
    ),
    "failedToLoadAppConfig": MessageLookupByLibrary.simpleMessage(
      "Không thể tải cấu hình ứng dụng. Vui lòng thử lại hoặc khởi động lại ứng dụng của bạn.",
    ),
    "failedToLoadImage": MessageLookupByLibrary.simpleMessage(
      "Không thể tải hình ảnh",
    ),
    "fair": MessageLookupByLibrary.simpleMessage("Bình thường"),
    "favorite": MessageLookupByLibrary.simpleMessage("Yêu thích"),
    "fax": MessageLookupByLibrary.simpleMessage("Số fax"),
    "feature": MessageLookupByLibrary.simpleMessage("Tính năng"),
    "featureNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Tính năng không khả dụng",
    ),
    "featureProducts": MessageLookupByLibrary.simpleMessage("Sản phẩm nổi bật"),
    "featured": MessageLookupByLibrary.simpleMessage("Nổi bật"),
    "features": MessageLookupByLibrary.simpleMessage("Tính năng"),
    "fileIsTooBig": MessageLookupByLibrary.simpleMessage(
      "Tệp quá lớn. Vui lòng chọn một tệp nhỏ hơn!",
    ),
    "fileUploadFailed": MessageLookupByLibrary.simpleMessage(
      "Tải lên tệp không thành công!",
    ),
    "files": MessageLookupByLibrary.simpleMessage("các tập tin"),
    "filter": MessageLookupByLibrary.simpleMessage("Lọc"),
    "fingerprintsTouchID": MessageLookupByLibrary.simpleMessage(
      "Dấu vân tay, ID cảm ứng",
    ),
    "finishSetup": MessageLookupByLibrary.simpleMessage("Hoàn tất cài đặt"),
    "finnish": MessageLookupByLibrary.simpleMessage("Tiếng Phần Lan"),
    "firstComment": MessageLookupByLibrary.simpleMessage(
      "Hãy là người đầu tiên bình luận cho bài viết này!",
    ),
    "firstName": MessageLookupByLibrary.simpleMessage("Họ"),
    "firstNameIsRequired": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập họ",
    ),
    "firstRenewal": MessageLookupByLibrary.simpleMessage("Gia hạn lần đầu"),
    "fixedCartDiscount": MessageLookupByLibrary.simpleMessage(
      "Giảm giá cố định",
    ),
    "fixedProductDiscount": MessageLookupByLibrary.simpleMessage(
      "Giảm giá sản phẩm cố định",
    ),
    "forThisProduct": MessageLookupByLibrary.simpleMessage("cho sản phẩm này"),
    "free": MessageLookupByLibrary.simpleMessage("Miễn phí"),
    "freeOfCharge": MessageLookupByLibrary.simpleMessage("Miễn phí"),
    "french": MessageLookupByLibrary.simpleMessage("Tiếng Pháp"),
    "friday": MessageLookupByLibrary.simpleMessage("thứ sáu"),
    "from": MessageLookupByLibrary.simpleMessage("Từ"),
    "fullName": MessageLookupByLibrary.simpleMessage("Họ và tên"),
    "gallery": MessageLookupByLibrary.simpleMessage("BỘ SƯU TẬP"),
    "generalError": MessageLookupByLibrary.simpleMessage(
      "Có lỗi xảy ra. Vui lòng thử lại.",
    ),
    "generalSetting": MessageLookupByLibrary.simpleMessage("Cài đặt chung"),
    "generatingLink": MessageLookupByLibrary.simpleMessage(
      "Đang tạo liên kết ...",
    ),
    "german": MessageLookupByLibrary.simpleMessage("Tiếng Đức"),
    "getNotification": MessageLookupByLibrary.simpleMessage("Nhận thông báo"),
    "getNotified": MessageLookupByLibrary.simpleMessage("Nhận được thông báo!"),
    "getPasswordLink": MessageLookupByLibrary.simpleMessage(
      "Lấy liên kết mật khẩu",
    ),
    "getStarted": MessageLookupByLibrary.simpleMessage("Bắt đầu"),
    "goBack": MessageLookupByLibrary.simpleMessage("Quay lại"),
    "goBackHomePage": MessageLookupByLibrary.simpleMessage("Quay về trang chủ"),
    "goBackToAddress": MessageLookupByLibrary.simpleMessage("Quay lại địa chỉ"),
    "goBackToReview": MessageLookupByLibrary.simpleMessage("Quay lại xem lại"),
    "goBackToShipping": MessageLookupByLibrary.simpleMessage(
      "Quay lại vận chuyển",
    ),
    "gold": MessageLookupByLibrary.simpleMessage("Vàng"),
    "goldPriority": MessageLookupByLibrary.simpleMessage("Ưu tiên vàng"),
    "good": MessageLookupByLibrary.simpleMessage("Tốt"),
    "graphqlAuthError": MessageLookupByLibrary.simpleMessage(
      "Xác thực không thành công. Vui lòng đăng nhập lại.",
    ),
    "graphqlAuthzError": MessageLookupByLibrary.simpleMessage(
      "Bạn không có quyền thực hiện hành động này.",
    ),
    "graphqlError": MessageLookupByLibrary.simpleMessage(
      "Có gì đó không ổn khi cố gắng thực hiện hành động này. Vui lòng kiểm tra lại",
    ),
    "graphqlValidationError": MessageLookupByLibrary.simpleMessage(
      "Dữ liệu được cung cấp không hợp lệ. Vui lòng kiểm tra thông tin đầu vào của bạn.",
    ),
    "greaterDistance": m26,
    "greek": MessageLookupByLibrary.simpleMessage("Tiếng Hy Lạp"),
    "grossSales": MessageLookupByLibrary.simpleMessage("Tổng doanh thu"),
    "grouped": MessageLookupByLibrary.simpleMessage("Đã nhóm"),
    "guests": MessageLookupByLibrary.simpleMessage("Khách"),
    "hasBeenDeleted": MessageLookupByLibrary.simpleMessage("đã bị xóa"),
    "hebrew": MessageLookupByLibrary.simpleMessage("Tiếng Do Thái"),
    "hideAbout": MessageLookupByLibrary.simpleMessage("Ẩn thông tin"),
    "hideAddress": MessageLookupByLibrary.simpleMessage("Ẩn địa chỉ"),
    "hideEmail": MessageLookupByLibrary.simpleMessage("Ẩn email"),
    "hideMap": MessageLookupByLibrary.simpleMessage("Ẩn bản đồ"),
    "hidePhone": MessageLookupByLibrary.simpleMessage("Ẩn số điện thoại"),
    "hidePolicy": MessageLookupByLibrary.simpleMessage("Ẩn chính sách"),
    "hindi": MessageLookupByLibrary.simpleMessage("Tiếng Hindi"),
    "history": MessageLookupByLibrary.simpleMessage("Lịch sử"),
    "historyTransaction": MessageLookupByLibrary.simpleMessage(
      "Lịch sử giao dịch",
    ),
    "home": MessageLookupByLibrary.simpleMessage("Trang chủ"),
    "horizontal": MessageLookupByLibrary.simpleMessage("Nằm ngang"),
    "hour": MessageLookupByLibrary.simpleMessage("Giờ"),
    "hoursAgo": m27,
    "howToEarnPoints": MessageLookupByLibrary.simpleMessage(
      "Làm thế nào để kiếm được điểm?",
    ),
    "hungarian": MessageLookupByLibrary.simpleMessage("Tiếng Hungary"),
    "hungary": MessageLookupByLibrary.simpleMessage("Hungary"),
    "iAgree": MessageLookupByLibrary.simpleMessage("Tôi đồng ý với"),
    "imIn": MessageLookupByLibrary.simpleMessage("Tôi tham gia"),
    "imageFeature": MessageLookupByLibrary.simpleMessage("Ảnh đặc trưng"),
    "imageGallery": MessageLookupByLibrary.simpleMessage("Thư viện ảnh"),
    "imageGenerate": MessageLookupByLibrary.simpleMessage("Tạo hình ảnh"),
    "imageNetwork": MessageLookupByLibrary.simpleMessage("Mạng hình ảnh"),
    "images": MessageLookupByLibrary.simpleMessage("Hình ảnh"),
    "inStock": MessageLookupByLibrary.simpleMessage("Còn hàng"),
    "incorrectPassword": MessageLookupByLibrary.simpleMessage("Sai mật khẩu"),
    "india": MessageLookupByLibrary.simpleMessage("Hindi"),
    "indonesian": MessageLookupByLibrary.simpleMessage("Tiếng Indonesia"),
    "informationTable": MessageLookupByLibrary.simpleMessage("Bảng thông tin"),
    "installDigitsPlugin": MessageLookupByLibrary.simpleMessage(
      "Vui lòng cài đặt plugin DIGITS: Wordpress Mobile Number Signup and Login",
    ),
    "instantlyClose": MessageLookupByLibrary.simpleMessage("Đóng ngay lập tức"),
    "insufficientBalanceMessage": m28,
    "invalidAddress": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập địa chỉ hợp lệ",
    ),
    "invalidAddressFormat": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập địa chỉ đầy đủ với tên đường và số nhà",
    ),
    "invalidCity": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập tên thành phố hợp lệ",
    ),
    "invalidCityFormat": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập tên thành phố hợp lệ không có ký tự đặc biệt",
    ),
    "invalidCountry": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn một quốc gia hợp lệ",
    ),
    "invalidCountryCode": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn mã quốc gia hợp lệ",
    ),
    "invalidCountryCodeFormat": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn mã quốc gia hợp lệ từ danh sách",
    ),
    "invalidCountryFormat": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn một quốc gia từ danh sách",
    ),
    "invalidEmail": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập địa chỉ email hợp lệ",
    ),
    "invalidEmailFormat": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập định dạng email hợp lệ (ví dụ: <EMAIL>)",
    ),
    "invalidPhone": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập số điện thoại hợp lệ",
    ),
    "invalidPhoneFormat": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập định dạng số điện thoại hợp lệ",
    ),
    "invalidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Số điện thoại không hợp lệ",
    ),
    "invalidPostalCode": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập mã bưu chính hợp lệ",
    ),
    "invalidPostalCodeFormat": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập định dạng mã bưu chính hợp lệ cho quốc gia của bạn",
    ),
    "invalidProvince": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập tỉnh/tiểu bang hợp lệ",
    ),
    "invalidProvinceFormat": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập tên tỉnh/tiểu bang hợp lệ",
    ),
    "invalidSMSCode": MessageLookupByLibrary.simpleMessage(
      "Mã xác minh SMS không hợp lệ",
    ),
    "invalidYearOfBirth": MessageLookupByLibrary.simpleMessage(
      "Năm sinh không hợp lệ",
    ),
    "invoice": MessageLookupByLibrary.simpleMessage("Hóa đơn"),
    "isEverythingSet": MessageLookupByLibrary.simpleMessage(
      "Mọi thứ đã sẵn sàng...?",
    ),
    "isTyping": MessageLookupByLibrary.simpleMessage("đang nhập..."),
    "italian": MessageLookupByLibrary.simpleMessage("Tiếng Ý"),
    "item": MessageLookupByLibrary.simpleMessage("Mục"),
    "itemCondition": MessageLookupByLibrary.simpleMessage(
      "Tình trạng sản phẩm",
    ),
    "itemConditionNew": MessageLookupByLibrary.simpleMessage("mới"),
    "itemTotal": MessageLookupByLibrary.simpleMessage("Tổng mục:"),
    "items": MessageLookupByLibrary.simpleMessage("sản phẩm"),
    "itsOrdered": MessageLookupByLibrary.simpleMessage("Đã đặt hàng!"),
    "iwantToCreateAccount": MessageLookupByLibrary.simpleMessage(
      "Tôi muốn tạo tài khoản",
    ),
    "japanese": MessageLookupByLibrary.simpleMessage("Tiếng Nhật"),
    "kannada": MessageLookupByLibrary.simpleMessage("Tiếng Kannada"),
    "keep": MessageLookupByLibrary.simpleMessage("Giữ lại"),
    "khmer": MessageLookupByLibrary.simpleMessage("Tiếng Khmer"),
    "korean": MessageLookupByLibrary.simpleMessage("Tiếng Hàn"),
    "kurdish": MessageLookupByLibrary.simpleMessage("Tiếng Kurdish"),
    "language": MessageLookupByLibrary.simpleMessage("Ngôn ngữ"),
    "languageSuccess": MessageLookupByLibrary.simpleMessage(
      "Cập nhật ngôn ngữ thành công",
    ),
    "lao": MessageLookupByLibrary.simpleMessage("Tiếng Lào"),
    "lastName": MessageLookupByLibrary.simpleMessage("Tên"),
    "lastNameIsRequired": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập tên",
    ),
    "lastTransactions": MessageLookupByLibrary.simpleMessage(
      "Giao dịch gần đây",
    ),
    "latestProducts": MessageLookupByLibrary.simpleMessage("Sản phẩm mới nhất"),
    "layout": MessageLookupByLibrary.simpleMessage("Bố cục"),
    "lightTheme": MessageLookupByLibrary.simpleMessage("Chủ đề sáng"),
    "link": MessageLookupByLibrary.simpleMessage("Liên kết"),
    "list": MessageLookupByLibrary.simpleMessage("Danh sách"),
    "listBannerType": MessageLookupByLibrary.simpleMessage(
      "Loại banner danh sách",
    ),
    "listBannerVideo": MessageLookupByLibrary.simpleMessage(
      "Danh sách video banner",
    ),
    "listMessages": MessageLookupByLibrary.simpleMessage("Danh sách tin nhắn"),
    "listTile": MessageLookupByLibrary.simpleMessage("Danh sách Ngói"),
    "listening": MessageLookupByLibrary.simpleMessage("Đang nghe..."),
    "loadFail": MessageLookupByLibrary.simpleMessage(
      "Tải thất bại. Vui lòng thử lại!",
    ),
    "loadFailed": MessageLookupByLibrary.simpleMessage("Tải không thành công!"),
    "loading": MessageLookupByLibrary.simpleMessage("Đang tải..."),
    "loadingLink": MessageLookupByLibrary.simpleMessage("Đang tải liên kết..."),
    "location": MessageLookupByLibrary.simpleMessage("Vị trí"),
    "lockScreenAndSecurity": MessageLookupByLibrary.simpleMessage(
      "Màn hình khóa và bảo mật",
    ),
    "login": MessageLookupByLibrary.simpleMessage("Đăng nhập"),
    "loginCanceled": MessageLookupByLibrary.simpleMessage("Đăng nhập bị hủy"),
    "loginErrorServiceProvider": m29,
    "loginFailed": MessageLookupByLibrary.simpleMessage("Đăng nhập thất bại!"),
    "loginInvalid": MessageLookupByLibrary.simpleMessage(
      "Bạn không được phép sử dụng ứng dụng này.",
    ),
    "loginRequired": MessageLookupByLibrary.simpleMessage("Yêu cầu đăng nhập"),
    "loginSuccess": MessageLookupByLibrary.simpleMessage(
      "Đăng nhập thành công!",
    ),
    "loginToComment": MessageLookupByLibrary.simpleMessage(
      "Vui lòng đăng nhập để bình luận",
    ),
    "loginToContinue": MessageLookupByLibrary.simpleMessage(
      "Vui lòng đăng nhập để tiếp tục",
    ),
    "loginToReview": MessageLookupByLibrary.simpleMessage(
      "Vui lòng đăng nhập để xem xét",
    ),
    "loginToYourAccount": MessageLookupByLibrary.simpleMessage(
      "Đăng nhập vào tài khoản của bạn",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("Đăng xuất"),
    "logoutFailed": MessageLookupByLibrary.simpleMessage(
      "Đăng xuất không thành công",
    ),
    "logoutSuccess": MessageLookupByLibrary.simpleMessage(
      "Đăng xuất thành công",
    ),
    "loyaltyVoucher": MessageLookupByLibrary.simpleMessage(
      "Phiếu mua hàng trung thành",
    ),
    "malay": MessageLookupByLibrary.simpleMessage("Tiếng Mã Lai"),
    "manCollections": MessageLookupByLibrary.simpleMessage("Bộ sưu tập nam"),
    "manageApiKey": MessageLookupByLibrary.simpleMessage("Quản lý khóa API"),
    "manageStock": MessageLookupByLibrary.simpleMessage("Quản lý kho"),
    "map": MessageLookupByLibrary.simpleMessage("Bản đồ"),
    "marathi": MessageLookupByLibrary.simpleMessage("Tiếng Marathi"),
    "markAsRead": MessageLookupByLibrary.simpleMessage("Đánh dấu đã đọc"),
    "markAsShipped": MessageLookupByLibrary.simpleMessage(
      "Đánh dấu đã giao hàng",
    ),
    "markAsUnread": MessageLookupByLibrary.simpleMessage("Đánh dấu chưa đọc"),
    "maxAmountForPayment": m30,
    "maximumFileSizeMb": m31,
    "maybeLater": MessageLookupByLibrary.simpleMessage("Có thể để sau"),
    "menuOrder": MessageLookupByLibrary.simpleMessage("Thứ tự menu"),
    "menuServiceItems": m32,
    "menus": MessageLookupByLibrary.simpleMessage("Thực đơn"),
    "message": MessageLookupByLibrary.simpleMessage("Tin nhắn"),
    "messageTo": MessageLookupByLibrary.simpleMessage("Gửi tin nhắn đến"),
    "minAmountForPayment": m33,
    "minOrderAmount": m34,
    "minTotalCouponInvalidMsg": m35,
    "minTransaction": m36,
    "minimumQuantityIs": MessageLookupByLibrary.simpleMessage(
      "Số lượng tối thiểu là",
    ),
    "minutesAgo": m37,
    "mobile": MessageLookupByLibrary.simpleMessage("Điện thoại di động"),
    "mobileIsRequired": MessageLookupByLibrary.simpleMessage(
      "Điện thoại di động là bắt buộc",
    ),
    "mobileNumberInUse": MessageLookupByLibrary.simpleMessage(
      "Số điện thoại di động đã được sử dụng!",
    ),
    "mobileNumberIsNotRegistered": MessageLookupByLibrary.simpleMessage(
      "Số điện thoại chưa được đăng ký!",
    ),
    "mobileVerification": MessageLookupByLibrary.simpleMessage(
      "Xác thực điện thoại",
    ),
    "momentAgo": MessageLookupByLibrary.simpleMessage("vừa xong"),
    "monday": MessageLookupByLibrary.simpleMessage("thứ hai"),
    "monthsAgo": m38,
    "more": MessageLookupByLibrary.simpleMessage("...Thêm"),
    "moreFromStore": m39,
    "moreInformation": MessageLookupByLibrary.simpleMessage("Thêm thông tin"),
    "morning": MessageLookupByLibrary.simpleMessage("Sáng"),
    "multipleSellersDetected": MessageLookupByLibrary.simpleMessage(
      "Nhiều người bán được phát hiện",
    ),
    "multipleSellersDetectedAndDisableMultiVendorCheckoutContent":
        MessageLookupByLibrary.simpleMessage(
          "Bạn đang cố gắng thêm sản phẩm từ người bán mới vào giỏ hàng của mình. Xin lưu ý rằng bạn chỉ có thể mua từ một người bán tại một thời điểm.",
        ),
    "multipleSellersDetectedAndEnableMultiVendorCheckoutContent":
        MessageLookupByLibrary.simpleMessage(
          "Bạn đang cố gắng thêm sản phẩm từ người bán mới vào giỏ hàng của mình. Bạn có muốn tiếp tục không?",
        ),
    "mustBeBoughtInGroupsOf": m40,
    "mustSelectOneItem": MessageLookupByLibrary.simpleMessage(
      "Phải chọn 1 mục",
    ),
    "myCart": MessageLookupByLibrary.simpleMessage("Giỏ hàng của tôi"),
    "myCoupons": MessageLookupByLibrary.simpleMessage("Phiếu giảm giá của tôi"),
    "myOrder": MessageLookupByLibrary.simpleMessage("Đơn hàng của tôi"),
    "myPoints": MessageLookupByLibrary.simpleMessage("Điểm của tôi"),
    "myProducts": MessageLookupByLibrary.simpleMessage("Sản phẩm của tôi"),
    "myProductsEmpty": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa có sản phẩm nào. Hãy thử tạo một sản phẩm!",
    ),
    "myQRCode": MessageLookupByLibrary.simpleMessage("Mã QR của tôi"),
    "myQRCodeNote": MessageLookupByLibrary.simpleMessage(
      "Cung cấp mã này cho nhân viên để",
    ),
    "myRating": MessageLookupByLibrary.simpleMessage("Đánh giá của tôi"),
    "myReviews": MessageLookupByLibrary.simpleMessage("Đánh giá của tôi"),
    "myWallet": MessageLookupByLibrary.simpleMessage("Ví của tôi"),
    "myWishList": MessageLookupByLibrary.simpleMessage(
      "Danh sách yêu thích của tôi",
    ),
    "nItems": m41,
    "name": MessageLookupByLibrary.simpleMessage("Tên"),
    "nameOnCard": MessageLookupByLibrary.simpleMessage("Tên trên thẻ"),
    "nearbyPlaces": MessageLookupByLibrary.simpleMessage("Địa điểm gần đây"),
    "needHelp": MessageLookupByLibrary.simpleMessage("Cần giúp đỡ? "),
    "needToLoginAgain": MessageLookupByLibrary.simpleMessage(
      "Bạn cần đăng nhập lại để cập nhật có hiệu lực",
    ),
    "netherlands": MessageLookupByLibrary.simpleMessage("Hà Lan"),
    "networkError": MessageLookupByLibrary.simpleMessage(
      "Vui lòng kiểm tra lại mạng của bạn",
    ),
    "networkServerError": MessageLookupByLibrary.simpleMessage(
      "Lỗi máy chủ. Vui lòng thử lại sau.",
    ),
    "networkTimeout": MessageLookupByLibrary.simpleMessage(
      "Hết thời gian kết nối. Vui lòng thử lại.",
    ),
    "newAppConfig": MessageLookupByLibrary.simpleMessage(
      "Nội dung mới có sẵn!",
    ),
    "newPassword": MessageLookupByLibrary.simpleMessage("Mật khẩu mới"),
    "newVariation": MessageLookupByLibrary.simpleMessage("Biến thể mới"),
    "next": MessageLookupByLibrary.simpleMessage("Tiếp theo"),
    "niceName": MessageLookupByLibrary.simpleMessage("Tên"),
    "no": MessageLookupByLibrary.simpleMessage("Không"),
    "noAddressHaveBeenSaved": MessageLookupByLibrary.simpleMessage(
      "Chưa có địa chỉ nào được lưu.",
    ),
    "noBackHistoryItem": MessageLookupByLibrary.simpleMessage(
      "Không có lịch sử quay lại",
    ),
    "noBlog": MessageLookupByLibrary.simpleMessage(
      "Rất tiếc, blog không còn tồn tại",
    ),
    "noCameraPermissionIsGranted": MessageLookupByLibrary.simpleMessage(
      "Không có quyền đối với máy ảnh được cấp. Vui lòng cấp nó trong Cài đặt của thiết bị của bạn.",
    ),
    "noComments": MessageLookupByLibrary.simpleMessage("Không có bình luận"),
    "noConversation": MessageLookupByLibrary.simpleMessage(
      "Chưa có cuộc trò chuyện nào",
    ),
    "noConversationDescription": MessageLookupByLibrary.simpleMessage(
      "Nó sẽ xuất hiện khi ai đó bắt đầu trò chuyện với bạn",
    ),
    "noData": MessageLookupByLibrary.simpleMessage("Không có dữ liệu"),
    "noFavoritesYet": MessageLookupByLibrary.simpleMessage(
      "Chưa có sản phẩm yêu thích",
    ),
    "noFileToDownload": MessageLookupByLibrary.simpleMessage(
      "Không có tệp để tải xuống.",
    ),
    "noForwardHistoryItem": MessageLookupByLibrary.simpleMessage(
      "Không có lịch sử chuyển tiếp",
    ),
    "noInternetConnection": MessageLookupByLibrary.simpleMessage(
      "Không có kết nối internet",
    ),
    "noListingNearby": MessageLookupByLibrary.simpleMessage(
      "Không có cửa hàng nào gần đây!",
    ),
    "noOrders": MessageLookupByLibrary.simpleMessage("Không có đơn hàng"),
    "noPaymentMethodsAvailable": MessageLookupByLibrary.simpleMessage(
      "Hiện không có phương thức thanh toán nào",
    ),
    "noPermissionForCurrentRole": MessageLookupByLibrary.simpleMessage(
      "Rất tiếc, sản phẩm này không thể truy cập được đối với vai trò hiện tại của bạn.",
    ),
    "noPermissionToViewProduct": MessageLookupByLibrary.simpleMessage(
      "Sản phẩm này có sẵn cho người dùng với các vai trò cụ thể. Vui lòng đăng nhập bằng thông tin đăng nhập thích hợp để truy cập sản phẩm này hoặc liên hệ với chúng tôi để biết thêm thông tin.",
    ),
    "noPermissionToViewProductMsg": MessageLookupByLibrary.simpleMessage(
      "Vui lòng đăng nhập bằng thông tin xác thực phù hợp để truy cập sản phẩm này hoặc liên hệ với chúng tôi để biết thêm thông tin.",
    ),
    "noPost": MessageLookupByLibrary.simpleMessage(
      "Rất tiếc, trang này không còn tồn tại!",
    ),
    "noPrinters": MessageLookupByLibrary.simpleMessage("Không có máy in"),
    "noProduct": MessageLookupByLibrary.simpleMessage("Không có sản phẩm"),
    "noResultFound": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy kết quả",
    ),
    "noReviews": MessageLookupByLibrary.simpleMessage("Chưa có đánh giá"),
    "noSlotAvailable": MessageLookupByLibrary.simpleMessage(
      "Không có chỗ trống",
    ),
    "noStoreNearby": MessageLookupByLibrary.simpleMessage(
      "Không có cửa hàng gần đó!",
    ),
    "noSuggestionSearch": MessageLookupByLibrary.simpleMessage(
      "Không có gợi ý nào",
    ),
    "noThanks": MessageLookupByLibrary.simpleMessage("Không, cảm ơn"),
    "noTransactionsMsg": MessageLookupByLibrary.simpleMessage(
      "Rất tiếc, không tìm thấy giao dịch nào!",
    ),
    "noVideoFound": MessageLookupByLibrary.simpleMessage(
      "Xin lỗi, không tìm thấy video.",
    ),
    "none": MessageLookupByLibrary.simpleMessage("Không có"),
    "normal": MessageLookupByLibrary.simpleMessage("Bình thường"),
    "notFindResult": MessageLookupByLibrary.simpleMessage(
      "Xin lỗi, chúng tôi không thể tìm thấy bất kỳ kết quả.",
    ),
    "notFound": MessageLookupByLibrary.simpleMessage("Không tìm thấy"),
    "notRated": MessageLookupByLibrary.simpleMessage("Không được đánh giá"),
    "note": MessageLookupByLibrary.simpleMessage("Ghi chú đơn hàng"),
    "noteMessage": MessageLookupByLibrary.simpleMessage("Lưu ý"),
    "noteOptional": MessageLookupByLibrary.simpleMessage("Lưu ý (tùy chọn)"),
    "noteTransfer": MessageLookupByLibrary.simpleMessage(
      "Ghi chú (không bắt buộc)",
    ),
    "notice": MessageLookupByLibrary.simpleMessage("Lưu ý"),
    "notifications": MessageLookupByLibrary.simpleMessage("Thông báo"),
    "notifyLatestOffer": MessageLookupByLibrary.simpleMessage(
      "Thông báo các ưu đãi mới nhất và sản phẩm còn hàng",
    ),
    "ofThisProduct": MessageLookupByLibrary.simpleMessage("của sản phẩm này"),
    "ok": MessageLookupByLibrary.simpleMessage("OK"),
    "on": MessageLookupByLibrary.simpleMessage("Bật"),
    "onSale": MessageLookupByLibrary.simpleMessage("Đang giảm giá"),
    "onVacation": MessageLookupByLibrary.simpleMessage("Đang nghỉ"),
    "oneEachRecipient": MessageLookupByLibrary.simpleMessage(
      "1 cho mỗi người nhận",
    ),
    "online": MessageLookupByLibrary.simpleMessage("Trực tuyến"),
    "open24Hours": MessageLookupByLibrary.simpleMessage("Mở cửa 24h"),
    "openMap": MessageLookupByLibrary.simpleMessage("Mở bản đồ"),
    "openNow": MessageLookupByLibrary.simpleMessage("Mở cửa"),
    "openSettings": MessageLookupByLibrary.simpleMessage("Mở Cài đặt"),
    "openingHours": MessageLookupByLibrary.simpleMessage("Giờ mở cửa"),
    "optional": MessageLookupByLibrary.simpleMessage("Không bắt buộc"),
    "options": MessageLookupByLibrary.simpleMessage("Tùy chọn"),
    "optionsTotal": m42,
    "or": MessageLookupByLibrary.simpleMessage("Hoặc"),
    "orLoginWith": MessageLookupByLibrary.simpleMessage("Hoặc đăng nhập bằng"),
    "orderConfirmation": MessageLookupByLibrary.simpleMessage(
      "Xác nhận đơn hàng",
    ),
    "orderConfirmationMsg": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc chắn tạo đơn hàng không?",
    ),
    "orderDate": MessageLookupByLibrary.simpleMessage("Ngày đặt hàng"),
    "orderDetail": MessageLookupByLibrary.simpleMessage("Chi tiết đơn hàng"),
    "orderHistory": MessageLookupByLibrary.simpleMessage("Lịch sử đặt hàng"),
    "orderId": MessageLookupByLibrary.simpleMessage("Mã đơn hàng:"),
    "orderIdWithoutColon": MessageLookupByLibrary.simpleMessage("Mã đơn hàng"),
    "orderNo": MessageLookupByLibrary.simpleMessage("Mã đơn hàng"),
    "orderNotes": MessageLookupByLibrary.simpleMessage("Ghi chú đơn hàng"),
    "orderNumber": MessageLookupByLibrary.simpleMessage("Mã đơn hàng"),
    "orderStatusCanceledReversal": MessageLookupByLibrary.simpleMessage(
      "Đã hủy hoàn tiền",
    ),
    "orderStatusCancelled": MessageLookupByLibrary.simpleMessage("Đã hủy"),
    "orderStatusChargeBack": MessageLookupByLibrary.simpleMessage("Hoàn tiền"),
    "orderStatusCompleted": MessageLookupByLibrary.simpleMessage("Hoàn thành"),
    "orderStatusDenied": MessageLookupByLibrary.simpleMessage("Từ chối"),
    "orderStatusExpired": MessageLookupByLibrary.simpleMessage("Hết hạn"),
    "orderStatusFailed": MessageLookupByLibrary.simpleMessage("Thất bại"),
    "orderStatusOnHold": MessageLookupByLibrary.simpleMessage("Đang chờ xử lý"),
    "orderStatusPending": MessageLookupByLibrary.simpleMessage(
      "Đang chờ xử lý",
    ),
    "orderStatusPendingPayment": MessageLookupByLibrary.simpleMessage(
      "Đang chờ thanh toán",
    ),
    "orderStatusProcessed": MessageLookupByLibrary.simpleMessage("Đã xử lý"),
    "orderStatusProcessing": MessageLookupByLibrary.simpleMessage("Đang xử lý"),
    "orderStatusRefunded": MessageLookupByLibrary.simpleMessage("Đã hoàn tiền"),
    "orderStatusReversed": MessageLookupByLibrary.simpleMessage("Đã hoàn tiền"),
    "orderStatusShipped": MessageLookupByLibrary.simpleMessage("Đã giao hàng"),
    "orderStatusVoided": MessageLookupByLibrary.simpleMessage("Đã hủy bỏ"),
    "orderSuccessMsg1": MessageLookupByLibrary.simpleMessage(
      "Bạn có thể kiểm tra trạng thái đơn hàng của mình bằng tính năng theo dõi đơn hàng của chúng tôi. Bạn sẽ nhận được email xác nhận đơn hàng với chi tiết đơn hàng và đường dẫn để theo dõi tiến trình.",
    ),
    "orderSuccessMsg2": MessageLookupByLibrary.simpleMessage(
      "Bạn có thể đăng nhập vào tài khoản bằng email và mật khẩu đã đặt trước đó. Trên tài khoản của bạn, bạn có thể chỉnh sửa dữ liệu hồ sơ, kiểm tra lịch sử giao dịch, chỉnh sửa đăng ký nhận bản tin.",
    ),
    "orderSuccessTitle1": MessageLookupByLibrary.simpleMessage(
      "Bạn đã đặt hàng thành công",
    ),
    "orderSuccessTitle2": MessageLookupByLibrary.simpleMessage(
      "Tài khoản của bạn",
    ),
    "orderSummary": MessageLookupByLibrary.simpleMessage("Tóm tắt đơn hàng"),
    "orderTotal": MessageLookupByLibrary.simpleMessage("Tổng đơn hàng"),
    "orderTracking": MessageLookupByLibrary.simpleMessage("Theo dõi đơn hàng"),
    "orders": MessageLookupByLibrary.simpleMessage("Đơn hàng"),
    "otpVerification": MessageLookupByLibrary.simpleMessage("Xác minh OTP"),
    "ourBankDetails": MessageLookupByLibrary.simpleMessage(
      "Chi tiết của Ngân hàng chúng tôi",
    ),
    "outOfStock": MessageLookupByLibrary.simpleMessage("Hết hàng"),
    "pageView": MessageLookupByLibrary.simpleMessage("Lượt xem trang"),
    "paid": MessageLookupByLibrary.simpleMessage("Đã thanh toán"),
    "paidStatus": MessageLookupByLibrary.simpleMessage("Trạng thái thanh toán"),
    "password": MessageLookupByLibrary.simpleMessage("Mật khẩu"),
    "passwordIsRequired": MessageLookupByLibrary.simpleMessage(
      "Trường Mật khẩu là bắt buộc",
    ),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "Mật khẩu không phù hợp",
    ),
    "pasteYourImageUrl": MessageLookupByLibrary.simpleMessage(
      "Dán URL hình ảnh của bạn",
    ),
    "payByWallet": MessageLookupByLibrary.simpleMessage("Thanh toán bằng ví"),
    "payNow": MessageLookupByLibrary.simpleMessage("Thanh toán ngay"),
    "payWithAmount": m43,
    "payment": MessageLookupByLibrary.simpleMessage("Thanh toán"),
    "paymentDetailsChangedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Chi tiết thanh toán đã được thay đổi thành công.",
    ),
    "paymentMethod": MessageLookupByLibrary.simpleMessage(
      "Phương thức thanh toán",
    ),
    "paymentMethodIsNotSupported": MessageLookupByLibrary.simpleMessage(
      "Phương thức thanh toán này không được hỗ trợ",
    ),
    "paymentMethods": MessageLookupByLibrary.simpleMessage(
      "Phương thức thanh toán",
    ),
    "paymentSettings": MessageLookupByLibrary.simpleMessage(
      "Cài đặt thanh toán",
    ),
    "paymentSuccessful": MessageLookupByLibrary.simpleMessage(
      "Thanh toán thành công",
    ),
    "pending": MessageLookupByLibrary.simpleMessage("Đang chờ xử lý"),
    "pendingReviews": MessageLookupByLibrary.simpleMessage("Đang chờ đánh giá"),
    "persian": MessageLookupByLibrary.simpleMessage("Tiếng Ba Tư"),
    "phone": MessageLookupByLibrary.simpleMessage("Điện thoại"),
    "phoneEmpty": MessageLookupByLibrary.simpleMessage("Số điện thoại trống"),
    "phoneHintFormat": MessageLookupByLibrary.simpleMessage(
      "Định dạng: +84123456789",
    ),
    "phoneIsRequired": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập số điện thoại",
    ),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("Số điện thoại"),
    "phoneNumberVerification": MessageLookupByLibrary.simpleMessage(
      "Xác minh số điện thoại",
    ),
    "pickADate": MessageLookupByLibrary.simpleMessage("Chọn ngày và giờ"),
    "pickVoucherToApply": MessageLookupByLibrary.simpleMessage(
      "Chọn một chứng từ để áp dụng.",
    ),
    "picking": MessageLookupByLibrary.simpleMessage("Đang lấy hàng"),
    "placeMyOrder": MessageLookupByLibrary.simpleMessage("Đặt hàng"),
    "platinum": MessageLookupByLibrary.simpleMessage("Bạch kim"),
    "platinumPriority": MessageLookupByLibrary.simpleMessage(
      "Ưu tiên Bạch kim",
    ),
    "playAll": MessageLookupByLibrary.simpleMessage("Phát tất cả"),
    "pleaseAddPrice": MessageLookupByLibrary.simpleMessage("Vui lòng thêm giá"),
    "pleaseAgreeTerms": MessageLookupByLibrary.simpleMessage(
      "Vui lòng đồng ý với điều khoản của chúng tôi",
    ),
    "pleaseAllowAccessCameraGallery": MessageLookupByLibrary.simpleMessage(
      "Vui lòng cho phép truy cập vào máy ảnh và thư viện",
    ),
    "pleaseCheckInternet": MessageLookupByLibrary.simpleMessage(
      "Vui lòng kiểm tra kết nối internet!",
    ),
    "pleaseChooseBranch": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn chi nhánh",
    ),
    "pleaseChooseCategory": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn danh mục",
    ),
    "pleaseEnterProductName": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập tên sản phẩm",
    ),
    "pleaseFillCode": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập mã của bạn",
    ),
    "pleaseFillUpAllCellsProperly": MessageLookupByLibrary.simpleMessage(
      "* Vui lòng điền đúng tất cả các ô",
    ),
    "pleaseIncreaseOrDecreaseTheQuantity": MessageLookupByLibrary.simpleMessage(
      "Vui lòng tăng hoặc giảm số lượng để tiếp tục.",
    ),
    "pleaseInput": MessageLookupByLibrary.simpleMessage(
      "Vui lòng điền vào tất cả các trường",
    ),
    "pleaseInputFillAllFields": MessageLookupByLibrary.simpleMessage(
      "Vui lòng điền vào tất cả các trường",
    ),
    "pleaseSelectADate": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn ngày đặt chỗ",
    ),
    "pleaseSelectALocation": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn vị trí",
    ),
    "pleaseSelectAllAttributes": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn một tùy chọn cho mỗi thuộc tính của sản phẩm",
    ),
    "pleaseSelectAttr": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn ít nhất 1 tùy chọn cho mỗi thuộc tính đang hoạt động",
    ),
    "pleaseSelectImages": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn hình ảnh",
    ),
    "pleaseSelectRequiredOptions": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn các tùy chọn bắt buộc!",
    ),
    "pleaseSignInBeforeUploading": MessageLookupByLibrary.simpleMessage(
      "Vui lòng đăng nhập vào tài khoản của bạn trước khi tải lên tệp.",
    ),
    "point": MessageLookupByLibrary.simpleMessage("Điểm"),
    "pointHistory": MessageLookupByLibrary.simpleMessage("Lịch sử điểm"),
    "pointMsgConfigNotFound": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy cấu hình điểm giảm giá trên máy chủ",
    ),
    "pointMsgEnter": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập điểm giảm giá",
    ),
    "pointMsgMaximumDiscountPoint": MessageLookupByLibrary.simpleMessage(
      "Điểm giảm giá tối đa",
    ),
    "pointMsgNotEnough": MessageLookupByLibrary.simpleMessage(
      "Bạn không đủ điểm giảm giá. Tổng điểm giảm giá của bạn là",
    ),
    "pointMsgOverMaximumDiscountPoint": MessageLookupByLibrary.simpleMessage(
      "Bạn đã đạt đến điểm giảm giá tối đa",
    ),
    "pointMsgOverTotalBill": MessageLookupByLibrary.simpleMessage(
      "Tổng giá trị giảm giá vượt quá tổng hóa đơn",
    ),
    "pointMsgRemove": MessageLookupByLibrary.simpleMessage(
      "Đã xóa điểm giảm giá",
    ),
    "pointMsgSuccess": MessageLookupByLibrary.simpleMessage(
      "Đã áp dụng điểm giảm giá thành công",
    ),
    "pointRewardMessage": MessageLookupByLibrary.simpleMessage(
      "Có quy tắc giảm giá để áp dụng điểm của bạn vào giỏ hàng",
    ),
    "points": MessageLookupByLibrary.simpleMessage("Điểm"),
    "pointsAddedMsg": MessageLookupByLibrary.simpleMessage(
      "Điểm đã được cộng vào tài khoản của người dùng.",
    ),
    "pointsAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Điểm đã được thêm thành công",
    ),
    "pointsRedeemedMsg": MessageLookupByLibrary.simpleMessage(
      "Điểm đã được đổi từ tài khoản của người dùng.",
    ),
    "pointsRedeemedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Điểm đã được đổi thành công",
    ),
    "polish": MessageLookupByLibrary.simpleMessage("Tiếng Ba Lan"),
    "poor": MessageLookupByLibrary.simpleMessage("Kém"),
    "popular": MessageLookupByLibrary.simpleMessage("Phổ biến"),
    "popularity": MessageLookupByLibrary.simpleMessage("Phổ biến"),
    "posAddressToolTip": MessageLookupByLibrary.simpleMessage(
      "Địa chỉ này sẽ được lưu vào thiết bị của bạn. Đây KHÔNG phải là địa chỉ người dùng.",
    ),
    "postContent": MessageLookupByLibrary.simpleMessage("Nội dung"),
    "postFail": MessageLookupByLibrary.simpleMessage("Không thể tạo bài viết"),
    "postImageFeature": MessageLookupByLibrary.simpleMessage("Ảnh đặc trưng"),
    "postManagement": MessageLookupByLibrary.simpleMessage("Quản lý bài viết"),
    "postProduct": MessageLookupByLibrary.simpleMessage("Đăng sản phẩm"),
    "postSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Bài viết của bạn đã được tạo thành công",
    ),
    "postTitle": MessageLookupByLibrary.simpleMessage("Tiêu đề"),
    "prepaid": MessageLookupByLibrary.simpleMessage("Trả trước"),
    "prev": MessageLookupByLibrary.simpleMessage("Trước"),
    "preview": MessageLookupByLibrary.simpleMessage("Xem trước"),
    "price": MessageLookupByLibrary.simpleMessage("Giá bán"),
    "priceHighToLow": MessageLookupByLibrary.simpleMessage("Giá: Cao đến thấp"),
    "priceLowToHigh": MessageLookupByLibrary.simpleMessage("Giá: Thấp đến cao"),
    "prices": MessageLookupByLibrary.simpleMessage("Menu"),
    "printReceipt": MessageLookupByLibrary.simpleMessage("In hóa đơn"),
    "printer": MessageLookupByLibrary.simpleMessage("Máy in"),
    "printerNotFound": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy máy in",
    ),
    "printerSelection": MessageLookupByLibrary.simpleMessage("Chọn máy in"),
    "printing": MessageLookupByLibrary.simpleMessage("Đang in..."),
    "privacyAndTerm": MessageLookupByLibrary.simpleMessage(
      "Chính sách bảo mật & Điều khoản",
    ),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Chính sách bảo mật"),
    "privacyTerms": MessageLookupByLibrary.simpleMessage(
      "Quyền riêng tư & Điều khoản",
    ),
    "private": MessageLookupByLibrary.simpleMessage("Riêng tư"),
    "processing": MessageLookupByLibrary.simpleMessage("Đang xử lý..."),
    "product": MessageLookupByLibrary.simpleMessage("Sản phẩm"),
    "productAddToCart": m44,
    "productAdded": MessageLookupByLibrary.simpleMessage("Đã thêm sản phẩm"),
    "productCreateReview": MessageLookupByLibrary.simpleMessage(
      "Sản phẩm của bạn sẽ hiển thị sau khi được duyệt.",
    ),
    "productExpired": MessageLookupByLibrary.simpleMessage(
      "Rất tiếc, sản phẩm này không thể truy cập được vì nó đã hết hạn.",
    ),
    "productName": MessageLookupByLibrary.simpleMessage("Tên sản phẩm"),
    "productNameCanNotEmpty": MessageLookupByLibrary.simpleMessage(
      "Tên sản phẩm không được để trống",
    ),
    "productNeedAtLeastOneVariation": MessageLookupByLibrary.simpleMessage(
      "Sản phẩm dạng biến thể cần ít nhất một biến thể",
    ),
    "productNeedNameAndPrice": MessageLookupByLibrary.simpleMessage(
      "Sản phẩm dạng đơn giản cần tên và giá thông thường",
    ),
    "productOutOfStock": MessageLookupByLibrary.simpleMessage(
      "Sản phẩm này đã hết hàng",
    ),
    "productOverview": MessageLookupByLibrary.simpleMessage(
      "Tổng quan về sản phẩm",
    ),
    "productRating": MessageLookupByLibrary.simpleMessage("Đánh giá sản phẩm"),
    "productReview": MessageLookupByLibrary.simpleMessage("Xem xét sản phẩm"),
    "productType": MessageLookupByLibrary.simpleMessage("Loại sản phẩm"),
    "products": MessageLookupByLibrary.simpleMessage("Sản phẩm"),
    "promptPayID": MessageLookupByLibrary.simpleMessage("PromptPay ID:"),
    "promptPayName": MessageLookupByLibrary.simpleMessage("Tên PromptPay:"),
    "promptPayType": MessageLookupByLibrary.simpleMessage("Loại PromptPay:"),
    "publish": MessageLookupByLibrary.simpleMessage("Đăng"),
    "pullToLoadMore": MessageLookupByLibrary.simpleMessage("Kéo để tải thêm"),
    "pullToRefresh": MessageLookupByLibrary.simpleMessage("Kéo để làm mới"),
    "pullUpLoad": MessageLookupByLibrary.simpleMessage("Kéo lên để tải thêm"),
    "qRCodeMsgSuccess": MessageLookupByLibrary.simpleMessage(
      "Mã QR đã được lưu thành công.",
    ),
    "qRCodeSaveFailure": MessageLookupByLibrary.simpleMessage(
      "Không lưu được mã QR",
    ),
    "qty": MessageLookupByLibrary.simpleMessage("SL"),
    "qtyTotal": m45,
    "quantity": MessageLookupByLibrary.simpleMessage("Số lượng"),
    "quantityProductExceedInStock": MessageLookupByLibrary.simpleMessage(
      "Số lượng hiện tại nhiều hơn số lượng trong kho",
    ),
    "random": MessageLookupByLibrary.simpleMessage("Ngẫu nhiên"),
    "rankDetails": MessageLookupByLibrary.simpleMessage("Chi tiết xếp hạng"),
    "rankDetailsMsg": MessageLookupByLibrary.simpleMessage(
      "Hiện tại bạn là thành viên của cấp bậc này",
    ),
    "rate": MessageLookupByLibrary.simpleMessage("Đánh giá"),
    "rateProduct": MessageLookupByLibrary.simpleMessage("Đánh giá sản phẩm"),
    "rateTheApp": MessageLookupByLibrary.simpleMessage("Đánh giá ứng dụng"),
    "rateThisApp": MessageLookupByLibrary.simpleMessage(
      "Đánh giá ứng dụng này",
    ),
    "rateThisAppDescription": MessageLookupByLibrary.simpleMessage(
      "Nếu bạn thích ứng dụng này, vui lòng dành chút thời gian để đánh giá!\nĐiều này thực sự giúp ích cho chúng tôi và không mất quá một phút.",
    ),
    "rating": MessageLookupByLibrary.simpleMessage("Đánh giá"),
    "ratingFirst": MessageLookupByLibrary.simpleMessage(
      "Vui lòng đánh giá trước khi gửi bình luận",
    ),
    "reOrder": MessageLookupByLibrary.simpleMessage("Đặt lại"),
    "readReviews": MessageLookupByLibrary.simpleMessage("Xem đánh giá"),
    "readyToPick": MessageLookupByLibrary.simpleMessage("Sẵn sàng lấy hàng"),
    "received": MessageLookupByLibrary.simpleMessage("Đã nhận"),
    "receivedMoney": MessageLookupByLibrary.simpleMessage("Đã nhận tiền"),
    "receivedMoneyFrom": m46,
    "receiver": MessageLookupByLibrary.simpleMessage("Người nhận"),
    "recent": MessageLookupByLibrary.simpleMessage("Gần đây"),
    "recentSearches": MessageLookupByLibrary.simpleMessage("Tìm kiếm gần đây"),
    "recentView": MessageLookupByLibrary.simpleMessage("Xem gần đây"),
    "recentlyViewed": MessageLookupByLibrary.simpleMessage("Đã xem gần đây"),
    "recommended": MessageLookupByLibrary.simpleMessage("Đề xuất"),
    "recurringTotals": MessageLookupByLibrary.simpleMessage("Tổng định kỳ"),
    "redeem": MessageLookupByLibrary.simpleMessage("chuộc lại"),
    "redeemPoints": MessageLookupByLibrary.simpleMessage("Đổi điểm"),
    "redeemRewards": MessageLookupByLibrary.simpleMessage("Đổi phần thưởng"),
    "redeemed": MessageLookupByLibrary.simpleMessage("Đã chuộc"),
    "refresh": MessageLookupByLibrary.simpleMessage("Làm mới"),
    "refreshCompleted": MessageLookupByLibrary.simpleMessage(
      "Làm mới hoàn tất",
    ),
    "refreshing": MessageLookupByLibrary.simpleMessage("Thật sảng khoái..."),
    "refund": MessageLookupByLibrary.simpleMessage("Hoàn tiền"),
    "refundOrderFailed": MessageLookupByLibrary.simpleMessage(
      "Yêu cầu hoàn tiền đơn hàng thất bại",
    ),
    "refundOrderSuccess": MessageLookupByLibrary.simpleMessage(
      "Yêu cầu hoàn tiền đơn hàng thành công!",
    ),
    "refundRequest": MessageLookupByLibrary.simpleMessage("Yêu cầu hoàn tiền"),
    "refundRequested": MessageLookupByLibrary.simpleMessage(
      "Đã yêu cầu hoàn tiền",
    ),
    "refunds": MessageLookupByLibrary.simpleMessage("Hoàn tiền"),
    "regenerateResponse": MessageLookupByLibrary.simpleMessage(
      "Tạo lại phản hồi",
    ),
    "registerAs": MessageLookupByLibrary.simpleMessage("Đăng ký như"),
    "registerAsVendor": MessageLookupByLibrary.simpleMessage(
      "Đăng ký làm người bán",
    ),
    "registerErrorSyncAccount": MessageLookupByLibrary.simpleMessage(
      "Không thể đồng bộ tài khoản. Vui lòng đăng nhập để tiếp tục.",
    ),
    "registerFailed": MessageLookupByLibrary.simpleMessage("Đăng ký thất bại"),
    "registerInvalid": MessageLookupByLibrary.simpleMessage(
      "Đăng ký không hợp lệ hoặc đã hết hạn, vui lòng thử lại",
    ),
    "registerSuccess": MessageLookupByLibrary.simpleMessage(
      "Đăng ký thành công",
    ),
    "regularPrice": MessageLookupByLibrary.simpleMessage("Giá gốc"),
    "relatedLayoutTitle": MessageLookupByLibrary.simpleMessage(
      "Có thể bạn thích",
    ),
    "releaseToLoadMore": MessageLookupByLibrary.simpleMessage(
      "Thả để tải thêm",
    ),
    "releaseToRefresh": MessageLookupByLibrary.simpleMessage(
      "Phát hành để làm mới",
    ),
    "remainingAmountCod": MessageLookupByLibrary.simpleMessage(
      "Số tiền còn lại phải trả khi giao hàng",
    ),
    "remove": MessageLookupByLibrary.simpleMessage("Xóa"),
    "removeFromWishList": MessageLookupByLibrary.simpleMessage(
      "Xóa khỏi danh sách yêu thích",
    ),
    "removeWishlist": MessageLookupByLibrary.simpleMessage(
      "Xóa khỏi danh sách mong muốn",
    ),
    "removeWishlistContent": m47,
    "requestBooking": MessageLookupByLibrary.simpleMessage("Yêu cầu đặt chỗ"),
    "requestTooMany": MessageLookupByLibrary.simpleMessage(
      "Bạn đã yêu cầu quá nhiều mã trong thời gian ngắn. Vui lòng thử lại sau.",
    ),
    "resend": MessageLookupByLibrary.simpleMessage("Gửi lại"),
    "reservePrice": MessageLookupByLibrary.simpleMessage("Giá dự trữ"),
    "reset": MessageLookupByLibrary.simpleMessage("Đặt lại"),
    "resetPassword": MessageLookupByLibrary.simpleMessage("Đặt lại mật khẩu"),
    "resetYourPassword": MessageLookupByLibrary.simpleMessage(
      "Đặt lại mật khẩu của bạn",
    ),
    "results": MessageLookupByLibrary.simpleMessage("Tất cả kết quả"),
    "retry": MessageLookupByLibrary.simpleMessage("Thử lại"),
    "reverse": MessageLookupByLibrary.simpleMessage("Đảo ngược"),
    "review": MessageLookupByLibrary.simpleMessage("Xem trước"),
    "reviewApproval": MessageLookupByLibrary.simpleMessage("Xem xét phê duyệt"),
    "reviewPendingApproval": MessageLookupByLibrary.simpleMessage(
      "Đánh giá của bạn đã được gửi và đang chờ phê duyệt!",
    ),
    "reviewSent": MessageLookupByLibrary.simpleMessage(
      "Đánh giá của bạn đã được gửi!",
    ),
    "reviews": MessageLookupByLibrary.simpleMessage("Đánh giá"),
    "rewards": MessageLookupByLibrary.simpleMessage("Phần thưởng"),
    "romanian": MessageLookupByLibrary.simpleMessage("Tiếng Romania"),
    "russian": MessageLookupByLibrary.simpleMessage("Tiếng Nga"),
    "sale": m48,
    "salePrice": MessageLookupByLibrary.simpleMessage("Giá bán"),
    "saturday": MessageLookupByLibrary.simpleMessage("ngày thứ bảy"),
    "save": MessageLookupByLibrary.simpleMessage("Lưu"),
    "saveAddress": MessageLookupByLibrary.simpleMessage("Lưu địa chỉ"),
    "saveAddressSuccess": MessageLookupByLibrary.simpleMessage(
      "Lưu địa chỉ thành công",
    ),
    "saveForLater": MessageLookupByLibrary.simpleMessage("Lưu để sau"),
    "saveQRCode": MessageLookupByLibrary.simpleMessage("Lưu mã QR"),
    "saveToWishList": MessageLookupByLibrary.simpleMessage(
      "Lưu vào danh sách yêu thích",
    ),
    "scanPoints": MessageLookupByLibrary.simpleMessage("Điểm quét"),
    "scanQRCode": MessageLookupByLibrary.simpleMessage("Quét mã QR"),
    "scannerCannotScan": MessageLookupByLibrary.simpleMessage(
      "Không thể quét mục này",
    ),
    "scannerLoginFirst": MessageLookupByLibrary.simpleMessage(
      "Bạn cần đăng nhập để quét đơn hàng",
    ),
    "scannerOrderAvailable": MessageLookupByLibrary.simpleMessage(
      "Đơn hàng này không khả dụng cho tài khoản của bạn",
    ),
    "search": MessageLookupByLibrary.simpleMessage("Tìm kiếm"),
    "searchByCountryNameOrDialCode": MessageLookupByLibrary.simpleMessage(
      "Tìm kiếm theo tên quốc gia hoặc mã vùng",
    ),
    "searchByName": MessageLookupByLibrary.simpleMessage(
      "Tìm kiếm theo tên...",
    ),
    "searchEmptyDataMessage": MessageLookupByLibrary.simpleMessage(
      "Ối! Có vẻ như không có kết quả nào phù hợp với tiêu chí tìm kiếm của bạn",
    ),
    "searchForItems": MessageLookupByLibrary.simpleMessage("Tìm kiếm sản phẩm"),
    "searchInput": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập nội dung tìm kiếm",
    ),
    "searchOrderId": MessageLookupByLibrary.simpleMessage(
      "Tìm kiếm ID đơn hàng...",
    ),
    "searchPlace": MessageLookupByLibrary.simpleMessage("Tìm kiếm địa điểm"),
    "searchResultFor": m49,
    "searchResultItem": m50,
    "searchResultItems": m51,
    "searchingAddress": MessageLookupByLibrary.simpleMessage(
      "Đang tìm địa chỉ",
    ),
    "secondsAgo": m52,
    "seeAll": MessageLookupByLibrary.simpleMessage("Xem tất cả"),
    "seeNewAppConfig": MessageLookupByLibrary.simpleMessage(
      "Tiếp tục xem nội dung mới trên ứng dụng của bạn.",
    ),
    "seeOrder": MessageLookupByLibrary.simpleMessage("Xem đơn hàng"),
    "seeReviews": MessageLookupByLibrary.simpleMessage("Xem đánh giá"),
    "select": MessageLookupByLibrary.simpleMessage("Chọn"),
    "selectAddress": MessageLookupByLibrary.simpleMessage("Chọn địa chỉ"),
    "selectAll": MessageLookupByLibrary.simpleMessage("Chọn tất cả"),
    "selectDate": MessageLookupByLibrary.simpleMessage("Chọn ngày"),
    "selectDates": MessageLookupByLibrary.simpleMessage("Chọn ngày"),
    "selectFileCancelled": MessageLookupByLibrary.simpleMessage(
      "Đã hủy chọn tệp!",
    ),
    "selectImage": MessageLookupByLibrary.simpleMessage("Chọn hình ảnh"),
    "selectItem": MessageLookupByLibrary.simpleMessage("Chọn mục"),
    "selectNone": MessageLookupByLibrary.simpleMessage("Bỏ chọn tất cả"),
    "selectPrinter": MessageLookupByLibrary.simpleMessage("Chọn máy in"),
    "selectRole": MessageLookupByLibrary.simpleMessage("Chọn vai trò"),
    "selectStore": MessageLookupByLibrary.simpleMessage("Chọn cửa hàng"),
    "selectTheColor": MessageLookupByLibrary.simpleMessage("Chọn màu sắc"),
    "selectTheFile": MessageLookupByLibrary.simpleMessage("Chọn tập tin"),
    "selectThePoint": MessageLookupByLibrary.simpleMessage("Chọn điểm"),
    "selectTheQuantity": MessageLookupByLibrary.simpleMessage("Chọn số lượng"),
    "selectTheSize": MessageLookupByLibrary.simpleMessage("Chọn kích thước"),
    "selectType": MessageLookupByLibrary.simpleMessage("Lựa chọn đối tượng"),
    "selectVoucher": MessageLookupByLibrary.simpleMessage("Chọn chứng từ"),
    "send": MessageLookupByLibrary.simpleMessage("Gửi"),
    "sendBack": MessageLookupByLibrary.simpleMessage("Gửi lại"),
    "sendSMSCode": MessageLookupByLibrary.simpleMessage("Gửi mã"),
    "sendSMStoVendor": MessageLookupByLibrary.simpleMessage(
      "Gửi SMS cho chủ cửa hàng",
    ),
    "sendTo": MessageLookupByLibrary.simpleMessage(
      "Tài khoản bạn muốn chuyển tiền đến (email)",
    ),
    "separateMultipleEmailWithComma": MessageLookupByLibrary.simpleMessage(
      "Phân tách nhiều địa chỉ email bằng dấu phẩy.",
    ),
    "serbian": MessageLookupByLibrary.simpleMessage("Tiếng Serbia"),
    "sessionExpired": MessageLookupByLibrary.simpleMessage(
      "Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại để tiếp tục.",
    ),
    "setAnAddressInSettingPage": MessageLookupByLibrary.simpleMessage(
      "Vui lòng thiết lập địa chỉ trong trang cài đặt",
    ),
    "settings": MessageLookupByLibrary.simpleMessage("Cài đặt"),
    "setup": MessageLookupByLibrary.simpleMessage("Cài đặt"),
    "share": MessageLookupByLibrary.simpleMessage("Chia sẻ"),
    "shareProductData": MessageLookupByLibrary.simpleMessage(
      "Chia sẻ dữ liệu sản phẩm",
    ),
    "shareProductLink": MessageLookupByLibrary.simpleMessage(
      "Chia sẻ liên kết sản phẩm",
    ),
    "shipped": MessageLookupByLibrary.simpleMessage("Đã giao hàng"),
    "shipping": MessageLookupByLibrary.simpleMessage("Vận chuyển"),
    "shippingAddress": MessageLookupByLibrary.simpleMessage(
      "Địa chỉ giao hàng",
    ),
    "shippingFee": MessageLookupByLibrary.simpleMessage("Phí vận chuyển"),
    "shippingMethod": MessageLookupByLibrary.simpleMessage(
      "Phương thức vận chuyển",
    ),
    "shop": MessageLookupByLibrary.simpleMessage("Cửa hàng"),
    "shopEmail": MessageLookupByLibrary.simpleMessage("Email cửa hàng"),
    "shopName": MessageLookupByLibrary.simpleMessage("Tên cửa hàng"),
    "shopOrders": MessageLookupByLibrary.simpleMessage("Đơn đặt hàng"),
    "shopPhone": MessageLookupByLibrary.simpleMessage("Điện thoại cửa hàng"),
    "shopSlug": MessageLookupByLibrary.simpleMessage("Đường dẫn cửa hàng"),
    "shopifyCustomerAccountLoginDescription": MessageLookupByLibrary.simpleMessage(
      "Sử dụng tài khoản Shopify của bạn để đăng nhập và truy cập đơn hàng, địa chỉ và nhiều thông tin khác.",
    ),
    "shopifyCustomerAccountLoginTitle": MessageLookupByLibrary.simpleMessage(
      "Đăng nhập bằng Tài khoản khách hàng Shopify",
    ),
    "shoppingCartItems": m53,
    "shortDescription": MessageLookupByLibrary.simpleMessage("Mô tả ngắn"),
    "showAllMyOrdered": MessageLookupByLibrary.simpleMessage(
      "Xem tất cả đơn hàng",
    ),
    "showDetails": MessageLookupByLibrary.simpleMessage("Hiển thị chi tiết"),
    "showGallery": MessageLookupByLibrary.simpleMessage("Hiển thị thư viện"),
    "showLess": MessageLookupByLibrary.simpleMessage("Hiện ít hơn"),
    "showMore": MessageLookupByLibrary.simpleMessage("Cho xem nhiều hơn"),
    "signIn": MessageLookupByLibrary.simpleMessage("Đăng nhập"),
    "signInWithEmail": MessageLookupByLibrary.simpleMessage(
      "Đăng nhập bằng email",
    ),
    "signUp": MessageLookupByLibrary.simpleMessage("Đăng ký"),
    "signup": MessageLookupByLibrary.simpleMessage("Đăng ký"),
    "silver": MessageLookupByLibrary.simpleMessage("Bạc"),
    "silverPriority": MessageLookupByLibrary.simpleMessage("Ưu tiên Bạc"),
    "simple": MessageLookupByLibrary.simpleMessage("Đơn giản"),
    "simpleList": MessageLookupByLibrary.simpleMessage("Danh sách đơn giản"),
    "size": MessageLookupByLibrary.simpleMessage("Kích thước"),
    "sizeGuide": MessageLookupByLibrary.simpleMessage(
      "Hướng dẫn chọn kích thước",
    ),
    "skip": MessageLookupByLibrary.simpleMessage("Bỏ qua"),
    "sku": MessageLookupByLibrary.simpleMessage("SKU"),
    "slovak": MessageLookupByLibrary.simpleMessage("Tiếng Slovak"),
    "smsCodeExpired": MessageLookupByLibrary.simpleMessage(
      "Mã SMS đã hết hạn. Vui lòng gửi lại mã xác minh để thử lại.",
    ),
    "sold": m54,
    "soldBy": MessageLookupByLibrary.simpleMessage("Bán bởi"),
    "somethingWrong": MessageLookupByLibrary.simpleMessage(
      "Đã xảy ra lỗi. Vui lòng thử lại sau.",
    ),
    "sortBy": MessageLookupByLibrary.simpleMessage("Sắp xếp theo"),
    "sortCode": MessageLookupByLibrary.simpleMessage("Sort code"),
    "spanish": MessageLookupByLibrary.simpleMessage("Tiếng Tây Ban Nha"),
    "speechNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Không khả dụng",
    ),
    "spendAtLeast": MessageLookupByLibrary.simpleMessage("Chi tiêu ít nhất"),
    "startExploring": MessageLookupByLibrary.simpleMessage("Bắt đầu khám phá"),
    "startPrice": MessageLookupByLibrary.simpleMessage("Giá khởi điểm"),
    "startShopping": MessageLookupByLibrary.simpleMessage("Bắt đầu mua sắm"),
    "startingBid": MessageLookupByLibrary.simpleMessage("Giá khởi điểm"),
    "state": MessageLookupByLibrary.simpleMessage("Tiểu bang"),
    "stateIsRequired": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập tỉnh/thành phố",
    ),
    "stateProvince": MessageLookupByLibrary.simpleMessage("Tỉnh/Thành phố"),
    "status": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "stock": MessageLookupByLibrary.simpleMessage("Kho"),
    "stockQuantity": MessageLookupByLibrary.simpleMessage("Số lượng trong kho"),
    "stop": MessageLookupByLibrary.simpleMessage("Dừng"),
    "store": MessageLookupByLibrary.simpleMessage("Cửa hàng"),
    "storeAddress": MessageLookupByLibrary.simpleMessage("Địa chỉ cửa hàng"),
    "storeBanner": MessageLookupByLibrary.simpleMessage("Banner"),
    "storeBrand": MessageLookupByLibrary.simpleMessage("Thương hiệu"),
    "storeClosed": MessageLookupByLibrary.simpleMessage("Cửa hàng đã đóng cửa"),
    "storeEmail": MessageLookupByLibrary.simpleMessage("Email cửa hàng"),
    "storeInformation": MessageLookupByLibrary.simpleMessage(
      "Thông tin cửa hàng",
    ),
    "storeListBanner": MessageLookupByLibrary.simpleMessage(
      "Banner danh sách cửa hàng",
    ),
    "storeLocation": MessageLookupByLibrary.simpleMessage("Vị trí cửa hàng"),
    "storeLocatorSearchPlaceholder": MessageLookupByLibrary.simpleMessage(
      "Nhập địa chỉ/thành phố",
    ),
    "storeLogo": MessageLookupByLibrary.simpleMessage("Logo cửa hàng"),
    "storeMobileBanner": MessageLookupByLibrary.simpleMessage(
      "Banner di động của cửa hàng",
    ),
    "storeSettings": MessageLookupByLibrary.simpleMessage("Cài đặt cửa hàng"),
    "storeSliderBanner": MessageLookupByLibrary.simpleMessage(
      "Banner trượt của cửa hàng",
    ),
    "storeStaticBanner": MessageLookupByLibrary.simpleMessage(
      "Banner tĩnh của cửa hàng",
    ),
    "storeVacation": MessageLookupByLibrary.simpleMessage("Nghỉ cửa hàng"),
    "stores": MessageLookupByLibrary.simpleMessage("Cửa hàng"),
    "street": MessageLookupByLibrary.simpleMessage("Đường"),
    "street2": MessageLookupByLibrary.simpleMessage("Đường 2"),
    "streetIsRequired": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập tên đường",
    ),
    "streetName": MessageLookupByLibrary.simpleMessage("Tên đường"),
    "streetNameApartment": MessageLookupByLibrary.simpleMessage("Căn hộ"),
    "streetNameBlock": MessageLookupByLibrary.simpleMessage("Tòa nhà"),
    "subTitleOrderConfirmed": MessageLookupByLibrary.simpleMessage(
      "Cảm ơn bạn đã đặt hàng. Chúng tôi đang làm việc nhanh chóng để xử lý đơn đặt hàng của bạn. Hãy theo dõi để nhận email xác nhận trong thời gian ngắn",
    ),
    "submit": MessageLookupByLibrary.simpleMessage("Gửi"),
    "submitYourPost": MessageLookupByLibrary.simpleMessage(
      "Gửi bài viết của bạn",
    ),
    "subtotal": MessageLookupByLibrary.simpleMessage("Tạm tính"),
    "successful": MessageLookupByLibrary.simpleMessage("thành công"),
    "sunday": MessageLookupByLibrary.simpleMessage("chủ nhật"),
    "support": MessageLookupByLibrary.simpleMessage("Hỗ trợ"),
    "swahili": MessageLookupByLibrary.simpleMessage("Tiếng Swahili"),
    "swedish": MessageLookupByLibrary.simpleMessage("Tiếng Thụy Điển"),
    "systemError": MessageLookupByLibrary.simpleMessage(
      "Lỗi hệ thống. Vui lòng liên hệ bộ phận hỗ trợ.",
    ),
    "tag": MessageLookupByLibrary.simpleMessage("Nhãn"),
    "tagNotExist": MessageLookupByLibrary.simpleMessage(
      "Thẻ này không tồn tại",
    ),
    "tags": MessageLookupByLibrary.simpleMessage("Thẻ"),
    "takePicture": MessageLookupByLibrary.simpleMessage("Chụp ảnh"),
    "tamil": MessageLookupByLibrary.simpleMessage("Tiếng Tamil"),
    "tapSelectLocation": MessageLookupByLibrary.simpleMessage(
      "Chạm để chọn vị trí này",
    ),
    "tapTheMicToTalk": MessageLookupByLibrary.simpleMessage(
      "Nhấn vào mic để nói",
    ),
    "tax": MessageLookupByLibrary.simpleMessage("Thuế"),
    "teraWallet": MessageLookupByLibrary.simpleMessage("Ví Tera"),
    "terrible": MessageLookupByLibrary.simpleMessage("Tệ"),
    "thailand": MessageLookupByLibrary.simpleMessage("Thái"),
    "theFieldIsRequired": m55,
    "thisDateIsNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Ngày này không có sẵn",
    ),
    "thisFeatureDoesNotSupportTheCurrentLanguage":
        MessageLookupByLibrary.simpleMessage(
          "Tính năng này không hỗ trợ ngôn ngữ hiện tại",
        ),
    "thisIsCustomerRole": MessageLookupByLibrary.simpleMessage(
      "Đây là vai trò của khách hàng",
    ),
    "thisIsDeliveryrRole": MessageLookupByLibrary.simpleMessage(
      "Đây là vai trò giao hàng",
    ),
    "thisIsVendorRole": MessageLookupByLibrary.simpleMessage(
      "Đây là vai trò của nhà cung cấp",
    ),
    "thisItemIsSold": MessageLookupByLibrary.simpleMessage(
      "Mặt hàng này đã được bán",
    ),
    "thisPlatformNotSupportWebview": MessageLookupByLibrary.simpleMessage(
      "Hệ thống này không hỗ trợ webview",
    ),
    "thisProductNotSupport": MessageLookupByLibrary.simpleMessage(
      "Sản phẩm này không được hỗ trợ",
    ),
    "thursday": MessageLookupByLibrary.simpleMessage("thứ năm"),
    "tickets": MessageLookupByLibrary.simpleMessage("Vé"),
    "time": MessageLookupByLibrary.simpleMessage("Thời gian"),
    "timeLeft": MessageLookupByLibrary.simpleMessage("Thời gian còn lại"),
    "title": MessageLookupByLibrary.simpleMessage("Tiêu đề"),
    "titleAToZ": MessageLookupByLibrary.simpleMessage("Tiêu đề: A đến Z"),
    "titleFirst": MessageLookupByLibrary.simpleMessage(
      "Xin vui lòng thêm tiêu đề",
    ),
    "titleZToA": MessageLookupByLibrary.simpleMessage("Tiêu đề: Z đến A"),
    "to": MessageLookupByLibrary.simpleMessage("Đến"),
    "toRate": MessageLookupByLibrary.simpleMessage("Để đánh giá"),
    "tooManyFailedLogin": MessageLookupByLibrary.simpleMessage(
      "Quá nhiều lần đăng nhập không thành công. Vui lòng thử lại sau.",
    ),
    "topUp": MessageLookupByLibrary.simpleMessage("Nạp tiền"),
    "topUpProductNotFound": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy sản phẩm nạp tiền",
    ),
    "total": MessageLookupByLibrary.simpleMessage("Tổng cộng"),
    "totalAmount": MessageLookupByLibrary.simpleMessage("Tổng cộng"),
    "totalCartValue": MessageLookupByLibrary.simpleMessage(
      "Tổng giá trị đơn hàng phải ít nhất",
    ),
    "totalPoints": MessageLookupByLibrary.simpleMessage("Tổng số điểm"),
    "totalPrice": MessageLookupByLibrary.simpleMessage("Tổng giá"),
    "totalProducts": m56,
    "totalTax": MessageLookupByLibrary.simpleMessage("Tổng thuế"),
    "trackingNumberIs": MessageLookupByLibrary.simpleMessage("Mã theo dõi là"),
    "trackingPage": MessageLookupByLibrary.simpleMessage("Trang theo dõi"),
    "transactionCancelled": MessageLookupByLibrary.simpleMessage(
      "Giao dịch đã bị hủy",
    ),
    "transactionDetail": MessageLookupByLibrary.simpleMessage(
      "Chi tiết giao dịch",
    ),
    "transactionFailded": MessageLookupByLibrary.simpleMessage(
      "Giao dịch không thành công",
    ),
    "transactionFailed": MessageLookupByLibrary.simpleMessage(
      "Giao dịch thất bại",
    ),
    "transactionFee": MessageLookupByLibrary.simpleMessage("Phí giao dịch"),
    "transactionResult": MessageLookupByLibrary.simpleMessage(
      "Kết quả giao dịch",
    ),
    "transfer": MessageLookupByLibrary.simpleMessage("Chuyển khoản"),
    "transferConfirm": MessageLookupByLibrary.simpleMessage(
      "Xác nhận chuyển khoản",
    ),
    "transferErrorMessage": MessageLookupByLibrary.simpleMessage(
      "Bạn không thể chuyển khoản cho người dùng này",
    ),
    "transferFailed": MessageLookupByLibrary.simpleMessage(
      "Chuyển khoản thất bại",
    ),
    "transferMoneyTo": m57,
    "transferSuccess": MessageLookupByLibrary.simpleMessage(
      "Chuyển khoản thành công",
    ),
    "tuesday": MessageLookupByLibrary.simpleMessage("thứ ba"),
    "turkish": MessageLookupByLibrary.simpleMessage("Tiếng Thổ Nhĩ Kỳ"),
    "turnOnBle": MessageLookupByLibrary.simpleMessage("Bật Bluetooth"),
    "typeAMessage": MessageLookupByLibrary.simpleMessage("Nhập tin nhắn..."),
    "typeYourMessage": MessageLookupByLibrary.simpleMessage(
      "Nhập tin nhắn của bạn ở đây...",
    ),
    "typing": MessageLookupByLibrary.simpleMessage("Đang nhập..."),
    "ukrainian": MessageLookupByLibrary.simpleMessage("Tiếng Ukraine"),
    "unavailable": MessageLookupByLibrary.simpleMessage("Không có sẵn"),
    "unblock": MessageLookupByLibrary.simpleMessage("Bỏ chặn"),
    "unblockUser": MessageLookupByLibrary.simpleMessage("Bỏ chặn người dùng"),
    "undo": MessageLookupByLibrary.simpleMessage("Hoàn tác"),
    "unexpectedError": MessageLookupByLibrary.simpleMessage(
      "Đã xảy ra lỗi không mong muốn. Vui lòng thử lại.",
    ),
    "unknownError": MessageLookupByLibrary.simpleMessage(
      "Có gì đó không ổn, lỗi không xác định đã xuất hiện",
    ),
    "unpaid": MessageLookupByLibrary.simpleMessage("Chưa thanh toán"),
    "upRankNote1": MessageLookupByLibrary.simpleMessage("Kiếm được nhiều hơn"),
    "upRankNote2": MessageLookupByLibrary.simpleMessage(
      "điểm để tăng thứ hạng này.",
    ),
    "update": MessageLookupByLibrary.simpleMessage("Cập nhật"),
    "updateFailed": MessageLookupByLibrary.simpleMessage("Cập nhật thất bại!"),
    "updateInfo": MessageLookupByLibrary.simpleMessage("Cập nhật thông tin"),
    "updatePassword": MessageLookupByLibrary.simpleMessage("Cập nhật mật khẩu"),
    "updateStatus": MessageLookupByLibrary.simpleMessage("Cập nhật trạng thái"),
    "updateSuccess": MessageLookupByLibrary.simpleMessage(
      "Cập nhật thành công!",
    ),
    "updateUserFailed": MessageLookupByLibrary.simpleMessage(
      "Cập nhật người dùng không thành công",
    ),
    "updateUserInfor": MessageLookupByLibrary.simpleMessage(
      "Cập nhật thông tin người dùng",
    ),
    "uploadFile": MessageLookupByLibrary.simpleMessage("Tải lên tệp"),
    "uploadImage": MessageLookupByLibrary.simpleMessage("Tải lên hình ảnh"),
    "uploadProduct": MessageLookupByLibrary.simpleMessage("Tải lên sản phẩm"),
    "uploading": MessageLookupByLibrary.simpleMessage("Đang tải lên"),
    "url": MessageLookupByLibrary.simpleMessage("URL"),
    "useAmountPoints": m58,
    "useMaximumPointDiscount": m59,
    "useNow": MessageLookupByLibrary.simpleMessage("Sử dụng ngay"),
    "usePoint": MessageLookupByLibrary.simpleMessage("Sử dụng điểm"),
    "useThisImage": MessageLookupByLibrary.simpleMessage(
      "Sử dụng hình ảnh này",
    ),
    "used": MessageLookupByLibrary.simpleMessage("Đã sử dụng"),
    "userExists": MessageLookupByLibrary.simpleMessage(
      "Tên đăng nhập/email này đã tồn tại.",
    ),
    "userHasBeenBlocked": MessageLookupByLibrary.simpleMessage(
      "Người dùng đã bị chặn",
    ),
    "userNameInCorrect": MessageLookupByLibrary.simpleMessage(
      "Tên người dùng hoặc mật khẩu không chính xác.",
    ),
    "userNotFound": MessageLookupByLibrary.simpleMessage(
      "Người dùng không tồn tại",
    ),
    "username": MessageLookupByLibrary.simpleMessage("Tên đăng nhập"),
    "usernameAlreadyInUse": MessageLookupByLibrary.simpleMessage(
      "Tên người dùng đã được sử dụng!",
    ),
    "usernameAndPasswordRequired": MessageLookupByLibrary.simpleMessage(
      "Yêu cầu tên đăng nhập và mật khẩu",
    ),
    "usernameInvalid": MessageLookupByLibrary.simpleMessage(
      "Tên người dùng không hợp lệ",
    ),
    "usernameIsRequired": MessageLookupByLibrary.simpleMessage(
      "Tên người dùng không được bỏ trống",
    ),
    "vacationMessage": MessageLookupByLibrary.simpleMessage("Thông báo nghỉ"),
    "vacationType": MessageLookupByLibrary.simpleMessage("Loại nghỉ"),
    "validUntil": m60,
    "validUntilDate": m61,
    "variable": MessageLookupByLibrary.simpleMessage("Biến thể"),
    "variation": MessageLookupByLibrary.simpleMessage("Biến thể"),
    "vendor": MessageLookupByLibrary.simpleMessage("Nhà cung cấp"),
    "vendorAdmin": MessageLookupByLibrary.simpleMessage("Quản lý người bán"),
    "vendorInfo": MessageLookupByLibrary.simpleMessage("Thông tin người bán"),
    "verificationCode": MessageLookupByLibrary.simpleMessage(
      "Mã xác thực (6 số)",
    ),
    "verifySMSCode": MessageLookupByLibrary.simpleMessage("Xác minh"),
    "version": m62,
    "viaWallet": MessageLookupByLibrary.simpleMessage("Qua ví"),
    "video": MessageLookupByLibrary.simpleMessage("Video"),
    "vietnamese": MessageLookupByLibrary.simpleMessage("Tiếng Việt"),
    "view": MessageLookupByLibrary.simpleMessage("Xem"),
    "viewAll": MessageLookupByLibrary.simpleMessage("Xem tất cả"),
    "viewCart": MessageLookupByLibrary.simpleMessage("Xem giỏ hàng"),
    "viewDetail": MessageLookupByLibrary.simpleMessage("Xem chi tiết"),
    "viewMore": MessageLookupByLibrary.simpleMessage("Xem thêm"),
    "viewOnGoogleMaps": MessageLookupByLibrary.simpleMessage(
      "Xem trên Google Maps",
    ),
    "viewOrder": MessageLookupByLibrary.simpleMessage("Xem đơn hàng"),
    "viewPointHistory": MessageLookupByLibrary.simpleMessage(
      "Lịch sử điểm xem",
    ),
    "viewRecentTransactions": MessageLookupByLibrary.simpleMessage(
      "Xem các giao dịch gần đây",
    ),
    "visible": MessageLookupByLibrary.simpleMessage("Hiển thị"),
    "visitStore": MessageLookupByLibrary.simpleMessage("Xem cửa hàng"),
    "waitForLoad": MessageLookupByLibrary.simpleMessage("Đang chờ tải ảnh"),
    "waitForPost": MessageLookupByLibrary.simpleMessage(
      "Đang chờ đăng sản phẩm",
    ),
    "waiting": MessageLookupByLibrary.simpleMessage("Đang chờ"),
    "waitingForConfirmation": MessageLookupByLibrary.simpleMessage(
      "Đang chờ xác nhận",
    ),
    "walletBalance": MessageLookupByLibrary.simpleMessage("Số dư ví"),
    "walletBalanceWithValue": m63,
    "walletName": MessageLookupByLibrary.simpleMessage("Tên ví"),
    "warning": m64,
    "warningCurrencyMessageForWallet": m65,
    "weFoundBlogs": MessageLookupByLibrary.simpleMessage("Đã tìm thấy blog"),
    "weFoundProducts": m66,
    "weNeedCameraAccessTo": MessageLookupByLibrary.simpleMessage(
      "Chúng tôi cần quyền truy cập máy ảnh để quét mã QR hoặc mã vạch.",
    ),
    "weSentAnOTPTo": MessageLookupByLibrary.simpleMessage(
      "Mã xác thực đã được gửi đến",
    ),
    "weWillSendYouNotification": MessageLookupByLibrary.simpleMessage(
      "Chúng tôi sẽ gửi thông báo cho bạn khi có sản phẩm mới hoặc có ưu đãi. Bạn luôn có thể thay đổi cài đặt này trong cài đặt.",
    ),
    "webView": MessageLookupByLibrary.simpleMessage("Xem web"),
    "website": MessageLookupByLibrary.simpleMessage("Trang mạng"),
    "wednesday": MessageLookupByLibrary.simpleMessage("thứ tư"),
    "week": m67,
    "welcome": MessageLookupByLibrary.simpleMessage("Xin chào"),
    "welcomeBack": MessageLookupByLibrary.simpleMessage("Chào mừng trở lại"),
    "welcomeRegister": MessageLookupByLibrary.simpleMessage(
      "Bắt đầu hành trình mua sắm của bạn với chúng tôi ngay bây giờ",
    ),
    "welcomeUser": m68,
    "whichLanguageDoYouPrefer": MessageLookupByLibrary.simpleMessage(
      "Bạn thích ngôn ngữ nào hơn?",
    ),
    "wholesaleRegisterMsg": MessageLookupByLibrary.simpleMessage(
      "Vui lòng liên hệ với quản trị viên để phê duyệt đăng ký của bạn.",
    ),
    "willNotSendAndReceiveMessage": MessageLookupByLibrary.simpleMessage(
      "Bạn sẽ không thể gửi và nhận tin nhắn từ người dùng này.",
    ),
    "winningBid": MessageLookupByLibrary.simpleMessage("Giá thầu chiến thắng"),
    "withdrawAmount": MessageLookupByLibrary.simpleMessage("Số tiền rút"),
    "withdrawRequest": MessageLookupByLibrary.simpleMessage("Rút lại yêu cầu"),
    "withdrawal": MessageLookupByLibrary.simpleMessage("Rút tiền"),
    "womanCollections": MessageLookupByLibrary.simpleMessage("Bộ sưu tập nữ"),
    "writeComment": MessageLookupByLibrary.simpleMessage(
      "Viết bình luận của bạn",
    ),
    "writeTitle": MessageLookupByLibrary.simpleMessage("Viết tiêu đề của bạn"),
    "writeYourNote": MessageLookupByLibrary.simpleMessage(
      "Viết ghi chú của bạn",
    ),
    "yearsAgo": m69,
    "yes": MessageLookupByLibrary.simpleMessage("Có"),
    "youAreOur": MessageLookupByLibrary.simpleMessage("Bạn là của chúng tôi"),
    "youAreSelecting": m70,
    "youCanOnlyOrderSingleStore": MessageLookupByLibrary.simpleMessage(
      "Bạn chỉ có thể mua từ một cửa hàng.",
    ),
    "youCanOnlyPurchase": MessageLookupByLibrary.simpleMessage(
      "Bạn chỉ có thể mua",
    ),
    "youDontHaveAnyCoupons": MessageLookupByLibrary.simpleMessage(
      "Bạn không có phiếu giảm giá nào.",
    ),
    "youDontHavePermissionToCreatePost": MessageLookupByLibrary.simpleMessage(
      "Bạn không có quyền tạo bài viết",
    ),
    "youHave": MessageLookupByLibrary.simpleMessage("Bạn có"),
    "youHaveAssignedToOrder": m71,
    "youHaveBeenSaveAddressYourLocal": MessageLookupByLibrary.simpleMessage(
      "Bạn đã lưu địa chỉ vào tệp cục bộ của mình thành công!",
    ),
    "youHaveNoPost": MessageLookupByLibrary.simpleMessage(
      "Bạn không có bài viết nào",
    ),
    "youHavePassed": m72,
    "youHavePoints": m73,
    "youMightAlsoLike": MessageLookupByLibrary.simpleMessage(
      "Có thể bạn cũng thích",
    ),
    "youNeedToLoginCheckout": MessageLookupByLibrary.simpleMessage(
      "Bạn cần đăng nhập để thanh toán",
    ),
    "youNotBeAsked": MessageLookupByLibrary.simpleMessage(
      "Bạn sẽ không được hỏi lại sau khi hoàn thành",
    ),
    "yourAccountIsUnderReview": MessageLookupByLibrary.simpleMessage(
      "Tài khoản của bạn đang được xem xét. Vui lòng liên hệ với quản trị viên nếu bạn cần bất kỳ trợ giúp nào.",
    ),
    "yourAddressExistYourLocal": MessageLookupByLibrary.simpleMessage(
      "Địa chỉ của bạn đã tồn tại",
    ),
    "yourAddressHasBeenSaved": MessageLookupByLibrary.simpleMessage(
      "Địa chỉ đã được lưu vào bộ nhớ máy",
    ),
    "yourApplicationIsUnderReview": MessageLookupByLibrary.simpleMessage(
      "Đơn đăng ký của bạn đang được xem xét.",
    ),
    "yourBagIsEmpty": MessageLookupByLibrary.simpleMessage(
      "Giỏ hàng của bạn đang trống",
    ),
    "yourBookingDetail": MessageLookupByLibrary.simpleMessage(
      "Chi tiết đặt chỗ của bạn",
    ),
    "yourEarningsThisMonth": MessageLookupByLibrary.simpleMessage(
      "Thu nhập của bạn trong tháng này",
    ),
    "yourNote": MessageLookupByLibrary.simpleMessage("Ghi chú của bạn"),
    "yourOrderHasBeenAdded": MessageLookupByLibrary.simpleMessage(
      "Đơn hàng của bạn đã được thêm",
    ),
    "yourOrderIsConfirmed": MessageLookupByLibrary.simpleMessage(
      "Đơn hàng của bạn đã được xác nhận!",
    ),
    "yourOrderIsEmpty": MessageLookupByLibrary.simpleMessage(
      "Đơn hàng của bạn trống",
    ),
    "yourOrderIsEmptyMsg": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa có bất kỳ đơn hàng nào trong lịch sử mua sắm của mình.\nHãy bắt đầu mua sắm ngay",
    ),
    "yourOrders": MessageLookupByLibrary.simpleMessage("Đơn hàng của bạn"),
    "yourProductIsUnderReview": MessageLookupByLibrary.simpleMessage(
      "Sản phẩm của bạn đang được xem xét",
    ),
    "yourUsernameEmail": MessageLookupByLibrary.simpleMessage(
      "Tên đăng nhập hoặc email của bạn",
    ),
    "zipCode": MessageLookupByLibrary.simpleMessage("Mã bưu điện"),
    "zipCodeIsRequired": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập mã bưu điện",
    ),
  };
}
