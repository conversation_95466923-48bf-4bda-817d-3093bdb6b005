name: openai
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.0.2+1

environment:
  sdk: ">=3.0.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter

  inspireui:
    path:  ../../_dev/common_library

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  provider: ^6.0.5
  jumping_dot: 0.0.7
  speech_to_text: 7.0.0
#  supabase_flutter: ^1.6.2
  flutter_tts: 4.2.2
  extended_image: 10.0.1
  flutter_image_gallery_saver: ^0.0.2
  async_button_builder: ^3.0.0+1
  http: ^1.1.0
  share_plus: 10.1.4
  path_provider: ^2.1.0
  carousel_slider_plus: 7.1.0
  flutter_svg: ^2.0.13
  intl: any
  animated_text_kit: 4.2.3
  flutter_linkify: ^6.0.0
  url_launcher: 6.3.1
  smooth_page_indicator: ^1.1.0
#  in_app_purchase: ^3.1.5
  purchases_flutter: ^8.4.6
  # supabase_auth_ui: ^0.2.1
  # flutter_adaptive_scaffold: ^0.1.3

  # Core
  retrofit: 4.4.2
  equatable: ^2.0.7
  collection: any
  bloc: ^8.1.4
  flutter_bloc: ^8.1.6
  get_it: ^8.0.2
  universal_platform: ^1.1.0

  # Local database
  shared_preferences: ^2.3.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Network client
  dio: ^5.7.0
  json_annotation: ^4.9.0
  freezed_annotation: 2.4.4

  # Plugins
  injectable: ^2.5.0

  # UI
  adaptive_theme: ^3.2.0
  cupertino_icons: ^1.0.5
  google_fonts: ^6.1.0
  dropdown_button2: ^2.1.0
  visibility_detector: ^0.4.0+2
  rxdart: ^0.28.0

  # Fix issue to compatible with Flutter 3.32
  multiple_localization:
    git:
      url: https://github.com/inspireui/flutter_multiple_localization.git
      ref: 48b65fa88ed468331d56ee86a102d808596c0a50
dev_dependencies:
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.3.3
  freezed: ^2.5.0
  json_serializable: ^6.6.1
  hive_generator: ^2.0.0
  injectable_generator: 2.6.1
  retrofit_generator: ^8.1.0
#  isar_generator: ^3.1.0


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

flutter_intl:
  enabled: true
  use_deferred_loading: true