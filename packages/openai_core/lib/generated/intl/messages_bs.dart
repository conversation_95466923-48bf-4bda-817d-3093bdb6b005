// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a bs locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'bs';

  static String m0(limit) =>
      "U besplatnoj verziji postoji samo ${limit}x pretraga slika.";

  static String m1(limit) =>
      "Do ${limit} poruka se može prikazati samo u besplatnoj verziji.";

  static String m2(date) => "Datum isteka pretplate ${date}";

  static String m3(number) =>
      "Generiraj (${number} ${Intl.plural(number, one: 'time', other: 'times')})";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about_openai": MessageLookupByLibrary.simpleMessage("O nama"),
    "apply_openai": MessageLookupByLibrary.simpleMessage("Prijavite se"),
    "artist_openai": MessageLookupByLibrary.simpleMessage("Umjetnik"),
    "cancel_openai": MessageLookupByLibrary.simpleMessage("Odustani"),
    "chatDetail_openai": MessageLookupByLibrary.simpleMessage("Chat Detail"),
    "chatGPT_openai": MessageLookupByLibrary.simpleMessage("Chat GPT"),
    "chatWithBot_openai": MessageLookupByLibrary.simpleMessage(
      "Razgovarajte sa Botom",
    ),
    "chat_openai": MessageLookupByLibrary.simpleMessage("Chat"),
    "chooseArtist_openai": MessageLookupByLibrary.simpleMessage(
      "Odaberite izvođača za svoju sliku",
    ),
    "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
      "Odaberite detalje za svoju sliku",
    ),
    "chooseMedium_openai": MessageLookupByLibrary.simpleMessage(
      "Odaberite medij za svoju sliku",
    ),
    "chooseMood_openai": MessageLookupByLibrary.simpleMessage(
      "Odaberite raspoloženje za svoju sliku",
    ),
    "chooseUseCase_openai": MessageLookupByLibrary.simpleMessage(
      "Odaberite slučaj upotrebe",
    ),
    "choseStyle_openai": MessageLookupByLibrary.simpleMessage(
      "Odaberite stil za svoju sliku",
    ),
    "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
      "Jeste li sigurni da obrišete sadržaj?",
    ),
    "clearContent_openai": MessageLookupByLibrary.simpleMessage(
      "Jasan sadržaj",
    ),
    "clearConversation_openai": MessageLookupByLibrary.simpleMessage(
      "Očisti razgovor",
    ),
    "clear_openai": MessageLookupByLibrary.simpleMessage("Jasno"),
    "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
      "Jeste li sigurni da želite izbrisati ovu stavku?",
    ),
    "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
      "Molimo potvrdite da li želite nastaviti sa brisanjem ove stavke. Ne možete poništiti ovu radnju.",
    ),
    "confirmRemoveKey_openai": MessageLookupByLibrary.simpleMessage(
      "Jeste li sigurni da ćete ukloniti ključ?",
    ),
    "confirm_openai": MessageLookupByLibrary.simpleMessage("Potvrdi"),
    "copiedToClipboard_openai": MessageLookupByLibrary.simpleMessage(
      "Sadržaj je kopiran u međuspremnik",
    ),
    "copy_openai": MessageLookupByLibrary.simpleMessage("copy"),
    "createChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Kreiranje ćaskanja nije uspjelo",
    ),
    "deleteFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Brisanje nije uspjelo",
    ),
    "delete_openai": MessageLookupByLibrary.simpleMessage("Izbriši"),
    "detail_openai": MessageLookupByLibrary.simpleMessage("Detalj"),
    "download_openai": MessageLookupByLibrary.simpleMessage("Skinuti"),
    "edit_openai": MessageLookupByLibrary.simpleMessage("Uredi"),
    "failedToGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Generiranje nije uspjelo",
    ),
    "generate_openai": MessageLookupByLibrary.simpleMessage("Generiraj"),
    "grid_openai": MessageLookupByLibrary.simpleMessage("Grid"),
    "imageGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Generiranje slike",
    ),
    "imageSize_openai": MessageLookupByLibrary.simpleMessage("Veličina slike"),
    "inputKey_openai": MessageLookupByLibrary.simpleMessage("Taster za unos"),
    "interest_openai": MessageLookupByLibrary.simpleMessage("Interes"),
    "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
      "Vaš API ključ se pohranjuje lokalno na vašem mobilnom telefonu i nikada se ne šalje nigdje drugdje. Možete sačuvati svoj ključ da ga kasnije koristite. Također možete ukloniti svoj ključ ako ga više ne želite koristiti.",
    ),
    "invalidKey_openai": MessageLookupByLibrary.simpleMessage("Nevažeći ključ"),
    "jobRole_openai": MessageLookupByLibrary.simpleMessage("Poslovna uloga"),
    "jobSkills_openai": MessageLookupByLibrary.simpleMessage("Veštine posla"),
    "layoutStyle_openai": MessageLookupByLibrary.simpleMessage("Layout Style"),
    "limitImage_openai": m0,
    "limitTheText_openai": m1,
    "listening_openai": MessageLookupByLibrary.simpleMessage("slušam..."),
    "loadKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Učitavanje ključa nije uspjelo",
    ),
    "loadKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "Učitavanje ključa uspješno",
    ),
    "manage_openai": MessageLookupByLibrary.simpleMessage("Upravljajte"),
    "medium_openai": MessageLookupByLibrary.simpleMessage("Srednje"),
    "mood_openai": MessageLookupByLibrary.simpleMessage("Raspoloženje"),
    "moreOptions_openai": MessageLookupByLibrary.simpleMessage("Više opcija"),
    "newChat_openai": MessageLookupByLibrary.simpleMessage("Novo ćaskanje"),
    "noImageGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Nema generiranja slike",
    ),
    "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
      "Broj slika za generiranje. Mora biti između 1 i 10.",
    ),
    "numberOfImages_openai": MessageLookupByLibrary.simpleMessage("Broj slika"),
    "options_openai": MessageLookupByLibrary.simpleMessage("Opcije"),
    "page_openai": MessageLookupByLibrary.simpleMessage("Page"),
    "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
      "Provjerite svoju vezu i pokušajte ponovo!",
    ),
    "pleaseInputFillAllFields_openai": MessageLookupByLibrary.simpleMessage(
      "Molimo popunite sva polja",
    ),
    "pleaseInputKey_openai": MessageLookupByLibrary.simpleMessage(
      "Unesite ključ",
    ),
    "prompt_openai": MessageLookupByLibrary.simpleMessage("Prompt"),
    "putKeyHere_openai": MessageLookupByLibrary.simpleMessage(
      "Stavi svoj ključ ovdje",
    ),
    "regenerateResponse_openai": MessageLookupByLibrary.simpleMessage(
      "Regeneracija odgovora",
    ),
    "remaining_openai": MessageLookupByLibrary.simpleMessage("preostali"),
    "removeKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Uklanjanje ključa nije uspjelo",
    ),
    "removeKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "Ključ je uspješno uklonjen",
    ),
    "remove_openai": MessageLookupByLibrary.simpleMessage("Ukloni"),
    "resetSettings_openai": MessageLookupByLibrary.simpleMessage(
      "Poništi postavke",
    ),
    "reset_openai": MessageLookupByLibrary.simpleMessage("Resetovati"),
    "saveKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Čuvanje ključa nije uspjelo",
    ),
    "saveKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "Ključ je uspješno sačuvan",
    ),
    "saveKey_openai": MessageLookupByLibrary.simpleMessage("Sačuvaj ključ"),
    "save_openai": MessageLookupByLibrary.simpleMessage("Spremi"),
    "searchByPrompt_openai": MessageLookupByLibrary.simpleMessage(
      "Traži po upitu...",
    ),
    "sectionKeywords_openai": MessageLookupByLibrary.simpleMessage(
      "Sekcija Ključne riječi",
    ),
    "sectionTopic_openai": MessageLookupByLibrary.simpleMessage(
      "Tema odjeljka",
    ),
    "selectChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Odaberite Chat nije uspio",
    ),
    "selectPrompt_openai": MessageLookupByLibrary.simpleMessage(
      "Odaberite Prompt",
    ),
    "settings_openai": MessageLookupByLibrary.simpleMessage("Podešavanja"),
    "share_openai": MessageLookupByLibrary.simpleMessage("Podijeli"),
    "skills_openai": MessageLookupByLibrary.simpleMessage("Vještine"),
    "somethingWentWrong_openai": MessageLookupByLibrary.simpleMessage(
      "Nešto je pošlo po zlu!!!",
    ),
    "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
      "Nešto je pošlo po zlu! Molimo pokušajte ponovo kasnije. Hvala ti puno!",
    ),
    "speechNotAvailable_openai": MessageLookupByLibrary.simpleMessage(
      "Govor nije dostupan",
    ),
    "style_openai": MessageLookupByLibrary.simpleMessage("Stil"),
    "subscriptionExpiredDate_openai": m2,
    "tapTheMicToTalk_openai": MessageLookupByLibrary.simpleMessage(
      "Dodirnite mikrofon za razgovor",
    ),
    "textGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Generiranje teksta",
    ),
    "textGenerator_openai": MessageLookupByLibrary.simpleMessage(
      "Text Generator",
    ),
    "timeGenerate_openai": m3,
    "typeAMessage_openai": MessageLookupByLibrary.simpleMessage(
      "Upišite poruku...",
    ),
    "viewType_openai": MessageLookupByLibrary.simpleMessage("Vrsta pogleda"),
    "view_openai": MessageLookupByLibrary.simpleMessage("Pogled"),
    "write_openai": MessageLookupByLibrary.simpleMessage("Pisati"),
  };
}
