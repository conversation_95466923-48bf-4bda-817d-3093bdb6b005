// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ro locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ro';

  static String m0(limit) =>
      "Există doar ${limit}x căutare de imagini în versiunea gratuită.";

  static String m1(limit) =>
      "Până la ${limit} mesaje pot fi afișate numai în versiunea gratuită.";

  static String m2(date) => "Data expirării abonamentului ${date}";

  static String m3(number) =>
      "Generați (${number} ${Intl.plural(number, one: 'time', other: 'times')})";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about_openai": MessageLookupByLibrary.simpleMessage("Despre"),
        "apply_openai": MessageLookupByLibrary.simpleMessage("aplica"),
        "artist_openai": MessageLookupByLibrary.simpleMessage("Artist"),
        "cancel_openai": MessageLookupByLibrary.simpleMessage("Anulare"),
        "chatDetail_openai":
            MessageLookupByLibrary.simpleMessage("Detaliu chat"),
        "chatGPT_openai": MessageLookupByLibrary.simpleMessage("Chat GPT"),
        "chatWithBot_openai":
            MessageLookupByLibrary.simpleMessage("Chat cu Bot"),
        "chat_openai": MessageLookupByLibrary.simpleMessage("conversație"),
        "chooseArtist_openai": MessageLookupByLibrary.simpleMessage(
            "Alege un artist pentru imaginea ta"),
        "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
            "Alegeți detaliile pentru imaginea dvs"),
        "chooseMedium_openai": MessageLookupByLibrary.simpleMessage(
            "Alegeți mediul pentru imaginea dvs"),
        "chooseMood_openai": MessageLookupByLibrary.simpleMessage(
            "Alegeți starea de spirit pentru imaginea dvs"),
        "chooseUseCase_openai":
            MessageLookupByLibrary.simpleMessage("Alegeți cazul de utilizare"),
        "choseStyle_openai": MessageLookupByLibrary.simpleMessage(
            "Alegeți stilul pentru imaginea dvs"),
        "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
            "Ești sigur că ștergi conținutul?"),
        "clearContent_openai":
            MessageLookupByLibrary.simpleMessage("Conținut clar"),
        "clearConversation_openai":
            MessageLookupByLibrary.simpleMessage("Conversație clară"),
        "clear_openai": MessageLookupByLibrary.simpleMessage("clar"),
        "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
            "Sigur doriți să ștergeți acest articol?"),
        "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
            "Vă rugăm să confirmați dacă doriți să continuați cu ștergerea acestui articol. Nu puteți anula această acțiune."),
        "confirmRemoveKey_openai": MessageLookupByLibrary.simpleMessage(
            "Sunteți sigur că eliminați cheia?"),
        "confirm_openai": MessageLookupByLibrary.simpleMessage("A confirma"),
        "copiedToClipboard_openai": MessageLookupByLibrary.simpleMessage(
            "Conținut copiat în clipboard"),
        "copy_openai": MessageLookupByLibrary.simpleMessage("Copie"),
        "createChatFailed_openai":
            MessageLookupByLibrary.simpleMessage("Crearea chat a eșuat"),
        "deleteFailed_openai":
            MessageLookupByLibrary.simpleMessage("Ștergerea a eșuat"),
        "delete_openai": MessageLookupByLibrary.simpleMessage("Șterge"),
        "detail_openai": MessageLookupByLibrary.simpleMessage("Detaliu"),
        "download_openai": MessageLookupByLibrary.simpleMessage("Descarca"),
        "edit_openai": MessageLookupByLibrary.simpleMessage("Editați | ×"),
        "failedToGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Nu s-a putut genera"),
        "generate_openai": MessageLookupByLibrary.simpleMessage("genera"),
        "grid_openai": MessageLookupByLibrary.simpleMessage("Grilă"),
        "imageGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Generarea imaginii"),
        "imageSize_openai":
            MessageLookupByLibrary.simpleMessage("Marimea imaginii"),
        "inputKey_openai":
            MessageLookupByLibrary.simpleMessage("Tasta de intrare"),
        "interest_openai": MessageLookupByLibrary.simpleMessage("interes"),
        "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
            "Cheia dvs. API este stocată local pe mobil și nu este trimisă niciodată altundeva. Puteți salva cheia pentru a o utiliza mai târziu. De asemenea, vă puteți elimina cheia dacă nu doriți să o mai folosiți."),
        "invalidKey_openai":
            MessageLookupByLibrary.simpleMessage("Cheie invalida"),
        "jobRole_openai":
            MessageLookupByLibrary.simpleMessage("Rolul postului"),
        "jobSkills_openai":
            MessageLookupByLibrary.simpleMessage("Abilități de muncă"),
        "layoutStyle_openai":
            MessageLookupByLibrary.simpleMessage("asezare in pagina"),
        "limitImage_openai": m0,
        "limitTheText_openai": m1,
        "listening_openai":
            MessageLookupByLibrary.simpleMessage("Ascultare..."),
        "loadKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("Încărcarea cheii a eșuat"),
        "loadKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("Încărcare cheie de succes"),
        "manage_openai": MessageLookupByLibrary.simpleMessage("Administra"),
        "medium_openai": MessageLookupByLibrary.simpleMessage("Mediu"),
        "mood_openai": MessageLookupByLibrary.simpleMessage("Dispoziţie"),
        "moreOptions_openai":
            MessageLookupByLibrary.simpleMessage("Mai multe opțiuni"),
        "newChat_openai": MessageLookupByLibrary.simpleMessage("Chat nou"),
        "noImageGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Nicio imagine generată"),
        "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
            "Numărul de imagini de generat. Trebuie să fie între 1 și 10."),
        "numberOfImages_openai":
            MessageLookupByLibrary.simpleMessage("Numărul de imagini"),
        "options_openai": MessageLookupByLibrary.simpleMessage("Opțiuni"),
        "page_openai": MessageLookupByLibrary.simpleMessage("pagină"),
        "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
            "Vă rugăm să vă verificați conexiunea și să încercați din nou!"),
        "pleaseInputFillAllFields_openai": MessageLookupByLibrary.simpleMessage(
            "Vă rugăm să completați toate câmpurile"),
        "pleaseInputKey_openai": MessageLookupByLibrary.simpleMessage(
            "Vă rugăm să introduceți tasta"),
        "prompt_openai": MessageLookupByLibrary.simpleMessage("Prompt"),
        "putKeyHere_openai":
            MessageLookupByLibrary.simpleMessage("Pune-ți cheia aici"),
        "regenerateResponse_openai":
            MessageLookupByLibrary.simpleMessage("Regenerați răspunsul"),
        "remaining_openai": MessageLookupByLibrary.simpleMessage("Rămas"),
        "removeKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("Eliminare cheie a eșuat"),
        "removeKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("Cheia eliminată cu succes"),
        "remove_openai": MessageLookupByLibrary.simpleMessage("Elimina"),
        "resetSettings_openai":
            MessageLookupByLibrary.simpleMessage("Reseteaza setarile"),
        "reset_openai": MessageLookupByLibrary.simpleMessage("Restabili"),
        "saveKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("Salvare cheie a eșuat"),
        "saveKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("Cheie salvată cu succes"),
        "saveKey_openai": MessageLookupByLibrary.simpleMessage("Salvare cheie"),
        "save_openai": MessageLookupByLibrary.simpleMessage("Salvați"),
        "searchByPrompt_openai":
            MessageLookupByLibrary.simpleMessage("Căutați după prompt..."),
        "sectionKeywords_openai":
            MessageLookupByLibrary.simpleMessage("Secțiunea Cuvinte cheie"),
        "sectionTopic_openai":
            MessageLookupByLibrary.simpleMessage("Sectiunea Subiect"),
        "selectChatFailed_openai":
            MessageLookupByLibrary.simpleMessage("Selectați Chat eșuat"),
        "selectPrompt_openai":
            MessageLookupByLibrary.simpleMessage("Selectați Prompt"),
        "settings_openai": MessageLookupByLibrary.simpleMessage("Setări"),
        "share_openai": MessageLookupByLibrary.simpleMessage("Acțiune"),
        "skills_openai": MessageLookupByLibrary.simpleMessage("Aptitudini"),
        "somethingWentWrong_openai":
            MessageLookupByLibrary.simpleMessage("Ceva n-a mers bine!!!"),
        "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
            "Ceva n-a mers bine! Vă rugăm să încercați din nou mai târziu. Mulțumesc foarte mult!"),
        "speechNotAvailable_openai": MessageLookupByLibrary.simpleMessage(
            "Discursul nu este disponibil"),
        "style_openai": MessageLookupByLibrary.simpleMessage("stil"),
        "subscriptionExpiredDate_openai": m2,
        "tapTheMicToTalk_openai": MessageLookupByLibrary.simpleMessage(
            "Atingeți microfonul pentru a vorbi"),
        "textGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Generare text"),
        "textGenerator_openai":
            MessageLookupByLibrary.simpleMessage("Generator de text"),
        "timeGenerate_openai": m3,
        "typeAMessage_openai":
            MessageLookupByLibrary.simpleMessage("Scrie un mesaj..."),
        "viewType_openai":
            MessageLookupByLibrary.simpleMessage("Tipul de vizualizare"),
        "view_openai": MessageLookupByLibrary.simpleMessage("Vedere"),
        "write_openai": MessageLookupByLibrary.simpleMessage("scrie")
      };
}
