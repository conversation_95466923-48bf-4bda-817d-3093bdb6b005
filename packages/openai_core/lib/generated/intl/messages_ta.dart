// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ta locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ta';

  static String m0(limit) =>
      "இலவச பதிப்பில் ${limit}x படத் தேடல் மட்டுமே உள்ளது.";

  static String m1(limit) =>
      "இலவச பதிப்பில் ${limit} செய்திகள் வரை மட்டுமே காட்டப்படும்.";

  static String m2(date) => "சந்தா காலாவதியான தேதி ${date}";

  static String m3(number) =>
      "உருவாக்கு (${number} ${Intl.plural(number, one: 'time', other: 'times')})";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about_openai": MessageLookupByLibrary.simpleMessage("பற்றி"),
        "apply_openai":
            MessageLookupByLibrary.simpleMessage("விண்ணப்பிக்கவும்"),
        "artist_openai": MessageLookupByLibrary.simpleMessage("கலைஞர்"),
        "cancel_openai": MessageLookupByLibrary.simpleMessage("ரத்து"),
        "chatDetail_openai":
            MessageLookupByLibrary.simpleMessage("அரட்டை விவரம்"),
        "chatGPT_openai": MessageLookupByLibrary.simpleMessage("அரட்டை GPT"),
        "chatWithBot_openai":
            MessageLookupByLibrary.simpleMessage("Bot உடன் அரட்டையடிக்கவும்"),
        "chat_openai": MessageLookupByLibrary.simpleMessage("அரட்டை"),
        "chooseArtist_openai": MessageLookupByLibrary.simpleMessage(
            "உங்கள் படத்திற்கான கலைஞரைத் தேர்ந்தெடுக்கவும்"),
        "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
            "உங்கள் படத்திற்கான விவரங்களைத் தேர்ந்தெடுக்கவும்"),
        "chooseMedium_openai": MessageLookupByLibrary.simpleMessage(
            "உங்கள் படத்திற்கான ஊடகத்தைத் தேர்ந்தெடுக்கவும்"),
        "chooseMood_openai": MessageLookupByLibrary.simpleMessage(
            "உங்கள் படத்திற்கான மனநிலையைத் தேர்ந்தெடுக்கவும்"),
        "chooseUseCase_openai": MessageLookupByLibrary.simpleMessage(
            "பயன்பாட்டு வழக்கைத் தேர்ந்தெடுக்கவும்"),
        "choseStyle_openai": MessageLookupByLibrary.simpleMessage(
            "உங்கள் படத்திற்கான பாணியைத் தேர்வுசெய்க"),
        "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
            "உள்ளடக்கத்தை நிச்சயமாக அழிப்பீர்களா?"),
        "clearContent_openai":
            MessageLookupByLibrary.simpleMessage("உள்ளடக்கத்தை அழிக்கவும்"),
        "clearConversation_openai":
            MessageLookupByLibrary.simpleMessage("தெளிவான உரையாடல்"),
        "clear_openai": MessageLookupByLibrary.simpleMessage("அழி"),
        "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
            "இந்த உருப்படியை நிச்சயமாக நீக்க விரும்புகிறீர்களா?"),
        "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
            "இந்த உருப்படியை நீக்குவதைத் தொடர விரும்புகிறீர்களா என்பதை உறுதிப்படுத்தவும். இந்தச் செயலைச் செயல்தவிர்க்க முடியாது."),
        "confirmRemoveKey_openai":
            MessageLookupByLibrary.simpleMessage("விசையை அகற்றுவது உறுதியா?"),
        "confirm_openai":
            MessageLookupByLibrary.simpleMessage("உறுதிப்படுத்தவும்"),
        "copiedToClipboard_openai": MessageLookupByLibrary.simpleMessage(
            "கிளிப்போர்டுக்கு உள்ளடக்கம் நகலெடுக்கப்பட்டது"),
        "copy_openai": MessageLookupByLibrary.simpleMessage("நகலெடுக்கவும்"),
        "createChatFailed_openai": MessageLookupByLibrary.simpleMessage(
            "அரட்டையை உருவாக்க முடியவில்லை"),
        "deleteFailed_openai":
            MessageLookupByLibrary.simpleMessage("நீக்குவது தோல்வியடைந்தது"),
        "delete_openai": MessageLookupByLibrary.simpleMessage("அழி"),
        "detail_openai": MessageLookupByLibrary.simpleMessage("விவரம்"),
        "download_openai": MessageLookupByLibrary.simpleMessage("பதிவிறக்க"),
        "edit_openai": MessageLookupByLibrary.simpleMessage("தொகு"),
        "failedToGenerate_openai":
            MessageLookupByLibrary.simpleMessage("உருவாக்க முடியவில்லை"),
        "generate_openai": MessageLookupByLibrary.simpleMessage("உருவாக்கு"),
        "grid_openai": MessageLookupByLibrary.simpleMessage("கட்டம்"),
        "imageGenerate_openai":
            MessageLookupByLibrary.simpleMessage("படத்தை உருவாக்குகிறது"),
        "imageSize_openai":
            MessageLookupByLibrary.simpleMessage("படத்தின் அளவு"),
        "inputKey_openai":
            MessageLookupByLibrary.simpleMessage("உள்ளீட்டு விசை"),
        "interest_openai": MessageLookupByLibrary.simpleMessage("ஆர்வம்"),
        "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
            "உங்கள் API விசை உங்கள் மொபைலில் உள்ளூரில் சேமிக்கப்பட்டு வேறு எங்கும் அனுப்பப்படவில்லை. உங்கள் விசையை பின்னர் பயன்படுத்த அதைச் சேமிக்கலாம். உங்கள் சாவியை இனி பயன்படுத்த விரும்பவில்லை என்றால் அதையும் அகற்றலாம்."),
        "invalidKey_openai": MessageLookupByLibrary.simpleMessage("தவறான விசை"),
        "jobRole_openai": MessageLookupByLibrary.simpleMessage("வேலை பங்கு"),
        "jobSkills_openai":
            MessageLookupByLibrary.simpleMessage("வேலை திறன்கள்"),
        "layoutStyle_openai":
            MessageLookupByLibrary.simpleMessage("தளவமைப்பு உடை"),
        "limitImage_openai": m0,
        "limitTheText_openai": m1,
        "listening_openai":
            MessageLookupByLibrary.simpleMessage("கேட்கிறது..."),
        "loadKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("ஏற்ற விசை தோல்வியடைந்தது"),
        "loadKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("ஏற்றுதல் வெற்றி"),
        "manage_openai": MessageLookupByLibrary.simpleMessage("நிர்வகிக்கவும்"),
        "medium_openai": MessageLookupByLibrary.simpleMessage("நடுத்தர"),
        "mood_openai": MessageLookupByLibrary.simpleMessage("மனநிலை"),
        "moreOptions_openai":
            MessageLookupByLibrary.simpleMessage("மேலும் விருப்பங்கள்"),
        "newChat_openai": MessageLookupByLibrary.simpleMessage("புதிய அரட்டை"),
        "noImageGenerate_openai":
            MessageLookupByLibrary.simpleMessage("படத்தை உருவாக்கவில்லை"),
        "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
            "உருவாக்க வேண்டிய படங்களின் எண்ணிக்கை. 1 முதல் 10 வரை இருக்க வேண்டும்."),
        "numberOfImages_openai":
            MessageLookupByLibrary.simpleMessage("படங்களின் எண்ணிக்கை"),
        "options_openai": MessageLookupByLibrary.simpleMessage("விருப்பங்கள்"),
        "page_openai": MessageLookupByLibrary.simpleMessage("பக்கம்"),
        "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
            "உங்கள் இணைப்பைச் சரிபார்த்து, மீண்டும் முயற்சிக்கவும்!"),
        "pleaseInputFillAllFields_openai": MessageLookupByLibrary.simpleMessage(
            "அனைத்து புலங்களையும் நிரப்பவும்"),
        "pleaseInputKey_openai":
            MessageLookupByLibrary.simpleMessage("தயவுசெய்து உள்ளிடவும்"),
        "prompt_openai": MessageLookupByLibrary.simpleMessage("உடனடி"),
        "putKeyHere_openai": MessageLookupByLibrary.simpleMessage(
            "உங்கள் சாவியை இங்கே வைக்கவும்"),
        "regenerateResponse_openai":
            MessageLookupByLibrary.simpleMessage("பதிலை மீண்டும் உருவாக்கவும்"),
        "remaining_openai": MessageLookupByLibrary.simpleMessage("மீதமுள்ள"),
        "removeKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("விசையை அகற்ற முடியவில்லை"),
        "removeKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
            "விசை வெற்றிகரமாக அகற்றப்பட்டது"),
        "remove_openai": MessageLookupByLibrary.simpleMessage("அகற்று"),
        "resetSettings_openai":
            MessageLookupByLibrary.simpleMessage("அமைப்புகளை மீட்டமைக்கவும்"),
        "reset_openai": MessageLookupByLibrary.simpleMessage("மீட்டமை"),
        "saveKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
            "விசையைச் சேமிக்க முடியவில்லை"),
        "saveKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
            "விசை வெற்றிகரமாக சேமிக்கப்பட்டது"),
        "saveKey_openai":
            MessageLookupByLibrary.simpleMessage("விசையைச் சேமிக்கவும்"),
        "save_openai": MessageLookupByLibrary.simpleMessage("சேமி"),
        "searchByPrompt_openai":
            MessageLookupByLibrary.simpleMessage("உடனடியாக தேடுங்கள்..."),
        "sectionKeywords_openai":
            MessageLookupByLibrary.simpleMessage("பிரிவு முக்கிய வார்த்தைகள்"),
        "sectionTopic_openai":
            MessageLookupByLibrary.simpleMessage("பிரிவு தலைப்பு"),
        "selectChatFailed_openai": MessageLookupByLibrary.simpleMessage(
            "அரட்டை தோல்வி என்பதைத் தேர்ந்தெடுக்கவும்"),
        "selectPrompt_openai": MessageLookupByLibrary.simpleMessage(
            "கட்டளையைத் தேர்ந்தெடுக்கவும்"),
        "settings_openai": MessageLookupByLibrary.simpleMessage("அமைப்புகள்"),
        "share_openai": MessageLookupByLibrary.simpleMessage("பகிர்"),
        "skills_openai": MessageLookupByLibrary.simpleMessage("திறன்கள்"),
        "somethingWentWrong_openai":
            MessageLookupByLibrary.simpleMessage("ஏதோ தவறு நடந்துவிட்டது!!!"),
        "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
            "ஏதோ தவறு நடந்துவிட்டது! பிறகு முயற்சிக்கவும். மிக்க நன்றி!"),
        "speechNotAvailable_openai":
            MessageLookupByLibrary.simpleMessage("பேச்சு கிடைக்கவில்லை"),
        "style_openai": MessageLookupByLibrary.simpleMessage("உடை"),
        "subscriptionExpiredDate_openai": m2,
        "tapTheMicToTalk_openai":
            MessageLookupByLibrary.simpleMessage("பேச மைக்கைத் தட்டவும்"),
        "textGenerate_openai":
            MessageLookupByLibrary.simpleMessage("உரை உருவாக்கம்"),
        "textGenerator_openai":
            MessageLookupByLibrary.simpleMessage("உரை ஜெனரேட்டர்"),
        "timeGenerate_openai": m3,
        "typeAMessage_openai":
            MessageLookupByLibrary.simpleMessage("செய்தியை உள்ளிடவும்..."),
        "viewType_openai": MessageLookupByLibrary.simpleMessage("பார்வை வகை"),
        "view_openai": MessageLookupByLibrary.simpleMessage("காண்க"),
        "write_openai": MessageLookupByLibrary.simpleMessage("எழுது")
      };
}
