import 'dart:convert' as convert;

import 'package:fstore/models/entities/product.dart';
import 'package:fstore/models/entities/product_auction.dart';
import 'package:fstore/services/services.dart';
import 'package:inspireui/inspireui.dart';
import 'package:quiver/strings.dart';

class AuctionServices {
  final domain = Services().api.domain;

  Future placeBid(
      {required Product product,
      required double bidValue,
      String? token}) async {
    try {
      var params = {
        'product_id': product.id,
        'bid_value': bidValue,
      };
      var response = await httpPost(
          Uri.parse('$domain/wp-json/api/flutter_auction/bid'),
          body: convert.jsonEncode(params),
          headers: {
            'User-Cookie': token ?? '',
          });
      var jsonDecode = convert.jsonDecode(response.body);
      if (jsonDecode is Map && isNotBlank(jsonDecode['message'])) {
        throw jsonDecode['message'];
      }
    } catch (e) {
      //This error exception is about your Rest API is not config correctly so that not return the correct JSON format, please double check the document from this link https://support.inspireui.com/help-center/
      rethrow;
    }
  }

  Future<List<ProductAuctionHistory>> getAuctionHistories(
      {required Product product, String? token}) async {
    try {
      var list = <ProductAuctionHistory>[];
      var response = await httpGet(
          Uri.parse(
              '$domain/wp-json/api/flutter_auction/history/${product.id}'),
          headers: {
            'User-Cookie': token ?? '',
          });
      var jsonDecode = convert.jsonDecode(response.body);
      if (jsonDecode is Map && isNotBlank(jsonDecode['message'])) {
        throw jsonDecode['message'];
      } else if (jsonDecode is List) {
        for (var item in jsonDecode) {
          list.add(ProductAuctionHistory.fromJson(item));
        }
      }
      return list;
    } catch (e) {
      //This error exception is about your Rest API is not config correctly so that not return the correct JSON format, please double check the document from this link https://support.inspireui.com/help-center/
      rethrow;
    }
  }
}
