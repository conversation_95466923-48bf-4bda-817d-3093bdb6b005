import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flux_localization/flux_localization.dart';
import 'package:fstore/models/app_model.dart';
import 'package:inspireui/utils/colors.dart';
import 'package:provider/provider.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../base/base_user_info.dart';
import '../constants.dart';
import '../widgets/user_info_widget.dart';
import '../widgets/user_points_widget.dart';

class MyQRCodeScreen extends BaseUserInfo {
  const MyQRCodeScreen({super.key, this.points});
  final int? points;
  @override
  BaseUserInfoState<MyQRCodeScreen> createState() => _MyQRCodeScreenState();
}

class _MyQRCodeScreenState extends BaseUserInfoState<MyQRCodeScreen> {
  @override
  Widget build(BuildContext context) {
    var isDarkTheme = Provider.of<AppModel>(context, listen: false).darkTheme;

    return Scaffold(
        appBar: AppBar(
          backgroundColor: Theme.of(context).colorScheme.surface,
          elevation: 0.0,
          title: Text(
            S.of(context).myQRCode,
            style: Theme.of(context)
                .textTheme
                .titleLarge
                ?.copyWith(fontWeight: FontWeight.w400),
          ),
        ),
        body: SafeArea(
            child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
                height: 8,
                color: !isDarkTheme
                    ? HexColor(greyColor)
                    : Theme.of(context).dividerColor),
            Container(
              color: Theme.of(context).colorScheme.surface,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  UserInfoWidget(loyaltyUser: loyaltyUser),
                  const Divider(),
                  const SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: RichText(
                      text: TextSpan(
                        text: '${S.of(context).myQRCodeNote} ',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w400, fontSize: 14.0),
                        children: <TextSpan>[
                          TextSpan(
                              text: S.of(context).add.toLowerCase(),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold)),
                          TextSpan(text: ' ${S.of(context).or.toLowerCase()} '),
                          TextSpan(
                              text: S.of(context).redeem.toLowerCase(),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold)),
                          TextSpan(
                              text: ' ${S.of(context).points.toLowerCase()}.'),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 15),
                  Align(
                    alignment: Alignment.center,
                    child: QrImageView(
                      data: json.encode(loyaltyUser?.toMap() ?? {}),
                      version: QrVersions.auto,
                      size: 200.0,
                      eyeStyle: QrEyeStyle(
                        eyeShape: QrEyeShape.square,
                        color: isDarkTheme ? Colors.white : Colors.black,
                      ),
                      dataModuleStyle: QrDataModuleStyle(
                        dataModuleShape: QrDataModuleShape.square,
                        color: isDarkTheme ? Colors.white : Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(height: 15),
                ],
              ),
            ),
            Container(
                height: 8,
                color: !isDarkTheme
                    ? HexColor(greyColor)
                    : Theme.of(context).dividerColor),
            UserPointsWidget(loyaltyUser: loyaltyUser),
          ],
        )));
  }
}
