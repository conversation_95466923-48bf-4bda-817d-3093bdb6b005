import 'package:flutter/material.dart';
import 'package:flux_localization/flux_localization.dart';
import 'package:fstore/common/config.dart';
import 'package:fstore/common/tools/flash.dart';
import 'package:fstore/models/entities/loyalty/loyalty_coupon.dart';
import 'package:fstore/routes/flux_navigate.dart';

import '../services/index.dart';
import '../widgets/coupon_card.dart';
import 'add_coupon_screen.dart';

class CouponsManagementScreen extends StatefulWidget {
  const CouponsManagementScreen({super.key});

  @override
  State<CouponsManagementScreen> createState() => _CouponsScreenState();
}

class _CouponsScreenState extends State<CouponsManagementScreen> {
  final _services = LoyaltyServices();
  List<LoyaltyCoupon> _items = [];
  bool _loading = false;

  LoyaltyCoupon? _deletingCoupon;

  Future<void> _getCoupons(BuildContext context) async {
    try {
      setState(() {
        _loading = true;
      });
      _items = await _services.getAllCoupons(context);
    } finally {
      setState(() {
        _loading = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      _getCoupons(context);
    });
  }

  Future<void> _addCoupon(BuildContext context) async {
    var result = await FluxNavigate.push<LoyaltyCoupon?>(
      MaterialPageRoute(builder: (context) => const AddCouponScreen()),
      context: context,
      forceRootNavigator: true,
    );
    if (result != null) {
      setState(() {
        _items = [result, ..._items];
      });
    }
  }

  Future<void> _editCoupon(BuildContext context, LoyaltyCoupon coupon) async {
    var result = await FluxNavigate.push<LoyaltyCoupon?>(
      MaterialPageRoute(builder: (context) => AddCouponScreen(coupon: coupon)),
      context: context,
      forceRootNavigator: true,
    );
    if (result != null) {
      setState(() {
        _items =
            _items.map((e) => e.docId == result.docId ? result : e).toList();
      });
    }
  }

  Future<void> _deleteCoupon(LoyaltyCoupon coupon) async {
    try {
      setState(() {
        _deletingCoupon = coupon;
      });
      await _services.deleteCoupon(coupon: coupon);
      setState(() {
        _items = _items.where((e) => e.docId != coupon.docId).toList();
      });
    } catch (e) {
      await FlashHelper.errorMessage(context, message: e.toString());
    } finally {
      setState(() {
        _deletingCoupon = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        elevation: 0.0,
        title: Text(
          S.of(context).couponsManagement,
          style: Theme.of(context)
              .textTheme
              .titleLarge
              ?.copyWith(fontWeight: FontWeight.w400),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 16.0),
          child: _loading
              ? Center(child: kLoadingWidget())
              : _items.isNotEmpty
                  ? ListView.builder(
                      itemBuilder: (BuildContext context, int index) {
                        var item = _items[index];
                        return CouponCard(
                          padding: const EdgeInsets.only(bottom: 10),
                          coupon: item,
                          loading: _deletingCoupon?.docId == item.docId,
                          onDelete: () {
                            _deleteCoupon(item);
                          },
                          onTap: () {
                            _editCoupon(context, item);
                          },
                        );
                      },
                      itemCount: _items.length,
                    )
                  : Center(child: Text(S.of(context).youDontHaveAnyCoupons)),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _addCoupon(context);
        },
        backgroundColor: Theme.of(context).colorScheme.primary,
        child: const Icon(
          Icons.add,
          color: Colors.white,
        ),
      ),
    );
  }
}
