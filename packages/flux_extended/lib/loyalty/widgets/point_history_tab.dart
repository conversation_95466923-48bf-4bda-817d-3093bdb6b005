import 'package:flutter/material.dart';
import 'package:flux_localization/flux_localization.dart';
import 'package:inspireui/utils/colors.dart';

enum HistoryTab { received, redeemed }

class PointHistoryTab extends StatelessWidget {
  const PointHistoryTab({super.key, required this.tab, required this.onTap});
  final HistoryTab tab;
  final Function(HistoryTab) onTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TabItem(
                label: S.of(context).received,
                active: tab == HistoryTab.received,
                onTap: () {
                  onTap(HistoryTab.received);
                },
              ),
            ),
            Expanded(
              child: TabItem(
                label: S.of(context).redeemed,
                active: tab == HistoryTab.redeemed,
                onTap: () {
                  onTap(HistoryTab.redeemed);
                },
              ),
            ),
          ],
        )
      ],
    );
  }
}

class TabItem extends StatelessWidget {
  const TabItem(
      {super.key,
      required this.label,
      required this.active,
      required this.onTap});
  final String label;
  final bool active;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    var primaryColor = Theme.of(context).primaryColor;
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Theme.of(context).colorScheme.surface,
        child: Column(
          children: [
            const SizedBox(height: 25.0),
            Text(
              label,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 14.0,
                  color: active ? primaryColor : HexColor('#FF667085')),
            ),
            const SizedBox(height: 15.0),
            Container(
              height: 2,
              color: active ? primaryColor : HexColor('#FFD0D5DD'),
            )
          ],
        ),
      ),
    );
  }
}
