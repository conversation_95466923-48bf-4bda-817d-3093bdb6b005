import 'package:flutter/material.dart';
import 'package:flux_localization/flux_localization.dart';
import 'package:flux_ui/flux_ui.dart';
import 'package:fstore/common/constants.dart';
import 'package:fstore/common/tools/price_tools.dart';
import 'package:fstore/models/app_model.dart';
import 'package:fstore/models/cart/cart_base.dart';
import 'package:fstore/models/entities/payment_method.dart';
import 'package:provider/provider.dart';

class RiskFreeCod extends StatefulWidget {
  const RiskFreeCod({super.key, required this.paymentMethod});
  final PaymentMethod paymentMethod;

  @override
  State<RiskFreeCod> createState() => _RiskFreeCodState();
}

class _RiskFreeCodState extends State<RiskFreeCod> {
  bool isChecked = false;
  CartModel get cartModel => Provider.of<CartModel>(context, listen: false);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.endOfFrame.then((_) {
      if (mounted) {
        cartModel.setWooSmartCod(null);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
      child: Column(
        children: [
          Row(children: [
            Checkbox(
              value: isChecked,
              onChanged: (bool? val) {
                setState(() {
                  isChecked = val!;
                });
                if (val == true) {
                  cartModel.setWooSmartCod(widget.paymentMethod.smartCod);
                } else {
                  cartModel.setWooSmartCod(null);
                }
              },
            ),
            Text(
              widget.paymentMethod.title ?? '',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.secondary,
                    fontWeight: FontWeight.bold,
                    fontSize: isDesktop ? 16 : null,
                  ),
            )
          ]),
          if (isChecked)
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColorLight,
                borderRadius: const BorderRadius.all(
                  Radius.circular(6),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 15.0, vertical: 15.0),
                child: HtmlWidget(
                  widget.paymentMethod.description ?? '',
                  textStyle: const TextStyle(height: 1.8),
                ),
              ),
            )
        ],
      ),
    );
  }
}

class SmartCodCheckoutExtraFeeInfo extends StatelessWidget {
  const SmartCodCheckoutExtraFeeInfo({super.key, required this.paymentMethod});
  final PaymentMethod paymentMethod;

  @override
  Widget build(BuildContext context) {
    return Consumer<CartModel>(builder: (context, model, child) {
      final extraFee = model.wooSmartCod?.extraFee ?? 0;
      if (extraFee > 0) {
        return _renderItem(context, S.of(context).codExtraFee, extraFee);
      }
      return const SizedBox();
    });
  }

  Widget _renderItem(BuildContext context, String label, double amount) {
    final currencyRate =
        Provider.of<AppModel>(context, listen: false).currencyRate;
    final currency = Provider.of<AppModel>(context, listen: false).currency;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.secondary,
            ),
          ),
          Text(
            PriceTools.getCurrencyFormatted(amount, currencyRate,
                    currency: currency) ??
                '',
            style: Theme.of(context).textTheme.titleMedium!.copyWith(
                  fontSize: 14,
                  color: Theme.of(context).colorScheme.secondary,
                  fontWeight: FontWeight.w600,
                ),
          )
        ],
      ),
    );
  }
}

class SmartCodCheckoutRiskFreeInfo extends StatelessWidget {
  const SmartCodCheckoutRiskFreeInfo({super.key, required this.paymentMethod});
  final PaymentMethod paymentMethod;

  @override
  Widget build(BuildContext context) {
    return Consumer<CartModel>(builder: (context, model, child) {
      final userAdvanceAmount = model.wooSmartCod?.userAdvanceAmount ?? 0;
      if (userAdvanceAmount > 0) {
        return Container(
          decoration: BoxDecoration(
              color: Theme.of(context).primaryColorLight,
              borderRadius: BorderRadius.circular(6)),
          padding: const EdgeInsets.symmetric(vertical: 10.0),
          child: Column(
            children: [
              _renderItem(context, S.of(context).remainingAmountCod,
                  paymentMethod.smartCod?.remainingAmount ?? 0),
              _renderItem(
                  context, S.of(context).advancePayment, userAdvanceAmount)
            ],
          ),
        );
      }
      return const SizedBox();
    });
  }

  Widget _renderItem(BuildContext context, String label, double amount) {
    final currencyRate =
        Provider.of<AppModel>(context, listen: false).currencyRate;
    final currency = Provider.of<AppModel>(context, listen: false).currency;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.secondary,
            ),
          ),
          Text(
            PriceTools.getCurrencyFormatted(amount, currencyRate,
                    currency: currency) ??
                '',
            style: Theme.of(context).textTheme.titleMedium!.copyWith(
                  fontSize: 14,
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
          )
        ],
      ),
    );
  }
}
