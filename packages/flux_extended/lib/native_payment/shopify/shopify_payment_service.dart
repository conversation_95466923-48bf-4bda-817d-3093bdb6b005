import 'dart:convert';

import 'package:fstore/common/constants.dart';
import 'package:fstore/models/entities/credit_card_new.dart';
import 'package:http/http.dart' as http;

import 'index.dart';

class ShopifyPaymentService {
  // Static variable to store the latest payment result
  static ShopifyPaymentResult? _lastPaymentResult;

  static Future<String?> getPaymentClientToken({
    required String domain,
    required CreditCardNew card,
  }) async {
    final result =
        await http.post('$domain/tokens'.toUri()!, body: card.toJson());
    if (result.statusCode == 200) {
      final data = jsonDecode(result.body);
      if (data['success'] == true) {
        return data['token'];
      }
      throw data['message'];
    }
    return null;
  }

  /// Save payment result
  static void savePaymentResult(ShopifyPaymentResult result) {
    _lastPaymentResult = result;
  }

  /// Get the latest payment result
  static ShopifyPaymentResult? get lastPaymentResult => _lastPaymentResult;
}
