import 'dart:convert' as convert;

import 'package:fstore/services/services.dart';
import 'package:inspireui/inspireui.dart';
import 'package:quiver/strings.dart';

import '../models/point_user.dart';

class OfflineStorePointsServices {
  final domain = Services().api.domain;

  Future<PointUser?> getUserPoints(String userId, String cookie) async {
    try {
      var response = await httpGet(
        Uri.parse('$domain/wp-json/api/flutter_osp/points?user_id=$userId'),
        headers: {'User-Cookie': cookie},
      );
      var jsonDecode = convert.jsonDecode(response.body);
      if (jsonDecode is Map && isNotBlank(jsonDecode['message'])) {
        throw Exception(jsonDecode['message']);
      } else {
        return PointUser.fromJson(jsonDecode);
      }
    } catch (e) {
      //This error exception is about your Rest API is not config correctly so that not return the correct JSON format, please double check the document from this link https://support.inspireui.com/help-center/
      rethrow;
    }
  }

  Future<bool> addPoints(
      {required String userId,
      required int points,
      required String? desc,
      required String cookie}) async {
    try {
      var params = <String, dynamic>{
        'user_id': userId,
        'points': points.toString(),
        'description': desc
      };

      var response = await httpPost(
        Uri.parse('$domain/wp-json/api/flutter_osp/points/add'),
        body: params,
        headers: {'User-Cookie': cookie},
      );
      var jsonDecode = convert.jsonDecode(response.body);
      if (jsonDecode is Map && jsonDecode['status'] == 'success') {
        return true;
      } else if (jsonDecode is Map && isNotBlank(jsonDecode['message'])) {
        throw Exception(jsonDecode['message']);
      } else {
        return false;
      }
    } catch (e) {
      //This error exception is about your Rest API is not config correctly so that not return the correct JSON format, please double check the document from this link https://support.inspireui.com/help-center/
      rethrow;
    }
  }

  Future<bool> usePoints(
      {required String userId,
      required int points,
      required String? desc,
      required String cookie}) async {
    try {
      var params = {
        'user_id': userId,
        'points': points.toString(),
        'description': desc
      };

      var response = await httpPost(
        Uri.parse('$domain/wp-json/api/flutter_osp/points/subtract'),
        body: params,
        headers: {'User-Cookie': cookie},
      );
      var jsonDecode = convert.jsonDecode(response.body);
      if (jsonDecode is Map && jsonDecode['status'] == 'success') {
        return true;
      } else if (jsonDecode is Map && isNotBlank(jsonDecode['message'])) {
        throw Exception(jsonDecode['message']);
      } else {
        return false;
      }
    } catch (e) {
      //This error exception is about your Rest API is not config correctly so that not return the correct JSON format, please double check the document from this link https://support.inspireui.com/help-center/
      rethrow;
    }
  }
}
