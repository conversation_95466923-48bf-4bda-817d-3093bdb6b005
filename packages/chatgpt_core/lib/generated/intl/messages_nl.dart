// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a nl locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'nl';

  static String m0(limit) =>
      "Er is slechts ${limit}x zoeken naar afbeeldingen in de gratis versie.";

  static String m1(limit) =>
      "Er kunnen maximaal ${limit} berichten worden weergegeven in de gratis versie.";

  static String m2(date) => "Vervaldatum abonnement ${date}";

  static String m3(number) =>
      "Genereren (${number} ${Intl.plural(number, one: 'time', other: 'times')})";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about_openai": MessageLookupByLibrary.simpleMessage("Wat betreft"),
    "apply_openai": MessageLookupByLibrary.simpleMessage("Van toepassing zijn"),
    "artist_openai": MessageLookupByLibrary.simpleMessage("Artiest"),
    "cancel_openai": MessageLookupByLibrary.simpleMessage("annuleren"),
    "chatDetail_openai": MessageLookupByLibrary.simpleMessage("Chatdetails"),
    "chatGPT_openai": MessageLookupByLibrary.simpleMessage("GPT chatten"),
    "chatWithBot_openai": MessageLookupByLibrary.simpleMessage(
      "Chatten met bot",
    ),
    "chat_openai": MessageLookupByLibrary.simpleMessage("babbelen"),
    "chooseArtist_openai": MessageLookupByLibrary.simpleMessage(
      "Kies artiest voor je afbeelding",
    ),
    "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
      "Kies detail voor uw afbeelding",
    ),
    "chooseMedium_openai": MessageLookupByLibrary.simpleMessage(
      "Kies medium voor je afbeelding",
    ),
    "chooseMood_openai": MessageLookupByLibrary.simpleMessage(
      "Kies de stemming voor uw afbeelding",
    ),
    "chooseUseCase_openai": MessageLookupByLibrary.simpleMessage(
      "Kies een use-case",
    ),
    "choseStyle_openai": MessageLookupByLibrary.simpleMessage(
      "Kies stijl voor uw afbeelding",
    ),
    "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
      "Weet u zeker dat u de inhoud wilt wissen?",
    ),
    "clearContent_openai": MessageLookupByLibrary.simpleMessage(
      "Duidelijke inhoud",
    ),
    "clearConversation_openai": MessageLookupByLibrary.simpleMessage(
      "Duidelijke conversatie",
    ),
    "clear_openai": MessageLookupByLibrary.simpleMessage("Duidelijk"),
    "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
      "Weet u zeker dat u dit item wilt verwijderen?",
    ),
    "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
      "Gelieve te bevestigen of u door wilt gaan met het verwijderen van dit item. U kunt deze actie niet ongedaan maken.",
    ),
    "confirmRemoveKey_openai": MessageLookupByLibrary.simpleMessage(
      "Weet u zeker dat u de sleutel wilt verwijderen?",
    ),
    "confirm_openai": MessageLookupByLibrary.simpleMessage("Bevestigen"),
    "copiedToClipboard_openai": MessageLookupByLibrary.simpleMessage(
      "Gekopieerde inhoud naar klembord",
    ),
    "copy_openai": MessageLookupByLibrary.simpleMessage("Kopiëren"),
    "createChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Chat maken mislukt",
    ),
    "deleteFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Verwijderen is mislukt",
    ),
    "delete_openai": MessageLookupByLibrary.simpleMessage("Verwijder"),
    "detail_openai": MessageLookupByLibrary.simpleMessage("Detail"),
    "download_openai": MessageLookupByLibrary.simpleMessage("Download"),
    "edit_openai": MessageLookupByLibrary.simpleMessage("BEWERK"),
    "failedToGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Kan niet genereren",
    ),
    "generate_openai": MessageLookupByLibrary.simpleMessage("voortbrengen"),
    "grid_openai": MessageLookupByLibrary.simpleMessage("Rooster"),
    "imageGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Afbeelding genereren",
    ),
    "imageSize_openai": MessageLookupByLibrary.simpleMessage(
      "Afbeeldingsgrootte",
    ),
    "inputKey_openai": MessageLookupByLibrary.simpleMessage("Invoersleutel"),
    "interest_openai": MessageLookupByLibrary.simpleMessage("Interesseren"),
    "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
      "Uw API-sleutel wordt lokaal op uw mobiel opgeslagen en wordt nooit ergens anders naartoe gestuurd. U kunt uw sleutel opslaan om deze later te gebruiken. U kunt uw sleutel ook verwijderen als u deze niet meer wilt gebruiken.",
    ),
    "invalidKey_openai": MessageLookupByLibrary.simpleMessage(
      "Ongeldige sleutel",
    ),
    "jobRole_openai": MessageLookupByLibrary.simpleMessage("Taakomschrijving"),
    "jobSkills_openai": MessageLookupByLibrary.simpleMessage(
      "Baan vaardigheden",
    ),
    "layoutStyle_openai": MessageLookupByLibrary.simpleMessage("Layout-stijl"),
    "limitImage_openai": m0,
    "limitTheText_openai": m1,
    "listening_openai": MessageLookupByLibrary.simpleMessage("Luisteren..."),
    "loadKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Sleutel laden mislukt",
    ),
    "loadKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "Laad sleutelsucces",
    ),
    "manage_openai": MessageLookupByLibrary.simpleMessage("Beheren"),
    "medium_openai": MessageLookupByLibrary.simpleMessage("Medium"),
    "mood_openai": MessageLookupByLibrary.simpleMessage("Humeur"),
    "moreOptions_openai": MessageLookupByLibrary.simpleMessage("Meer opties"),
    "newChat_openai": MessageLookupByLibrary.simpleMessage("Nieuw gesprek"),
    "noImageGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Er wordt geen afbeelding gegenereerd",
    ),
    "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
      "Het aantal te genereren afbeeldingen. Moet tussen 1 en 10 zijn.",
    ),
    "numberOfImages_openai": MessageLookupByLibrary.simpleMessage(
      "Aantal afbeeldingen",
    ),
    "options_openai": MessageLookupByLibrary.simpleMessage("opties"),
    "page_openai": MessageLookupByLibrary.simpleMessage("Pagina"),
    "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
      "Controleer je verbinding en probeer het opnieuw!",
    ),
    "pleaseInputFillAllFields_openai": MessageLookupByLibrary.simpleMessage(
      "Vul alstublieft alle velden in",
    ),
    "pleaseInputKey_openai": MessageLookupByLibrary.simpleMessage(
      "Voer de sleutel in",
    ),
    "prompt_openai": MessageLookupByLibrary.simpleMessage("snel"),
    "putKeyHere_openai": MessageLookupByLibrary.simpleMessage(
      "Leg hier uw sleutel neer",
    ),
    "regenerateResponse_openai": MessageLookupByLibrary.simpleMessage(
      "Reactie opnieuw genereren",
    ),
    "remaining_openai": MessageLookupByLibrary.simpleMessage("overblijvende"),
    "removeKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Sleutel verwijderen mislukt",
    ),
    "removeKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "Sleutel succesvol verwijderd",
    ),
    "remove_openai": MessageLookupByLibrary.simpleMessage("Verwijderen"),
    "resetSettings_openai": MessageLookupByLibrary.simpleMessage(
      "Reset instellingen",
    ),
    "reset_openai": MessageLookupByLibrary.simpleMessage("Reset"),
    "saveKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Sleutel opslaan mislukt",
    ),
    "saveKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "Sleutel succesvol opgeslagen",
    ),
    "saveKey_openai": MessageLookupByLibrary.simpleMessage("Sleutel opslaan"),
    "save_openai": MessageLookupByLibrary.simpleMessage("Opslaan"),
    "searchByPrompt_openai": MessageLookupByLibrary.simpleMessage(
      "Zoeken op prompt...",
    ),
    "sectionKeywords_openai": MessageLookupByLibrary.simpleMessage(
      "Sectie Trefwoorden",
    ),
    "sectionTopic_openai": MessageLookupByLibrary.simpleMessage(
      "Sectie onderwerp",
    ),
    "selectChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Selecteer Chat mislukt",
    ),
    "selectPrompt_openai": MessageLookupByLibrary.simpleMessage(
      "Selecteer vragen",
    ),
    "settings_openai": MessageLookupByLibrary.simpleMessage("instellingen"),
    "share_openai": MessageLookupByLibrary.simpleMessage("Delen"),
    "skills_openai": MessageLookupByLibrary.simpleMessage("vaardigheden"),
    "somethingWentWrong_openai": MessageLookupByLibrary.simpleMessage(
      "Er is iets fout gegaan!!!",
    ),
    "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
      "Er is iets fout gegaan! Probeer het later opnieuw. Ontzettend bedankt!",
    ),
    "speechNotAvailable_openai": MessageLookupByLibrary.simpleMessage(
      "Spraak niet beschikbaar",
    ),
    "style_openai": MessageLookupByLibrary.simpleMessage("Stijl"),
    "subscriptionExpiredDate_openai": m2,
    "tapTheMicToTalk_openai": MessageLookupByLibrary.simpleMessage(
      "Tik op de microfoon om te praten",
    ),
    "textGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Tekst genereren",
    ),
    "textGenerator_openai": MessageLookupByLibrary.simpleMessage(
      "Tekstgenerator",
    ),
    "timeGenerate_openai": m3,
    "typeAMessage_openai": MessageLookupByLibrary.simpleMessage(
      "Type een bericht...",
    ),
    "viewType_openai": MessageLookupByLibrary.simpleMessage("Type weergave"),
    "view_openai": MessageLookupByLibrary.simpleMessage("Uitzicht"),
    "write_openai": MessageLookupByLibrary.simpleMessage("SCHRIJVEN"),
  };
}
