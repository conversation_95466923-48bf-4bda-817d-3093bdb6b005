// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ti locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ti';

  static String m0(limit) => "ኣብቲ ነጻ ስሪት ${limit}x ምስሊ ምድላይ ጥራይ እዩ ዘሎ።";

  static String m1(limit) =>
      "ክሳብ ${limit} ዝበጽሕ መልእኽትታት ኣብቲ ነጻ ስሪት ጥራይ እዩ ክረአ ዝኽእል።";

  static String m2(date) => "ምዝገባ ዝውድኣሉ ዕለት ${date}";

  static String m3(number) =>
      "ምፍጣር (${number} ${Intl.plural(number, one: 'time', other: 'times')}) ።";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about_openai": MessageLookupByLibrary.simpleMessage("ብዝዕባ"),
    "apply_openai": MessageLookupByLibrary.simpleMessage("ኣተግብር"),
    "artist_openai": MessageLookupByLibrary.simpleMessage("ኣርቲስት"),
    "cancel_openai": MessageLookupByLibrary.simpleMessage("ሰርዝ"),
    "chatDetail_openai": MessageLookupByLibrary.simpleMessage("ዝርዝር ዕላል"),
    "chatGPT_openai": MessageLookupByLibrary.simpleMessage("ቻት ጂፒቲ"),
    "chatWithBot_openai": MessageLookupByLibrary.simpleMessage("ምስ ቦት ዕላል ግበር"),
    "chat_openai": MessageLookupByLibrary.simpleMessage("ፀወታ"),
    "chooseArtist_openai": MessageLookupByLibrary.simpleMessage(
      "ምስልኻ ስነጥበባዊ ምረጽ",
    ),
    "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
      "ምስልኻ ዝርዝር ምረጽ",
    ),
    "chooseMedium_openai": MessageLookupByLibrary.simpleMessage(
      "ምስልኻ ማእከላይ ምረጽ",
    ),
    "chooseMood_openai": MessageLookupByLibrary.simpleMessage("ምስልኻ ስሚዒት ምረጽ"),
    "chooseUseCase_openai": MessageLookupByLibrary.simpleMessage(
      "use case ምረጽ",
    ),
    "choseStyle_openai": MessageLookupByLibrary.simpleMessage("ምስልኻ ቅዲ ምረጽ"),
    "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
      "ትሕዝቶ ከተጽርዮ ርግጸኛ ዲኻ?",
    ),
    "clearContent_openai": MessageLookupByLibrary.simpleMessage("ንጹር ትሕዝቶ"),
    "clearConversation_openai": MessageLookupByLibrary.simpleMessage("ንጹር ዕላል"),
    "clear_openai": MessageLookupByLibrary.simpleMessage("ንፁር"),
    "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
      "ነዚ ንብረት ክትድምስሶ ከም እትደሊ ርግጸኛ ዲኻ?",
    ),
    "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
      "እዚ ንብረት ምድምሳስ ክትቅጽሉ ምስ እትደልዩ ኣረጋግጹ። ነዚ ተግባር ክትመልሶ ኣይትኽእልን ኢኻ።",
    ),
    "confirmRemoveKey_openai": MessageLookupByLibrary.simpleMessage(
      "መፍትሕ ከም እተውጽእ ርግጸኛ ዲኻ?",
    ),
    "confirm_openai": MessageLookupByLibrary.simpleMessage("ኣረጋግፅ"),
    "copiedToClipboard_openai": MessageLookupByLibrary.simpleMessage(
      "ትሕዝቶ ናብ ቅንጥብጣብ ሰሌዳ ዝተቐድሐ",
    ),
    "copy_openai": MessageLookupByLibrary.simpleMessage("ቅዳሕ"),
    "createChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "ቻት ምፍጣር ፈሺሉ።",
    ),
    "deleteFailed_openai": MessageLookupByLibrary.simpleMessage(
      "ምድምሳስ ኣይተዓወተን",
    ),
    "delete_openai": MessageLookupByLibrary.simpleMessage("ምድምሳስ"),
    "detail_openai": MessageLookupByLibrary.simpleMessage("ዝርዝር"),
    "download_openai": MessageLookupByLibrary.simpleMessage("ምውራድ"),
    "edit_openai": MessageLookupByLibrary.simpleMessage("ምዕራይ"),
    "failedToGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "ምፍራይ ኣይከኣለን",
    ),
    "generate_openai": MessageLookupByLibrary.simpleMessage("ምምንጫው"),
    "grid_openai": MessageLookupByLibrary.simpleMessage("መስመር"),
    "imageGenerate_openai": MessageLookupByLibrary.simpleMessage("ምስሊ ምፍጣር"),
    "imageSize_openai": MessageLookupByLibrary.simpleMessage("መጠን ምስሊ"),
    "inputKey_openai": MessageLookupByLibrary.simpleMessage("መፍትሕ ምእታው"),
    "interest_openai": MessageLookupByLibrary.simpleMessage("ወለድ"),
    "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
      "ኤፒኣይ መፍትሕካ ኣብ ሞባይልካ ኣብ ከባቢኻ ይኽዘን እምበር ናብ ካልእ ቦታ ተላኢኹ ኣይፈልጥን። መፍትሕካ ድሒርካ ክትጥቀመሉ ክትዕቅቦ ትኽእል ኢኻ። ድሕሪ ሕጂ ክትጥቀመሉ እንተዘይደሊኻ ውን መፍትሕካ ከተውጽኦ ትኽእል ኢኻ።",
    ),
    "invalidKey_openai": MessageLookupByLibrary.simpleMessage("ዘይሕጋዊ መፍትሕ"),
    "jobRole_openai": MessageLookupByLibrary.simpleMessage("ተራ ስራሕ"),
    "jobSkills_openai": MessageLookupByLibrary.simpleMessage("ናይ ስራሕ ክእለት።"),
    "layoutStyle_openai": MessageLookupByLibrary.simpleMessage("ቅዲ ኣቀማምጣ"),
    "limitImage_openai": m0,
    "limitTheText_openai": m1,
    "listening_openai": MessageLookupByLibrary.simpleMessage("ምድማፅ..."),
    "loadKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "መፍትሕ ጽዕነት ፈሺሉ።",
    ),
    "loadKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "ዓወት ቁልፊ ጽዕነት",
    ),
    "manage_openai": MessageLookupByLibrary.simpleMessage("ኣተሓሕዛ"),
    "medium_openai": MessageLookupByLibrary.simpleMessage("ማእኸላይ"),
    "mood_openai": MessageLookupByLibrary.simpleMessage("ስምዒት"),
    "moreOptions_openai": MessageLookupByLibrary.simpleMessage("ተወሳኺ ኣማራጺታት"),
    "newChat_openai": MessageLookupByLibrary.simpleMessage("ሓድሽ ዕላል"),
    "noImageGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "ምስሊ ዝፈጥር የለን",
    ),
    "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
      "ብዝሒ ምስልታት ክትፈጥሮ ዘለካ። ካብ 1 ክሳብ 10 ክኸውን ኣለዎ።",
    ),
    "numberOfImages_openai": MessageLookupByLibrary.simpleMessage("ብዝሒ ምስልታት"),
    "options_openai": MessageLookupByLibrary.simpleMessage("ኣማራጺታት"),
    "page_openai": MessageLookupByLibrary.simpleMessage("ገጽ"),
    "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
      "በጃኹም ርክብኩም ኣረጋግጹ እሞ እንደገና ፈትኑ!",
    ),
    "pleaseInputFillAllFields_openai": MessageLookupByLibrary.simpleMessage(
      "በጃኹም ኩሉ ቦታታት ምልኣዩ።",
    ),
    "pleaseInputKey_openai": MessageLookupByLibrary.simpleMessage(
      "በጃኹም መፍትሕ ኣእትዉ",
    ),
    "prompt_openai": MessageLookupByLibrary.simpleMessage("ምስዓብ"),
    "putKeyHere_openai": MessageLookupByLibrary.simpleMessage("መፍትሕካ ኣብዚ ኣቐምጥ"),
    "regenerateResponse_openai": MessageLookupByLibrary.simpleMessage(
      "ምላሽ ዳግማይ ምፍጣር",
    ),
    "remaining_openai": MessageLookupByLibrary.simpleMessage("ዝተረፈ"),
    "removeKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "መፍትሕ ምእላይ ፈሺሉ።",
    ),
    "removeKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "መፍትሕ ብዓወት ተኣልዩ።",
    ),
    "remove_openai": MessageLookupByLibrary.simpleMessage("ኣወግድ"),
    "resetSettings_openai": MessageLookupByLibrary.simpleMessage(
      "ቅጥዕታት ዳግማይ ምትካል",
    ),
    "reset_openai": MessageLookupByLibrary.simpleMessage("ዳግማይ ምትካል"),
    "saveKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "መፍትሕ ምዕቃብ ፈሺሉ።",
    ),
    "saveKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "ዝተዓቀበ መፍትሕ ብዓወት",
    ),
    "saveKey_openai": MessageLookupByLibrary.simpleMessage("መፍትሕ ምዕቃብ"),
    "save_openai": MessageLookupByLibrary.simpleMessage("ምቑጣብ"),
    "searchByPrompt_openai": MessageLookupByLibrary.simpleMessage(
      "ብፕሮምፕት ድለዩ...",
    ),
    "sectionKeywords_openai": MessageLookupByLibrary.simpleMessage(
      "ክፍሊ መፍትሕ ቃላት",
    ),
    "sectionTopic_openai": MessageLookupByLibrary.simpleMessage("ክፍሊ ኣርእስቲ"),
    "selectChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Chat Failed ዝብል ምረጽ",
    ),
    "selectPrompt_openai": MessageLookupByLibrary.simpleMessage(
      "Prompt ዝብል ምረጽ",
    ),
    "settings_openai": MessageLookupByLibrary.simpleMessage("ቅጥዕታት"),
    "share_openai": MessageLookupByLibrary.simpleMessage("ናይ ሓባር"),
    "skills_openai": MessageLookupByLibrary.simpleMessage("ክእለታት"),
    "somethingWentWrong_openai": MessageLookupByLibrary.simpleMessage(
      "ገለ ነገር ተጋግዩ!!!",
    ),
    "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
      "ገለ ነገር ተጋግዩ! በጃኹም ድሒርኩም ደጊምኩም ፈትኑ። ብጣዕሚ የቅንየልና!",
    ),
    "speechNotAvailable_openai": MessageLookupByLibrary.simpleMessage(
      "ዘረባ ኣይርከብን።",
    ),
    "style_openai": MessageLookupByLibrary.simpleMessage("ኣገባብ"),
    "subscriptionExpiredDate_openai": m2,
    "tapTheMicToTalk_openai": MessageLookupByLibrary.simpleMessage(
      "ንምዝርራብ ማይክ ጠውቕ",
    ),
    "textGenerate_openai": MessageLookupByLibrary.simpleMessage("ጽሑፍ ምፍጣር"),
    "textGenerator_openai": MessageLookupByLibrary.simpleMessage("ጽሑፍ ጀነሬተር"),
    "timeGenerate_openai": m3,
    "typeAMessage_openai": MessageLookupByLibrary.simpleMessage("መልእኽቲ ጽሓፍ..."),
    "viewType_openai": MessageLookupByLibrary.simpleMessage("ዓይነት ርእይቶ"),
    "view_openai": MessageLookupByLibrary.simpleMessage("ኣረኣእያ"),
    "write_openai": MessageLookupByLibrary.simpleMessage("ፀሓፍ"),
  };
}
