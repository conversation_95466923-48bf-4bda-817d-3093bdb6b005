// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(limit) =>
      "لا يوجد سوى ${limit}بحث عن الصور في الإصدار المجاني.";

  static String m1(limit) =>
      "يمكن عرض ما يصل إلى ${limit} من الرسائل فقط في الإصدار المجاني.";

  static String m2(date) => "تاريخ انتهاء الاشتراك ${date}";

  static String m3(number) =>
      "إنشاء (${number} ${Intl.plural(number, one: 'time', other: 'times')})";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about_openai": MessageLookupByLibrary.simpleMessage("حول"),
    "apply_openai": MessageLookupByLibrary.simpleMessage("تطبيق"),
    "artist_openai": MessageLookupByLibrary.simpleMessage("فنان"),
    "cancel_openai": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "chatDetail_openai": MessageLookupByLibrary.simpleMessage("تفاصيل الدردشة"),
    "chatGPT_openai": MessageLookupByLibrary.simpleMessage("دردشة GPT"),
    "chatWithBot_openai": MessageLookupByLibrary.simpleMessage(
      "الدردشة مع بوت",
    ),
    "chat_openai": MessageLookupByLibrary.simpleMessage("دردشة"),
    "chooseArtist_openai": MessageLookupByLibrary.simpleMessage(
      "اختر فنان لصورتك",
    ),
    "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
      "اختر التفاصيل لصورتك",
    ),
    "chooseMedium_openai": MessageLookupByLibrary.simpleMessage(
      "اختر وسيط لصورتك",
    ),
    "chooseMood_openai": MessageLookupByLibrary.simpleMessage(
      "اختر الحالة المزاجية لصورتك",
    ),
    "chooseUseCase_openai": MessageLookupByLibrary.simpleMessage(
      "اختر حالة الاستخدام",
    ),
    "choseStyle_openai": MessageLookupByLibrary.simpleMessage(
      "اختر نمط لصورتك",
    ),
    "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من مسح المحتوى؟",
    ),
    "clearContent_openai": MessageLookupByLibrary.simpleMessage("محتوى واضح"),
    "clearConversation_openai": MessageLookupByLibrary.simpleMessage(
      "محادثة واضحة",
    ),
    "clear_openai": MessageLookupByLibrary.simpleMessage("واضح"),
    "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك ترغب في حذف هذا العنصر؟",
    ),
    "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
      "يرجى التأكيد إذا كنت ترغب في متابعة حذف هذا العنصر. لا يمكنك التراجع عن هذا الإجراء.",
    ),
    "confirmRemoveKey_openai": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من إزالة المفتاح؟",
    ),
    "confirm_openai": MessageLookupByLibrary.simpleMessage("تؤكد"),
    "copiedToClipboard_openai": MessageLookupByLibrary.simpleMessage(
      "تم نسخ المحتوى إلى الحافظة",
    ),
    "copy_openai": MessageLookupByLibrary.simpleMessage("نسخ"),
    "createChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "فشل إنشاء الدردشة",
    ),
    "deleteFailed_openai": MessageLookupByLibrary.simpleMessage("فشل الحذف"),
    "delete_openai": MessageLookupByLibrary.simpleMessage("حذف"),
    "detail_openai": MessageLookupByLibrary.simpleMessage("التفاصيل"),
    "download_openai": MessageLookupByLibrary.simpleMessage("تحميل"),
    "edit_openai": MessageLookupByLibrary.simpleMessage("تصحيح"),
    "failedToGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "فشل في الإنشاء",
    ),
    "generate_openai": MessageLookupByLibrary.simpleMessage("توليد"),
    "grid_openai": MessageLookupByLibrary.simpleMessage("شبكة"),
    "imageGenerate_openai": MessageLookupByLibrary.simpleMessage("توليد الصور"),
    "imageSize_openai": MessageLookupByLibrary.simpleMessage("حجم الصورة"),
    "inputKey_openai": MessageLookupByLibrary.simpleMessage("مفتاح الإدخال"),
    "interest_openai": MessageLookupByLibrary.simpleMessage("فائدة"),
    "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
      "يتم تخزين مفتاح API الخاص بك محليًا على هاتفك المحمول ولا يتم إرساله إلى أي مكان آخر. يمكنك حفظ مفتاحك لاستخدامه لاحقًا. يمكنك أيضًا إزالة مفتاحك إذا كنت لا تريد استخدامه بعد الآن.",
    ),
    "invalidKey_openai": MessageLookupByLibrary.simpleMessage("مفتاح غير صالح"),
    "jobRole_openai": MessageLookupByLibrary.simpleMessage("الدور الوظيفي"),
    "jobSkills_openai": MessageLookupByLibrary.simpleMessage("مهارات العمل"),
    "layoutStyle_openai": MessageLookupByLibrary.simpleMessage("اسلوب التصميم"),
    "limitImage_openai": m0,
    "limitTheText_openai": m1,
    "listening_openai": MessageLookupByLibrary.simpleMessage("الاستماع ..."),
    "loadKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "فشل تحميل المفتاح",
    ),
    "loadKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "تحميل مفتاح النجاح",
    ),
    "manage_openai": MessageLookupByLibrary.simpleMessage("يدير"),
    "medium_openai": MessageLookupByLibrary.simpleMessage("متوسط"),
    "mood_openai": MessageLookupByLibrary.simpleMessage("مزاج"),
    "moreOptions_openai": MessageLookupByLibrary.simpleMessage(
      "المزيد من الخيارات",
    ),
    "newChat_openai": MessageLookupByLibrary.simpleMessage("دردشة جديدة"),
    "noImageGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "لا توجد صورة تولد",
    ),
    "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
      "عدد الصور المراد إنشاؤها. يجب أن يكون بين 1 و 10.",
    ),
    "numberOfImages_openai": MessageLookupByLibrary.simpleMessage("عدد الصور"),
    "options_openai": MessageLookupByLibrary.simpleMessage("خيارات"),
    "page_openai": MessageLookupByLibrary.simpleMessage("صفحة"),
    "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
      "يرجى التحقق من اتصالك وحاول مرة أخرى!",
    ),
    "pleaseInputFillAllFields_openai": MessageLookupByLibrary.simpleMessage(
      "يرجى تعبئة جميع الحقول",
    ),
    "pleaseInputKey_openai": MessageLookupByLibrary.simpleMessage(
      "الرجاء إدخال المفتاح",
    ),
    "prompt_openai": MessageLookupByLibrary.simpleMessage("مستعجل"),
    "putKeyHere_openai": MessageLookupByLibrary.simpleMessage("ضع مفتاحك هنا"),
    "regenerateResponse_openai": MessageLookupByLibrary.simpleMessage(
      "تجديد الاستجابة",
    ),
    "remaining_openai": MessageLookupByLibrary.simpleMessage("متبق"),
    "removeKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "فشل إزالة المفتاح",
    ),
    "removeKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "تمت إزالة المفتاح بنجاح",
    ),
    "remove_openai": MessageLookupByLibrary.simpleMessage("إزالة"),
    "resetSettings_openai": MessageLookupByLibrary.simpleMessage("اعادة الضبط"),
    "reset_openai": MessageLookupByLibrary.simpleMessage("إعادة تعيين"),
    "saveKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "فشل حفظ المفتاح",
    ),
    "saveKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "تم حفظ المفتاح بنجاح",
    ),
    "saveKey_openai": MessageLookupByLibrary.simpleMessage("حفظ مفتاح"),
    "save_openai": MessageLookupByLibrary.simpleMessage("حفظ"),
    "searchByPrompt_openai": MessageLookupByLibrary.simpleMessage(
      "البحث بالموجه ...",
    ),
    "sectionKeywords_openai": MessageLookupByLibrary.simpleMessage(
      "كلمات القسم",
    ),
    "sectionTopic_openai": MessageLookupByLibrary.simpleMessage("موضوع القسم"),
    "selectChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "حدد فشل الدردشة",
    ),
    "selectPrompt_openai": MessageLookupByLibrary.simpleMessage("حدد موجه"),
    "settings_openai": MessageLookupByLibrary.simpleMessage("الإعدادات"),
    "share_openai": MessageLookupByLibrary.simpleMessage("شارك"),
    "skills_openai": MessageLookupByLibrary.simpleMessage("مهارات"),
    "somethingWentWrong_openai": MessageLookupByLibrary.simpleMessage(
      "هناك خطأ ما!!!",
    ),
    "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
      "هناك خطأ ما! الرجاء معاودة المحاولة في وقت لاحق. شكراً جزيلاً!",
    ),
    "speechNotAvailable_openai": MessageLookupByLibrary.simpleMessage(
      "الكلام غير متوفر",
    ),
    "style_openai": MessageLookupByLibrary.simpleMessage("قلم المدقة"),
    "subscriptionExpiredDate_openai": m2,
    "tapTheMicToTalk_openai": MessageLookupByLibrary.simpleMessage(
      "اضغط على الميكروفون للتحدث",
    ),
    "textGenerate_openai": MessageLookupByLibrary.simpleMessage("توليد النص"),
    "textGenerator_openai": MessageLookupByLibrary.simpleMessage("مولد النص"),
    "timeGenerate_openai": m3,
    "typeAMessage_openai": MessageLookupByLibrary.simpleMessage(
      "اكتب رسالة ...",
    ),
    "viewType_openai": MessageLookupByLibrary.simpleMessage("نوع العرض"),
    "view_openai": MessageLookupByLibrary.simpleMessage("رأي"),
    "write_openai": MessageLookupByLibrary.simpleMessage("يكتب"),
  };
}
