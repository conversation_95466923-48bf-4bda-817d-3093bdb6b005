// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a tr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'tr';

  static String m0(limit) =>
      "Ücretsiz sürümde yalnızca ${limit}x görsel arama yapabilirsiniz.";

  static String m1(limit) =>
      "Ücretsiz sürümde en fazla ${limit} mesaj görüntülenebilir.";

  static String m2(date) => "Aboneliğiniz ${date} tarihinde sona erecek";

  static String m3(number) =>
      "Oluştur (${number} ${Intl.plural(number, one: 'time', other: 'times')})";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about_openai": MessageLookupByLibrary.simpleMessage("Hakkında"),
    "apply_openai": MessageLookupByLibrary.simpleMessage("Uygula"),
    "artist_openai": MessageLookupByLibrary.simpleMessage("Sanatçı"),
    "cancel_openai": MessageLookupByLibrary.simpleMessage("İptal"),
    "chatDetail_openai": MessageLookupByLibrary.simpleMessage("Sohbet Detayı"),
    "chatGPT_openai": MessageLookupByLibrary.simpleMessage("Chat GPT"),
    "chatWithBot_openai": MessageLookupByLibrary.simpleMessage(
      "Botla sohbet et",
    ),
    "chat_openai": MessageLookupByLibrary.simpleMessage("Sohbet"),
    "chooseArtist_openai": MessageLookupByLibrary.simpleMessage(
      "Resminiz için bir sanatçı seçin",
    ),
    "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
      "Resminiz için bir detay seçin",
    ),
    "chooseMedium_openai": MessageLookupByLibrary.simpleMessage(
      "Resminiz için bir ortam seçin",
    ),
    "chooseMood_openai": MessageLookupByLibrary.simpleMessage(
      "Resminiz için bir ruh hali seçin",
    ),
    "chooseUseCase_openai": MessageLookupByLibrary.simpleMessage(
      "Kullanım senaryosunu seçin",
    ),
    "choseStyle_openai": MessageLookupByLibrary.simpleMessage(
      "Resminiz için bir stil seçin",
    ),
    "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
      "İçeriği temizlemek istediğinizden emin misiniz?",
    ),
    "clearContent_openai": MessageLookupByLibrary.simpleMessage(
      "İçeriği temizle",
    ),
    "clearConversation_openai": MessageLookupByLibrary.simpleMessage(
      "Konuşmayı temizle",
    ),
    "clear_openai": MessageLookupByLibrary.simpleMessage("Temizle"),
    "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
      "Bu öğeyi silmek istediğinizden emin misiniz?",
    ),
    "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
      "Bu öğenin silinmesini onaylamak istediğinizi lütfen onaylayın. Bu işlem geri alınamaz.",
    ),
    "confirmRemoveKey_openai": MessageLookupByLibrary.simpleMessage(
      "Anahtarı kaldırmak istediğinizden emin misiniz?",
    ),
    "confirm_openai": MessageLookupByLibrary.simpleMessage("Onayla"),
    "copiedToClipboard_openai": MessageLookupByLibrary.simpleMessage(
      "İçerik panoya kopyalandı",
    ),
    "copy_openai": MessageLookupByLibrary.simpleMessage("Kopyala"),
    "createChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Sohbet Oluşturma Başarısız",
    ),
    "deleteFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Silme başarısız oldu",
    ),
    "delete_openai": MessageLookupByLibrary.simpleMessage("Sil"),
    "detail_openai": MessageLookupByLibrary.simpleMessage("Detay"),
    "download_openai": MessageLookupByLibrary.simpleMessage("İndir"),
    "edit_openai": MessageLookupByLibrary.simpleMessage("Düzenle"),
    "failedToGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Oluşturulamadı",
    ),
    "generate_openai": MessageLookupByLibrary.simpleMessage("Oluştur"),
    "grid_openai": MessageLookupByLibrary.simpleMessage("Kılavuz"),
    "imageGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Görüntü oluştur",
    ),
    "imageSize_openai": MessageLookupByLibrary.simpleMessage("Görüntü boyutu"),
    "inputKey_openai": MessageLookupByLibrary.simpleMessage("Giriş Anahtarı"),
    "interest_openai": MessageLookupByLibrary.simpleMessage("İlgi"),
    "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
      "API Anahtarınız yerel olarak cep telefonunuzda saklanır ve asla başka bir yere gönderilmez. Anahtarınızı daha sonra kullanmak üzere kaydedebilirsiniz. Artık kullanmak istemiyorsanız, anahtarınızı da kaldırabilirsiniz.",
    ),
    "invalidKey_openai": MessageLookupByLibrary.simpleMessage(
      "Geçersiz Anahtar",
    ),
    "jobRole_openai": MessageLookupByLibrary.simpleMessage("İş rolü"),
    "jobSkills_openai": MessageLookupByLibrary.simpleMessage("İş becerileri"),
    "layoutStyle_openai": MessageLookupByLibrary.simpleMessage("Düzen Stili"),
    "limitImage_openai": m0,
    "limitTheText_openai": m1,
    "listening_openai": MessageLookupByLibrary.simpleMessage("Dinleniyor..."),
    "loadKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Anahtar Yüklenemedi",
    ),
    "loadKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "Anahtar Başarıyla Yüklendi",
    ),
    "manage_openai": MessageLookupByLibrary.simpleMessage("Yönet"),
    "medium_openai": MessageLookupByLibrary.simpleMessage("Orta"),
    "mood_openai": MessageLookupByLibrary.simpleMessage("Mod"),
    "moreOptions_openai": MessageLookupByLibrary.simpleMessage(
      "Daha fazla seçenek",
    ),
    "newChat_openai": MessageLookupByLibrary.simpleMessage("Yeni Sohbet"),
    "noImageGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Görüntü oluşturma yok",
    ),
    "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
      "Oluşturulacak görüntü sayısı. 1 ile 10 arasında olmalıdır.",
    ),
    "numberOfImages_openai": MessageLookupByLibrary.simpleMessage(
      "Resim sayısı",
    ),
    "options_openai": MessageLookupByLibrary.simpleMessage("Seçenekler"),
    "page_openai": MessageLookupByLibrary.simpleMessage("Sayfa"),
    "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
      "Lütfen bağlantınızı kontrol edin ve tekrar deneyin!",
    ),
    "pleaseInputFillAllFields_openai": MessageLookupByLibrary.simpleMessage(
      "Lütfen tüm alanları doldurun",
    ),
    "pleaseInputKey_openai": MessageLookupByLibrary.simpleMessage(
      "Lütfen anahtarı girin",
    ),
    "prompt_openai": MessageLookupByLibrary.simpleMessage("Komut İsteği"),
    "putKeyHere_openai": MessageLookupByLibrary.simpleMessage(
      "Anahtarını buraya yerleştir",
    ),
    "regenerateResponse_openai": MessageLookupByLibrary.simpleMessage(
      "Yanıtı yeniden oluştur",
    ),
    "remaining_openai": MessageLookupByLibrary.simpleMessage("Kalan"),
    "removeKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Anahtar Kaldırılamadı",
    ),
    "removeKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "Anahtar Başarıyla Kaldırıldı",
    ),
    "remove_openai": MessageLookupByLibrary.simpleMessage("Kaldır"),
    "resetSettings_openai": MessageLookupByLibrary.simpleMessage(
      "Ayarları sıfırla",
    ),
    "reset_openai": MessageLookupByLibrary.simpleMessage("Sıfırla"),
    "saveKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Anahtar Kaydedilemedi",
    ),
    "saveKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "Anahtar Başarıyla Kaydedildi",
    ),
    "saveKey_openai": MessageLookupByLibrary.simpleMessage("Anahtarı Kaydet"),
    "save_openai": MessageLookupByLibrary.simpleMessage("Kaydet"),
    "searchByPrompt_openai": MessageLookupByLibrary.simpleMessage(
      "Bilgi İstemine Göre Ara...",
    ),
    "sectionKeywords_openai": MessageLookupByLibrary.simpleMessage(
      "Bölüm Anahtar Kelimeleri",
    ),
    "sectionTopic_openai": MessageLookupByLibrary.simpleMessage("Bölüm Konusu"),
    "selectChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Sohbet Seçimi Başarısız",
    ),
    "selectPrompt_openai": MessageLookupByLibrary.simpleMessage("İstemi Seç"),
    "settings_openai": MessageLookupByLibrary.simpleMessage("Ayarlar"),
    "share_openai": MessageLookupByLibrary.simpleMessage("Paylaş"),
    "skills_openai": MessageLookupByLibrary.simpleMessage("Yetenekler"),
    "somethingWentWrong_openai": MessageLookupByLibrary.simpleMessage(
      "Bir şeyler yanlış gitti!!!",
    ),
    "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
      "Bir şeyler yanlış gitti! Lütfen daha sonra tekrar deneyin. Çok teşekkürler!",
    ),
    "speechNotAvailable_openai": MessageLookupByLibrary.simpleMessage(
      "Konuşma mevcut değil",
    ),
    "style_openai": MessageLookupByLibrary.simpleMessage("Stil"),
    "subscriptionExpiredDate_openai": m2,
    "tapTheMicToTalk_openai": MessageLookupByLibrary.simpleMessage(
      "Konuşmak için mikrofona dokunun",
    ),
    "textGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Metin oluştur",
    ),
    "textGenerator_openai": MessageLookupByLibrary.simpleMessage(
      "Metin Oluşturucu",
    ),
    "timeGenerate_openai": m3,
    "typeAMessage_openai": MessageLookupByLibrary.simpleMessage(
      "Bir mesaj yazın...",
    ),
    "viewType_openai": MessageLookupByLibrary.simpleMessage("Görünüm Türü"),
    "view_openai": MessageLookupByLibrary.simpleMessage("Görünüm"),
    "write_openai": MessageLookupByLibrary.simpleMessage("Yaz"),
  };
}
