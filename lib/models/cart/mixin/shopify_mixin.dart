import 'package:flux_extended/index.dart';
import 'package:flux_extended/native_payment/shopify/shopify_payment_impl.dart';

import '../../entities/index.dart';
import 'cart_mixin.dart';

mixin ShopifyMixin on CartMixin {
  CartDataShopify? _cartDataShopify;

  // ShopifyCustomerAccountService? _customerAccountService;

  Map<dynamic, dynamic> get checkoutCreatedInCart => {};

  CartDataShopify? get cartDataShopify => _cartDataShopify;

  double? getTax() {
    return _cartDataShopify?.cost.totalTaxAmount?.amount;
  }

  void setCartDataShopify(CartDataShopify? cartData) {
    _cartDataShopify = cartData;
  }

  @override
  String getCartId() {
    return cartDataShopify?.id ?? '';
  }

  ShopifyPayment createShopifyPayment({
    required void Function(ShopifyPaymentResult?) onPaymentSuccess,
    required void Function(String) onPaymentError,
    required void Function() onPaymentCancel,
  }) {
    return ShopifyPaymentImpl(
      cartData: cartDataShopify!,
      currencyCode: currencyCode!.toUpperCase(),
      onPaymentSuccess: onPaymentSuccess,
      onPaymentError: onPaymentError,
      onPaymentCancel: onPaymentCancel,
    );
  }
}
