class HaravanConstants {
  static const Map<String, String> headers = {
    'accept':
        'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language':
        'en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7,fr-FR;q=0.6,fr;q=0.5',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
  };

  static const Map<String, String> headersCheckout = {
    'sec-ch-ua':
        '"Google Chrome";v="123", "Not:A-Brand";v="8", "Chromium";v="123"',
    'sec-ch-ua-mobile': '?0',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'x-requested-with': 'XMLHttpRequest',
    'accept': 'application/json, text/javascript, */*; q=0.01',
    'accept-language': '',
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
  };

  static Map<String, String> getHeader(String? cookie,
          {Map<String, String>? newHeaders}) =>
      {
        if (newHeaders?.isNotEmpty ?? false) ...newHeaders! else ...headers,
        if (cookie?.isNotEmpty ?? false) 'cookie': cookie!,
      };
  static const keywordDetectToCheckoutSuccess = [
    'Mã đơn hàng #',
    'Đơn hàng #',
    'Thanh toán đơn hàng',
    'Thanh toán đang chờ xử lý',
  ];
}
