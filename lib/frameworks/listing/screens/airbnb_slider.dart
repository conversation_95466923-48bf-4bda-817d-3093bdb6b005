import 'dart:async';

import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../../common/config.dart';
import '../../../common/extensions/extensions.dart';
import 'airbnb_slider_item.dart';

const _kTimerDuration = Duration(seconds: 7);
const _kAnimationToPageDuration = Duration(milliseconds: 200);

class AirbnbSlider extends StatefulWidget {
  final List<String>? images;
  final double? height;

  const AirbnbSlider({this.images, this.height});

  @override
  State<AirbnbSlider> createState() => _StateAirbnbSlider();
}

class _StateAirbnbSlider extends State<AirbnbSlider> {
  final _controller = PageController();
  int position = 0;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    if (kProductDetail.autoPlayGallery) {
      _startAutoPlay();
    }
  }

  void _startAutoPlay() {
    _timer = Timer.periodic(_kTimerDuration, (_) {
      if (!mounted) return;

      final nextPos = position + 1;
      final maxLength = widget.images?.length ?? 0;

      if (nextPos >= maxLength) {
        _controller.jumpToPage(0);
      } else {
        _controller.animateToPage(
          nextPos,
          duration: _kAnimationToPageDuration,
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final images = widget.images ?? [];

    if (images.isEmpty) {
      return const SizedBox();
    }

    return Stack(
      children: [
        PageView.builder(
          controller: _controller,
          itemCount: images.length,
          onPageChanged: (index) {
            if (mounted) {
              setState(() {
                position = index;
              });
            }
          },
          itemBuilder: (context, index) {
            return AirbnbSliderItem(
              imageUrl: images[index],
              height: widget.height,
              onImageTap: () => context.openImageGallery(
                isDialog: false,
                images: images,
                index: index,
                heroTagPrefix: 'airbnb_slider_item_hero_tag',
              ),
            );
          },
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: SmoothPageIndicator(
            controller: _controller,
            count: images.length,
            effect: const SlideEffect(
              spacing: 8.0,
              radius: 5.0,
              dotWidth: 24.0,
              dotHeight: 2.0,
              paintStyle: PaintingStyle.fill,
              strokeWidth: 1.5,
              dotColor: Colors.black12,
              activeDotColor: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }
}
