import 'package:flutter/material.dart';

/// BuildContext extension methods
extension ContextExtensions on BuildContext {
  /// Get theme data
  ThemeData get theme => Theme.of(this);
  
  /// Get color scheme
  ColorScheme get colorScheme => theme.colorScheme;
  
  /// Get text theme
  TextTheme get textTheme => theme.textTheme;
  
  /// Get media query data
  MediaQueryData get mediaQuery => MediaQuery.of(this);
  
  /// Get screen size
  Size get screenSize => mediaQuery.size;
  
  /// Get screen width
  double get screenWidth => screenSize.width;
  
  /// Get screen height
  double get screenHeight => screenSize.height;
  
  /// Get safe area padding
  EdgeInsets get padding => mediaQuery.padding;
  
  /// Get view insets (keyboard height, etc.)
  EdgeInsets get viewInsets => mediaQuery.viewInsets;
  
  /// Check if keyboard is visible
  bool get isKeyboardVisible => viewInsets.bottom > 0;
  
  /// Get keyboard height
  double get keyboardHeight => viewInsets.bottom;
  
  /// Get status bar height
  double get statusBarHeight => padding.top;
  
  /// Get bottom safe area height
  double get bottomSafeArea => padding.bottom;
  
  /// Check if device is in landscape mode
  bool get isLandscape => mediaQuery.orientation == Orientation.landscape;
  
  /// Check if device is in portrait mode
  bool get isPortrait => mediaQuery.orientation == Orientation.portrait;
  
  /// Get device pixel ratio
  double get devicePixelRatio => mediaQuery.devicePixelRatio;
  
  /// Check if device is tablet (width > 600)
  bool get isTablet => screenWidth > 600;
  
  /// Check if device is mobile
  bool get isMobile => screenWidth <= 600;
  
  /// Check if device is desktop (width > 1200)
  bool get isDesktop => screenWidth > 1200;
  
  /// Get responsive breakpoint
  ResponsiveBreakpoint get breakpoint {
    if (screenWidth <= 600) return ResponsiveBreakpoint.mobile;
    if (screenWidth <= 1200) return ResponsiveBreakpoint.tablet;
    return ResponsiveBreakpoint.desktop;
  }
  
  /// Show snackbar
  void showSnackBar(
    String message, {
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
    Color? backgroundColor,
    Color? textColor,
  }) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(color: textColor),
        ),
        duration: duration,
        action: action,
        backgroundColor: backgroundColor,
      ),
    );
  }
  
  /// Show error snackbar
  void showErrorSnackBar(String message) {
    showSnackBar(
      message,
      backgroundColor: colorScheme.error,
      textColor: colorScheme.onError,
    );
  }
  
  /// Show success snackbar
  void showSuccessSnackBar(String message) {
    showSnackBar(
      message,
      backgroundColor: Colors.green,
      textColor: Colors.white,
    );
  }
  
  /// Show warning snackbar
  void showWarningSnackBar(String message) {
    showSnackBar(
      message,
      backgroundColor: Colors.orange,
      textColor: Colors.white,
    );
  }
  
  /// Show loading dialog
  void showLoadingDialog({String? message}) {
    showDialog<void>(
      context: this,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(message ?? 'Loading...'),
          ],
        ),
      ),
    );
  }
  
  /// Hide loading dialog
  void hideLoadingDialog() {
    Navigator.of(this).pop();
  }
  
  /// Show confirmation dialog
  Future<bool?> showConfirmationDialog({
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
  }) {
    return showDialog<bool>(
      context: this,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }
  
  /// Show error dialog
  Future<void> showErrorDialog({
    required String title,
    required String message,
    String buttonText = 'OK',
  }) {
    return showDialog<void>(
      context: this,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(buttonText),
          ),
        ],
      ),
    );
  }
  
  /// Navigate to route
  Future<T?> pushNamed<T>(String routeName, {Object? arguments}) {
    return Navigator.of(this).pushNamed<T>(routeName, arguments: arguments);
  }
  
  /// Navigate and replace current route
  Future<T?> pushReplacementNamed<T>(String routeName, {Object? arguments}) {
    return Navigator.of(this).pushReplacementNamed<T>(routeName, arguments: arguments);
  }
  
  /// Navigate and clear stack
  Future<T?> pushNamedAndClearStack<T>(String routeName, {Object? arguments}) {
    return Navigator.of(this).pushNamedAndRemoveUntil<T>(
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }
  
  /// Pop current route
  void pop<T>([T? result]) {
    Navigator.of(this).pop<T>(result);
  }
  
  /// Check if can pop
  bool get canPop => Navigator.of(this).canPop();
  
  /// Focus next field
  void nextFocus() {
    FocusScope.of(this).nextFocus();
  }
  
  /// Unfocus current field
  void unfocus() {
    FocusScope.of(this).unfocus();
  }
  
  /// Get localization
  // Localizations get l10n => Localizations.of(this);
  
  /// Get locale
  Locale get locale => Localizations.localeOf(this);
  
  /// Check if RTL
  bool get isRTL => Directionality.of(this) == TextDirection.rtl;
  
  /// Check if LTR
  bool get isLTR => Directionality.of(this) == TextDirection.ltr;
}

/// Responsive breakpoints
enum ResponsiveBreakpoint {
  mobile,
  tablet,
  desktop,
}

/// Extension for responsive breakpoints
extension ResponsiveBreakpointExtensions on ResponsiveBreakpoint {
  bool get isMobile => this == ResponsiveBreakpoint.mobile;
  bool get isTablet => this == ResponsiveBreakpoint.tablet;
  bool get isDesktop => this == ResponsiveBreakpoint.desktop;
}
