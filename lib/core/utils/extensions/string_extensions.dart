import '../helpers/validation_helper.dart';

/// String extension methods
extension StringExtensions on String {
  /// Check if string is null or empty
  bool get isNullOrEmpty => isEmpty;
  
  /// Check if string is not null and not empty
  bool get isNotNullOrEmpty => isNotEmpty;
  
  /// Capitalize first letter
  String get capitalize {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1).toLowerCase()}';
  }
  
  /// Capitalize each word
  String get capitalizeWords {
    if (isEmpty) return this;
    return split(' ')
        .map((word) => word.capitalize)
        .join(' ');
  }
  
  /// Remove all whitespace
  String get removeWhitespace => replaceAll(RegExp(r'\s+'), '');
  
  /// Check if string is a valid email
  bool get isValidEmail => ValidationHelper.isValidEmail(this);
  
  /// Check if string is a valid phone number
  bool get isValidPhone => ValidationHelper.isValidPhone(this);
  
  /// Check if string is a valid password
  bool get isValidPassword => ValidationHelper.isValidPassword(this);
  
  /// Check if string is numeric
  bool get isNumeric => double.tryParse(this) != null;
  
  /// Convert string to double safely
  double? get toDouble => double.tryParse(this);
  
  /// Convert string to int safely
  int? get toInt => int.tryParse(this);
  
  /// Truncate string to specified length
  String truncate(int length, {String suffix = '...'}) {
    if (this.length <= length) return this;
    return '${substring(0, length)}$suffix';
  }
  
  /// Remove HTML tags
  String get removeHtmlTags => replaceAll(RegExp(r'<[^>]*>'), '');
  
  /// Convert to snake_case
  String get toSnakeCase {
    return replaceAllMapped(
      RegExp(r'[A-Z]'),
      (match) => '_${match.group(0)!.toLowerCase()}',
    ).replaceFirst(RegExp(r'^_'), '');
  }
  
  /// Convert to camelCase
  String get toCamelCase {
    final words = split('_');
    if (words.isEmpty) return this;
    
    final first = words.first.toLowerCase();
    final rest = words.skip(1).map((word) => word.capitalize);
    
    return [first, ...rest].join();
  }
  
  /// Convert to PascalCase
  String get toPascalCase {
    return split('_').map((word) => word.capitalize).join();
  }
  
  /// Check if string contains only alphabetic characters
  bool get isAlphabetic => RegExp(r'^[a-zA-Z]+$').hasMatch(this);
  
  /// Check if string contains only alphanumeric characters
  bool get isAlphanumeric => RegExp(r'^[a-zA-Z0-9]+$').hasMatch(this);
  
  /// Get initials from name
  String get initials {
    final words = trim().split(RegExp(r'\s+'));
    if (words.isEmpty) return '';
    
    return words
        .take(2)
        .map((word) => word.isNotEmpty ? word[0].toUpperCase() : '')
        .join();
  }
  
  /// Mask string (useful for sensitive data)
  String mask({int visibleStart = 2, int visibleEnd = 2, String maskChar = '*'}) {
    if (length <= visibleStart + visibleEnd) return this;
    
    final start = substring(0, visibleStart);
    final end = substring(length - visibleEnd);
    final middle = maskChar * (length - visibleStart - visibleEnd);
    
    return '$start$middle$end';
  }
  
  /// Format as currency
  String formatAsCurrency({String symbol = '\$', int decimalPlaces = 2}) {
    final number = toDouble;
    if (number == null) return this;
    
    return '$symbol${number.toStringAsFixed(decimalPlaces)}';
  }
  
  /// Parse JSON safely
  Map<String, dynamic>? get parseJson {
    try {
      // This would require dart:convert import
      // return json.decode(this) as Map<String, dynamic>;
      return null; // Placeholder
    } catch (e) {
      return null;
    }
  }
  
  /// Check if string is a valid URL
  bool get isValidUrl {
    try {
      final uri = Uri.parse(this);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }
  
  /// Extract domain from URL
  String? get domain {
    try {
      final uri = Uri.parse(this);
      return uri.host;
    } catch (e) {
      return null;
    }
  }
  
  /// Count words in string
  int get wordCount => trim().split(RegExp(r'\s+')).length;
  
  /// Reverse string
  String get reverse => split('').reversed.join();
  
  /// Check if string is palindrome
  bool get isPalindrome {
    final cleaned = toLowerCase().removeWhitespace;
    return cleaned == cleaned.reverse;
  }
  
  /// Generate slug from string
  String get slug {
    return toLowerCase()
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'\s+'), '-')
        .replaceAll(RegExp(r'-+'), '-')
        .replaceAll(RegExp(r'^-|-$'), '');
  }
}
