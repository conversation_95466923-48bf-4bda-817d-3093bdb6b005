import '../../../core/constants/app_constants.dart';

/// Helper class for validation operations
class ValidationHelper {
  /// Validate email address
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    return RegExp(AppConstants.emailRegex).hasMatch(email);
  }
  
  /// Validate phone number
  static bool isValidPhone(String phone) {
    if (phone.isEmpty) return false;
    return RegExp(AppConstants.phoneRegex).hasMatch(phone);
  }
  
  /// Validate password
  static bool isValidPassword(String password) {
    if (password.isEmpty) return false;
    if (password.length < AppConstants.minPasswordLength) return false;
    if (password.length > AppConstants.maxPasswordLength) return false;
    return RegExp(AppConstants.passwordRegex).hasMatch(password);
  }
  
  /// Validate username
  static bool isValidUsername(String username) {
    if (username.isEmpty) return false;
    if (username.length < AppConstants.minUsernameLength) return false;
    if (username.length > AppConstants.maxUsernameLength) return false;
    return RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(username);
  }
  
  /// Validate required field
  static String? validateRequired(String? value, {String fieldName = 'Field'}) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }
  
  /// Validate email field
  static String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }
    if (!isValidEmail(value.trim())) {
      return 'Please enter a valid email address';
    }
    return null;
  }
  
  /// Validate password field
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < AppConstants.minPasswordLength) {
      return 'Password must be at least ${AppConstants.minPasswordLength} characters';
    }
    if (value.length > AppConstants.maxPasswordLength) {
      return 'Password must be less than ${AppConstants.maxPasswordLength} characters';
    }
    if (!RegExp(r'(?=.*[a-z])').hasMatch(value)) {
      return 'Password must contain at least one lowercase letter';
    }
    if (!RegExp(r'(?=.*[A-Z])').hasMatch(value)) {
      return 'Password must contain at least one uppercase letter';
    }
    if (!RegExp(r'(?=.*\d)').hasMatch(value)) {
      return 'Password must contain at least one number';
    }
    return null;
  }
  
  /// Validate confirm password field
  static String? validateConfirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != password) {
      return 'Passwords do not match';
    }
    return null;
  }
  
  /// Validate phone field
  static String? validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number is required';
    }
    if (!isValidPhone(value.trim())) {
      return 'Please enter a valid phone number';
    }
    return null;
  }
  
  /// Validate username field
  static String? validateUsername(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Username is required';
    }
    if (!isValidUsername(value.trim())) {
      return 'Username can only contain letters, numbers, and underscores';
    }
    return null;
  }
  
  /// Validate name field
  static String? validateName(String? value, {String fieldName = 'Name'}) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    if (value.trim().length < 2) {
      return '$fieldName must be at least 2 characters';
    }
    if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(value.trim())) {
      return '$fieldName can only contain letters and spaces';
    }
    return null;
  }
  
  /// Validate numeric field
  static String? validateNumeric(String? value, {String fieldName = 'Field'}) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    if (double.tryParse(value.trim()) == null) {
      return '$fieldName must be a valid number';
    }
    return null;
  }
  
  /// Validate positive number
  static String? validatePositiveNumber(String? value, {String fieldName = 'Field'}) {
    final numericValidation = validateNumeric(value, fieldName: fieldName);
    if (numericValidation != null) return numericValidation;
    
    final number = double.parse(value!.trim());
    if (number <= 0) {
      return '$fieldName must be greater than 0';
    }
    return null;
  }
  
  /// Validate URL
  static String? validateUrl(String? value, {String fieldName = 'URL'}) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    
    try {
      final uri = Uri.parse(value.trim());
      if (!uri.hasScheme || (uri.scheme != 'http' && uri.scheme != 'https')) {
        return 'Please enter a valid URL';
      }
    } catch (e) {
      return 'Please enter a valid URL';
    }
    
    return null;
  }
  
  /// Validate date
  static String? validateDate(String? value, {String fieldName = 'Date'}) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    
    try {
      DateTime.parse(value.trim());
    } catch (e) {
      return 'Please enter a valid date';
    }
    
    return null;
  }
  
  /// Validate future date
  static String? validateFutureDate(String? value, {String fieldName = 'Date'}) {
    final dateValidation = validateDate(value, fieldName: fieldName);
    if (dateValidation != null) return dateValidation;
    
    final date = DateTime.parse(value!.trim());
    if (date.isBefore(DateTime.now())) {
      return '$fieldName must be in the future';
    }
    
    return null;
  }
  
  /// Validate past date
  static String? validatePastDate(String? value, {String fieldName = 'Date'}) {
    final dateValidation = validateDate(value, fieldName: fieldName);
    if (dateValidation != null) return dateValidation;
    
    final date = DateTime.parse(value!.trim());
    if (date.isAfter(DateTime.now())) {
      return '$fieldName must be in the past';
    }
    
    return null;
  }
  
  /// Validate minimum length
  static String? validateMinLength(String? value, int minLength, {String fieldName = 'Field'}) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    if (value.length < minLength) {
      return '$fieldName must be at least $minLength characters';
    }
    return null;
  }
  
  /// Validate maximum length
  static String? validateMaxLength(String? value, int maxLength, {String fieldName = 'Field'}) {
    if (value != null && value.length > maxLength) {
      return '$fieldName must be less than $maxLength characters';
    }
    return null;
  }
  
  /// Validate range
  static String? validateRange(String? value, double min, double max, {String fieldName = 'Field'}) {
    final numericValidation = validateNumeric(value, fieldName: fieldName);
    if (numericValidation != null) return numericValidation;
    
    final number = double.parse(value!.trim());
    if (number < min || number > max) {
      return '$fieldName must be between $min and $max';
    }
    
    return null;
  }
  
  /// Validate credit card number (basic Luhn algorithm)
  static bool isValidCreditCard(String cardNumber) {
    final cleanNumber = cardNumber.replaceAll(RegExp(r'\D'), '');
    if (cleanNumber.length < 13 || cleanNumber.length > 19) return false;
    
    var sum = 0;
    var alternate = false;
    
    for (var i = cleanNumber.length - 1; i >= 0; i--) {
      var digit = int.parse(cleanNumber[i]);
      
      if (alternate) {
        digit *= 2;
        if (digit > 9) digit = (digit % 10) + 1;
      }
      
      sum += digit;
      alternate = !alternate;
    }
    
    return sum % 10 == 0;
  }
  
  /// Validate credit card field
  static String? validateCreditCard(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Credit card number is required';
    }
    if (!isValidCreditCard(value.trim())) {
      return 'Please enter a valid credit card number';
    }
    return null;
  }
}
