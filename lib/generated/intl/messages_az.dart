// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a az locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'az';

  static String m0(limit) =>
      "Pulsuz versiyada yalnız ${limit}x şəkil axtarışı var.";

  static String m1(limit) =>
      " ${limit} -ə qədər mesaj yalnız pulsuz versiyada göstərilə bilər.";

  static String m2(date) => "Abunəliyin bitmə tarixi ${date}";

  static String m3(number) =>
      "Yaradın (${number} ${Intl.plural(number, one: 'time', other: 'times')})";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about_openai": MessageLookupByLibrary.simpleMessage("haqqında"),
        "apply_openai": MessageLookupByLibrary.simpleMessage("Tətbiq edin"),
        "artist_openai": MessageLookupByLibrary.simpleMessage("Rəssam"),
        "cancel_openai": MessageLookupByLibrary.simpleMessage("Ləğv et"),
        "chatDetail_openai":
            MessageLookupByLibrary.simpleMessage("Söhbət Təfərrüatı"),
        "chatGPT_openai": MessageLookupByLibrary.simpleMessage("GPT söhbəti"),
        "chatWithBot_openai":
            MessageLookupByLibrary.simpleMessage("Bot ilə söhbət edin"),
        "chat_openai": MessageLookupByLibrary.simpleMessage("Çat"),
        "chooseArtist_openai":
            MessageLookupByLibrary.simpleMessage("Şəkiliniz üçün rəssam seçin"),
        "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
            "Şəkiliniz üçün təfərrüat seçin"),
        "chooseMedium_openai":
            MessageLookupByLibrary.simpleMessage("Şəkiliniz üçün orta seçin"),
        "chooseMood_openai":
            MessageLookupByLibrary.simpleMessage("Şəkiliniz üçün əhval seçin"),
        "chooseUseCase_openai":
            MessageLookupByLibrary.simpleMessage("İstifadə vəziyyətini seçin"),
        "choseStyle_openai":
            MessageLookupByLibrary.simpleMessage("Şəkiliniz üçün üslub seçin"),
        "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
            "Məzmunu təmizləyəcəyinizə əminsiniz?"),
        "clearContent_openai":
            MessageLookupByLibrary.simpleMessage("Tərkibini təmizləyin"),
        "clearConversation_openai":
            MessageLookupByLibrary.simpleMessage("Söhbəti təmizləyin"),
        "clear_openai": MessageLookupByLibrary.simpleMessage("Aydındır"),
        "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
            "Bu elementi silmək istədiyinizə əminsiniz?"),
        "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
            "Bu elementin silinməsinə davam etmək istədiyinizi təsdiqləyin. Bu əməliyyatı geri qaytara bilməzsiniz."),
        "confirmRemoveKey_openai": MessageLookupByLibrary.simpleMessage(
            "Açarı siləcəyinizə əminsiniz?"),
        "confirm_openai": MessageLookupByLibrary.simpleMessage("Təsdiq edin"),
        "copiedToClipboard_openai":
            MessageLookupByLibrary.simpleMessage("Məzmun buferə kopyalandı"),
        "copy_openai": MessageLookupByLibrary.simpleMessage("Surəti"),
        "createChatFailed_openai":
            MessageLookupByLibrary.simpleMessage("Söhbət Yaradılmadı"),
        "deleteFailed_openai":
            MessageLookupByLibrary.simpleMessage("Silmək alınmadı"),
        "delete_openai": MessageLookupByLibrary.simpleMessage("Sil"),
        "detail_openai": MessageLookupByLibrary.simpleMessage("Detal"),
        "download_openai": MessageLookupByLibrary.simpleMessage("Yükləyin"),
        "edit_openai": MessageLookupByLibrary.simpleMessage("Redaktə edin"),
        "failedToGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Yaratmaq alınmadı"),
        "generate_openai": MessageLookupByLibrary.simpleMessage("Yaratmaq"),
        "grid_openai": MessageLookupByLibrary.simpleMessage("Grid"),
        "imageGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Şəkil yaratmaq"),
        "imageSize_openai":
            MessageLookupByLibrary.simpleMessage("Şəkil ölçüsü"),
        "inputKey_openai": MessageLookupByLibrary.simpleMessage("Giriş Açarı"),
        "interest_openai": MessageLookupByLibrary.simpleMessage("Maraq"),
        "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
            "API Açarınız yerli olaraq mobil telefonunuzda saxlanılır və heç vaxt başqa yerə göndərilmir. Daha sonra istifadə etmək üçün açarınızı saxlaya bilərsiniz. Bundan sonra istifadə etmək istəmirsinizsə, açarınızı da silə bilərsiniz."),
        "invalidKey_openai":
            MessageLookupByLibrary.simpleMessage("Yanlış Açar"),
        "jobRole_openai": MessageLookupByLibrary.simpleMessage("İş Rolu"),
        "jobSkills_openai":
            MessageLookupByLibrary.simpleMessage("İş Bacarıqları"),
        "layoutStyle_openai":
            MessageLookupByLibrary.simpleMessage("Layout Style"),
        "limitImage_openai": m0,
        "limitTheText_openai": m1,
        "listening_openai": MessageLookupByLibrary.simpleMessage("Dinlənir..."),
        "loadKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("Açarı yükləmək alınmadı"),
        "loadKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("Açar Uğurlu Yükləyin"),
        "manage_openai": MessageLookupByLibrary.simpleMessage("İdarə et"),
        "medium_openai": MessageLookupByLibrary.simpleMessage("Orta"),
        "mood_openai": MessageLookupByLibrary.simpleMessage("Əhval-ruhiyyə"),
        "moreOptions_openai":
            MessageLookupByLibrary.simpleMessage("Daha çox seçim"),
        "newChat_openai": MessageLookupByLibrary.simpleMessage("Yeni Söhbət"),
        "noImageGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Şəkil yaratmır"),
        "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
            "Yaradılan şəkillərin sayı. 1 ilə 10 arasında olmalıdır."),
        "numberOfImages_openai":
            MessageLookupByLibrary.simpleMessage("Şəkillərin sayı"),
        "options_openai": MessageLookupByLibrary.simpleMessage("Seçimlər"),
        "page_openai": MessageLookupByLibrary.simpleMessage("Səhifə"),
        "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
            "Bağlantınızı yoxlayın və yenidən cəhd edin!"),
        "pleaseInputFillAllFields_openai": MessageLookupByLibrary.simpleMessage(
            "Zəhmət olmasa bütün sahələri doldurun"),
        "pleaseInputKey_openai": MessageLookupByLibrary.simpleMessage(
            "Zəhmət olmasa açarı daxil edin"),
        "prompt_openai": MessageLookupByLibrary.simpleMessage("Tələb"),
        "putKeyHere_openai":
            MessageLookupByLibrary.simpleMessage("Açarı bura qoyun"),
        "regenerateResponse_openai":
            MessageLookupByLibrary.simpleMessage("Cavabın bərpası"),
        "remaining_openai": MessageLookupByLibrary.simpleMessage("Qalan"),
        "removeKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("Açarı silmək uğursuz oldu"),
        "removeKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("Açar Uğurla Silindi"),
        "remove_openai": MessageLookupByLibrary.simpleMessage("Sil"),
        "resetSettings_openai":
            MessageLookupByLibrary.simpleMessage("Parametrləri sıfırlayın"),
        "reset_openai": MessageLookupByLibrary.simpleMessage("Sıfırlayın"),
        "saveKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("Açarı Saxlama Başa çatmadı"),
        "saveKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("Açar Uğurla Saxlanıldı"),
        "saveKey_openai": MessageLookupByLibrary.simpleMessage("Açarı Saxla"),
        "save_openai": MessageLookupByLibrary.simpleMessage("Yadda saxla"),
        "searchByPrompt_openai":
            MessageLookupByLibrary.simpleMessage("Sorğu ilə Axtar..."),
        "sectionKeywords_openai":
            MessageLookupByLibrary.simpleMessage("Bölmə Açar Sözləri"),
        "sectionTopic_openai":
            MessageLookupByLibrary.simpleMessage("Bölmə Mövzu"),
        "selectChatFailed_openai":
            MessageLookupByLibrary.simpleMessage("Söhbət uğursuz oldu"),
        "selectPrompt_openai":
            MessageLookupByLibrary.simpleMessage("Sorğu seçin"),
        "settings_openai": MessageLookupByLibrary.simpleMessage("Ayarlar"),
        "share_openai": MessageLookupByLibrary.simpleMessage("Paylaşın"),
        "skills_openai": MessageLookupByLibrary.simpleMessage("bacarıqlar"),
        "somethingWentWrong_openai":
            MessageLookupByLibrary.simpleMessage("Nəsə xəta baş verdi!!!"),
        "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
            "Nəsə xəta baş verdi! Zəhmət olmasa bir az sonra yenə cəhd edin. Çox sağ ol!"),
        "speechNotAvailable_openai":
            MessageLookupByLibrary.simpleMessage("Nitq mövcud deyil"),
        "style_openai": MessageLookupByLibrary.simpleMessage("Üslub"),
        "subscriptionExpiredDate_openai": m2,
        "tapTheMicToTalk_openai": MessageLookupByLibrary.simpleMessage(
            "Danışmaq üçün mikrofona toxunun"),
        "textGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Mətn yaratmaq"),
        "textGenerator_openai":
            MessageLookupByLibrary.simpleMessage("Mətn generatoru"),
        "timeGenerate_openai": m3,
        "typeAMessage_openai":
            MessageLookupByLibrary.simpleMessage("Mesaj yazın..."),
        "viewType_openai": MessageLookupByLibrary.simpleMessage("Görünüş növü"),
        "view_openai": MessageLookupByLibrary.simpleMessage("Bax"),
        "write_openai": MessageLookupByLibrary.simpleMessage("yaz")
      };
}
