// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a fa locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'fa';

  static String m0(limit) =>
      "فقط ${limit}x جستجوی تصویر در نسخه رایگان وجود دارد.";

  static String m1(limit) =>
      "حداکثر تا ${limit} پیام فقط در نسخه رایگان قابل نمایش است.";

  static String m2(date) => "تاریخ انقضای اشتراک ${date}";

  static String m3(number) =>
      "ایجاد (${number} ${Intl.plural(number, one: 'time', other: 'times')})";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about_openai": MessageLookupByLibrary.simpleMessage("در باره"),
        "apply_openai": MessageLookupByLibrary.simpleMessage("درخواست دادن"),
        "artist_openai": MessageLookupByLibrary.simpleMessage("هنرمند"),
        "cancel_openai": MessageLookupByLibrary.simpleMessage("لغو"),
        "chatDetail_openai": MessageLookupByLibrary.simpleMessage("جزئیات چت"),
        "chatGPT_openai": MessageLookupByLibrary.simpleMessage("GPT چت"),
        "chatWithBot_openai":
            MessageLookupByLibrary.simpleMessage("چت با ربات"),
        "chat_openai": MessageLookupByLibrary.simpleMessage("چت"),
        "chooseArtist_openai": MessageLookupByLibrary.simpleMessage(
            "هنرمند را برای تصویر خود انتخاب کنید"),
        "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
            "جزئیات را برای تصویر خود انتخاب کنید"),
        "chooseMedium_openai": MessageLookupByLibrary.simpleMessage(
            "رسانه ای را برای تصویر خود انتخاب کنید"),
        "chooseMood_openai": MessageLookupByLibrary.simpleMessage(
            "حال و هوای تصویر خود را انتخاب کنید"),
        "chooseUseCase_openai":
            MessageLookupByLibrary.simpleMessage("مورد استفاده را انتخاب کنید"),
        "choseStyle_openai": MessageLookupByLibrary.simpleMessage(
            "سبکی را برای تصویر خود انتخاب کنید"),
        "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
            "آیا مطمئن هستید که محتوا را پاک می کنید؟"),
        "clearContent_openai":
            MessageLookupByLibrary.simpleMessage("محتوا را پاک کنید"),
        "clearConversation_openai":
            MessageLookupByLibrary.simpleMessage("مکالمه را پاک کنید"),
        "clear_openai": MessageLookupByLibrary.simpleMessage("پاک کردن"),
        "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
            "آیا مطمئن هستید که می خواهید این مورد را حذف کنید؟"),
        "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
            "لطفاً در صورت تمایل به حذف این مورد تأیید کنید. شما نمی توانید این عمل را واگرد کنید."),
        "confirmRemoveKey_openai": MessageLookupByLibrary.simpleMessage(
            "آیا مطمئن هستید که کلید را حذف می کنید؟"),
        "confirm_openai": MessageLookupByLibrary.simpleMessage("تایید"),
        "copiedToClipboard_openai":
            MessageLookupByLibrary.simpleMessage("محتوای کپی شده در کلیپ بورد"),
        "copy_openai": MessageLookupByLibrary.simpleMessage("کپی ?"),
        "createChatFailed_openai":
            MessageLookupByLibrary.simpleMessage("ایجاد چت ناموفق بود"),
        "deleteFailed_openai": MessageLookupByLibrary.simpleMessage("حذف نشد"),
        "delete_openai": MessageLookupByLibrary.simpleMessage("حذف"),
        "detail_openai": MessageLookupByLibrary.simpleMessage("جزئیات"),
        "download_openai": MessageLookupByLibrary.simpleMessage("دانلود"),
        "edit_openai": MessageLookupByLibrary.simpleMessage("ویرایش کنید"),
        "failedToGenerate_openai":
            MessageLookupByLibrary.simpleMessage("تولید نشد"),
        "generate_openai": MessageLookupByLibrary.simpleMessage("ایجادکردن"),
        "grid_openai": MessageLookupByLibrary.simpleMessage("توری"),
        "imageGenerate_openai":
            MessageLookupByLibrary.simpleMessage("تولید تصویر"),
        "imageSize_openai":
            MessageLookupByLibrary.simpleMessage("اندازه تصویر"),
        "inputKey_openai": MessageLookupByLibrary.simpleMessage("کلید ورودی"),
        "interest_openai": MessageLookupByLibrary.simpleMessage("علاقه"),
        "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
            "کلید API شما به صورت محلی در تلفن همراه شما ذخیره می شود و هرگز به جای دیگری ارسال نمی شود. می توانید کلید خود را ذخیره کنید تا بعداً از آن استفاده کنید. همچنین اگر دیگر نمی خواهید از آن استفاده کنید، می توانید کلید خود را بردارید."),
        "invalidKey_openai":
            MessageLookupByLibrary.simpleMessage("کلید نامعتبر"),
        "jobRole_openai": MessageLookupByLibrary.simpleMessage("نقش کار"),
        "jobSkills_openai":
            MessageLookupByLibrary.simpleMessage("مهارت های شغلی"),
        "layoutStyle_openai":
            MessageLookupByLibrary.simpleMessage("سبک چیدمان"),
        "limitImage_openai": m0,
        "limitTheText_openai": m1,
        "listening_openai": MessageLookupByLibrary.simpleMessage("استماع..."),
        "loadKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("کلید بارگیری انجام نشد"),
        "loadKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("بارگیری کلید موفقیت آمیز"),
        "manage_openai": MessageLookupByLibrary.simpleMessage("مدیریت کردن"),
        "medium_openai": MessageLookupByLibrary.simpleMessage("متوسط"),
        "mood_openai": MessageLookupByLibrary.simpleMessage("حالت"),
        "moreOptions_openai":
            MessageLookupByLibrary.simpleMessage("گزینه های بیشتر"),
        "newChat_openai": MessageLookupByLibrary.simpleMessage("چت جدید"),
        "noImageGenerate_openai":
            MessageLookupByLibrary.simpleMessage("بدون تولید تصویر"),
        "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
            "تعداد تصاویر برای تولید باید بین 1 تا 10 باشد."),
        "numberOfImages_openai":
            MessageLookupByLibrary.simpleMessage("تعداد تصاویر"),
        "options_openai": MessageLookupByLibrary.simpleMessage("گزینه ها"),
        "page_openai": MessageLookupByLibrary.simpleMessage("صفحه"),
        "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
            "لطفاً اتصال خود را بررسی کنید و دوباره امتحان کنید!"),
        "pleaseInputFillAllFields_openai":
            MessageLookupByLibrary.simpleMessage("لطفا همه موارد را پر کنید"),
        "pleaseInputKey_openai": MessageLookupByLibrary.simpleMessage(
            "لطفا کلید ورودی را وارد کنید"),
        "prompt_openai": MessageLookupByLibrary.simpleMessage("سریع"),
        "putKeyHere_openai":
            MessageLookupByLibrary.simpleMessage("کلیدت را اینجا بگذار"),
        "regenerateResponse_openai":
            MessageLookupByLibrary.simpleMessage("پاسخ را بازسازی کنید"),
        "remaining_openai":
            MessageLookupByLibrary.simpleMessage("باقی مانده است"),
        "removeKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("حذف کلید انجام نشد"),
        "removeKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("کلید با موفقیت حذف شد"),
        "remove_openai": MessageLookupByLibrary.simpleMessage("برداشتن"),
        "resetSettings_openai":
            MessageLookupByLibrary.simpleMessage("تنظیمات را بازنشانی کنید"),
        "reset_openai": MessageLookupByLibrary.simpleMessage("تنظیم مجدد"),
        "saveKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("ذخیره کلید ناموفق بود"),
        "saveKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("کلید با موفقیت ذخیره شد"),
        "saveKey_openai": MessageLookupByLibrary.simpleMessage("ذخیره کلید"),
        "save_openai": MessageLookupByLibrary.simpleMessage("صرفه جویی"),
        "searchByPrompt_openai":
            MessageLookupByLibrary.simpleMessage("جستجو بر اساس درخواست..."),
        "sectionKeywords_openai":
            MessageLookupByLibrary.simpleMessage("بخش کلمات کلیدی"),
        "sectionTopic_openai":
            MessageLookupByLibrary.simpleMessage("موضوع بخش"),
        "selectChatFailed_openai":
            MessageLookupByLibrary.simpleMessage("Chat Failed را انتخاب کنید"),
        "selectPrompt_openai":
            MessageLookupByLibrary.simpleMessage("Prompt را انتخاب کنید"),
        "settings_openai": MessageLookupByLibrary.simpleMessage("تنظیمات"),
        "share_openai": MessageLookupByLibrary.simpleMessage("اشتراک گذاری"),
        "skills_openai": MessageLookupByLibrary.simpleMessage("مهارت ها"),
        "somethingWentWrong_openai":
            MessageLookupByLibrary.simpleMessage("مشکلی پیش آمد!!!"),
        "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
            "مشکلی پیش آمد! لطفاً بعداً دوباره امتحان کنید. خیلی ممنونم!"),
        "speechNotAvailable_openai":
            MessageLookupByLibrary.simpleMessage("گفتار در دسترس نیست"),
        "style_openai": MessageLookupByLibrary.simpleMessage("سبک"),
        "subscriptionExpiredDate_openai": m2,
        "tapTheMicToTalk_openai": MessageLookupByLibrary.simpleMessage(
            "برای صحبت کردن، روی میکروفون ضربه بزنید"),
        "textGenerate_openai":
            MessageLookupByLibrary.simpleMessage("تولید متن"),
        "textGenerator_openai":
            MessageLookupByLibrary.simpleMessage("تولید کننده متن"),
        "timeGenerate_openai": m3,
        "typeAMessage_openai":
            MessageLookupByLibrary.simpleMessage("ارسال پیام ..."),
        "viewType_openai": MessageLookupByLibrary.simpleMessage("نوع مشاهده"),
        "view_openai": MessageLookupByLibrary.simpleMessage("چشم انداز"),
        "write_openai": MessageLookupByLibrary.simpleMessage("نوشتن")
      };
}
