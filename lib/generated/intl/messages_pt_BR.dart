// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a pt_BR locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'pt_BR';

  static String m0(limit) =>
      "Há apenas ${limit}x pesquisa de imagens na versão gratuita.";

  static String m1(limit) =>
      "Até ${limit} mensagens só podem ser exibidas na versão gratuita.";

  static String m2(date) => "Data de expiração da assinatura ${date}";

  static String m3(number) =>
      "Gerar (${number} ${Intl.plural(number, one: 'time', other: 'times')})";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about_openai": MessageLookupByLibrary.simpleMessage("Sobre"),
        "apply_openai": MessageLookupByLibrary.simpleMessage("Aplique"),
        "artist_openai": MessageLookupByLibrary.simpleMessage("Artista"),
        "cancel_openai": MessageLookupByLibrary.simpleMessage("Cancelar"),
        "chatDetail_openai":
            MessageLookupByLibrary.simpleMessage("Detalhes do bate-papo"),
        "chatGPT_openai": MessageLookupByLibrary.simpleMessage("Bate-papo GPT"),
        "chatWithBot_openai":
            MessageLookupByLibrary.simpleMessage("Bate-papo com bot"),
        "chat_openai": MessageLookupByLibrary.simpleMessage("Bate-papo"),
        "chooseArtist_openai": MessageLookupByLibrary.simpleMessage(
            "Escolha o artista para sua imagem"),
        "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
            "Escolha os detalhes da sua imagem"),
        "chooseMedium_openai": MessageLookupByLibrary.simpleMessage(
            "Escolha o meio para sua imagem"),
        "chooseMood_openai": MessageLookupByLibrary.simpleMessage(
            "Escolha o humor para sua imagem"),
        "chooseUseCase_openai":
            MessageLookupByLibrary.simpleMessage("Escolha o caso de uso"),
        "choseStyle_openai": MessageLookupByLibrary.simpleMessage(
            "Escolha o estilo para sua imagem"),
        "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
            "Tem certeza de que deseja limpar o conteúdo?"),
        "clearContent_openai":
            MessageLookupByLibrary.simpleMessage("Limpar conteúdo"),
        "clearConversation_openai":
            MessageLookupByLibrary.simpleMessage("Limpar conversa"),
        "clear_openai": MessageLookupByLibrary.simpleMessage("Claro"),
        "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
            "Tem certeza de que deseja excluir este item?"),
        "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
            "Confirme se deseja prosseguir com a exclusão deste item. Você não pode desfazer esta ação."),
        "confirmRemoveKey_openai": MessageLookupByLibrary.simpleMessage(
            "Tem certeza de remover a chave?"),
        "confirm_openai": MessageLookupByLibrary.simpleMessage("confirme"),
        "copiedToClipboard_openai": MessageLookupByLibrary.simpleMessage(
            "Conteúdo copiado para a área de transferência"),
        "copy_openai": MessageLookupByLibrary.simpleMessage("cópia de"),
        "createChatFailed_openai":
            MessageLookupByLibrary.simpleMessage("Falha ao criar bate-papo"),
        "deleteFailed_openai":
            MessageLookupByLibrary.simpleMessage("Falha na exclusão"),
        "delete_openai": MessageLookupByLibrary.simpleMessage("Excluir"),
        "detail_openai": MessageLookupByLibrary.simpleMessage("Detalhe"),
        "download_openai": MessageLookupByLibrary.simpleMessage("Baixar"),
        "edit_openai": MessageLookupByLibrary.simpleMessage("Editar"),
        "failedToGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Falha ao gerar"),
        "generate_openai": MessageLookupByLibrary.simpleMessage("Gerar"),
        "grid_openai": MessageLookupByLibrary.simpleMessage("Grade"),
        "imageGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Geração de imagem"),
        "imageSize_openai":
            MessageLookupByLibrary.simpleMessage("Tamanho da imagem"),
        "inputKey_openai":
            MessageLookupByLibrary.simpleMessage("Chave de entrada"),
        "interest_openai": MessageLookupByLibrary.simpleMessage("Interesse"),
        "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
            "Sua chave de API é armazenada localmente em seu celular e nunca é enviada para nenhum outro lugar. Você pode salvar sua chave para usá-la mais tarde. Você também pode remover sua chave se não quiser mais usá-la."),
        "invalidKey_openai":
            MessageLookupByLibrary.simpleMessage("Chave Inválida"),
        "jobRole_openai":
            MessageLookupByLibrary.simpleMessage("Cargo de Trabalho"),
        "jobSkills_openai":
            MessageLookupByLibrary.simpleMessage("Habilidades de trabalho"),
        "layoutStyle_openai":
            MessageLookupByLibrary.simpleMessage("Estilo de Layout"),
        "limitImage_openai": m0,
        "limitTheText_openai": m1,
        "listening_openai": MessageLookupByLibrary.simpleMessage("Audição..."),
        "loadKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("Falha ao Carregar Chave"),
        "loadKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("Carregar chave com sucesso"),
        "manage_openai": MessageLookupByLibrary.simpleMessage("gerir"),
        "medium_openai": MessageLookupByLibrary.simpleMessage("Médio"),
        "mood_openai": MessageLookupByLibrary.simpleMessage("Humor"),
        "moreOptions_openai":
            MessageLookupByLibrary.simpleMessage("Mais opções"),
        "newChat_openai": MessageLookupByLibrary.simpleMessage("Novo chat"),
        "noImageGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Nenhuma imagem gerada"),
        "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
            "O número de imagens a serem geradas. Deve estar entre 1 e 10."),
        "numberOfImages_openai":
            MessageLookupByLibrary.simpleMessage("Número de Imagens"),
        "options_openai": MessageLookupByLibrary.simpleMessage("Opções"),
        "page_openai": MessageLookupByLibrary.simpleMessage("Página"),
        "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
            "Por favor verifique sua conexão e tente novamente!"),
        "pleaseInputFillAllFields_openai": MessageLookupByLibrary.simpleMessage(
            "Por favor preencha todos os campos"),
        "pleaseInputKey_openai":
            MessageLookupByLibrary.simpleMessage("Insira a chave"),
        "prompt_openai": MessageLookupByLibrary.simpleMessage("Pronto"),
        "putKeyHere_openai":
            MessageLookupByLibrary.simpleMessage("Coloque sua chave aqui"),
        "regenerateResponse_openai":
            MessageLookupByLibrary.simpleMessage("Regenerar resposta"),
        "remaining_openai": MessageLookupByLibrary.simpleMessage("Restante"),
        "removeKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("Falha na remoção da chave"),
        "removeKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("Chave removida com sucesso"),
        "remove_openai": MessageLookupByLibrary.simpleMessage("Remover"),
        "resetSettings_openai":
            MessageLookupByLibrary.simpleMessage("Redefinir configurações"),
        "reset_openai": MessageLookupByLibrary.simpleMessage("Restabelecer"),
        "saveKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("Falha ao salvar chave"),
        "saveKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("Chave salva com sucesso"),
        "saveKey_openai": MessageLookupByLibrary.simpleMessage("Salvar chave"),
        "save_openai": MessageLookupByLibrary.simpleMessage("Salve "),
        "searchByPrompt_openai":
            MessageLookupByLibrary.simpleMessage("Pesquise por indicação..."),
        "sectionKeywords_openai":
            MessageLookupByLibrary.simpleMessage("Palavras-chave da seção"),
        "sectionTopic_openai":
            MessageLookupByLibrary.simpleMessage("Tópico da Seção"),
        "selectChatFailed_openai": MessageLookupByLibrary.simpleMessage(
            "Falha ao selecionar bate-papo"),
        "selectPrompt_openai":
            MessageLookupByLibrary.simpleMessage("Selecionar prompt"),
        "settings_openai": MessageLookupByLibrary.simpleMessage("Definições"),
        "share_openai": MessageLookupByLibrary.simpleMessage("Compartilhar"),
        "skills_openai": MessageLookupByLibrary.simpleMessage("habilidades"),
        "somethingWentWrong_openai":
            MessageLookupByLibrary.simpleMessage("Algo deu errado!!!"),
        "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
            "Algo deu errado! Por favor, tente novamente mais tarde. Muito obrigado!"),
        "speechNotAvailable_openai":
            MessageLookupByLibrary.simpleMessage("Discurso não disponível"),
        "style_openai": MessageLookupByLibrary.simpleMessage("Estilo"),
        "subscriptionExpiredDate_openai": m2,
        "tapTheMicToTalk_openai": MessageLookupByLibrary.simpleMessage(
            "Toque no microfone para falar"),
        "textGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Gerar texto"),
        "textGenerator_openai":
            MessageLookupByLibrary.simpleMessage("Gerador de texto"),
        "timeGenerate_openai": m3,
        "typeAMessage_openai":
            MessageLookupByLibrary.simpleMessage("Digite uma mensagem..."),
        "viewType_openai":
            MessageLookupByLibrary.simpleMessage("Tipo de visualização"),
        "view_openai": MessageLookupByLibrary.simpleMessage("Visão"),
        "write_openai": MessageLookupByLibrary.simpleMessage("Escrever")
      };
}
