import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:meditaguru/src/core/services/firebase/push_noti/fcm_debug_helper.dart';
import 'package:meditaguru/src/core/services/firebase/push_noti/local_push.dart';
import 'package:meditaguru/src/di/injection/injection.dart';

import '../../../utils.dart';

abstract class FirebaseCloudMessagagingAbs {
  void init();
  FirebaseCloudMessagingDelegate? delegate;
}

abstract class FirebaseCloudMessagingDelegate {
  void onMessage(Map<String, dynamic> message);
  void onResumeFCM(Map<String, dynamic> message);
  void onLaunch(Map<String, dynamic> message);
  void onTokenPushReceive(String token);
}

// permission
// token - refresh token
// foreground - background - terminated message
// local push - headup when in foreground
// receive data.type and navigate to screen by logic

class FirebaseCloudMessagagingWapper extends FirebaseCloudMessagagingAbs {
  FirebaseMessaging messaging = FirebaseMessaging.instance;

  @override
  void init() async {
    String? token = '';

    // Debug FCM status in debug mode
    if (kDebugMode) {
      await FCMDebugHelper.debugFCMStatus();
    }

    try {
      // Request permission first before getting token
      await requestPermission();

      // Get FCM token (remove vapidKey for iOS - it's only for web)
      token = await messaging.getToken();
      Log.printSimpleLog('[FCM] Successfully retrieved token: ${token?.substring(0, 20)}...');
    } catch (e) {
      Log.printSimpleLog('[FCM] Error getting token: $e');
      // Check if Firebase is properly initialized
      try {
        await messaging.requestPermission();
        Log.printSimpleLog('[FCM] Permission request successful, retrying token...');
        token = await messaging.getToken();
        Log.printSimpleLog('[FCM] Token retrieved on retry: ${token?.substring(0, 20)}...');
      } catch (retryError) {
        Log.printSimpleLog('[FCM] Retry failed: $retryError');
      }
    }

    delegate?.onTokenPushReceive(token ?? '');
    print('[FCM] Final token: ${token ?? 'NO_TOKEN'}');
    await firebaseCloudMessagingListeners();

    // Final debug check
    if (kDebugMode) {
      await FCMDebugHelper.debugFCMStatus();
    }
  }

  Future firebaseCloudMessagingListeners() async {
    await requestPermission();

    // Get any messages which caused the application to open from
    // a terminated state.
    var initialMessage = await FirebaseMessaging.instance.getInitialMessage();

    // If the message also contains a data property with a "type" of "chat",
    // navigate to a chat screen
    // if (initialMessage?.data['type'] == 'chat') {
    //   Navigator.pushNamed(context, '/chat',
    //       arguments: ChatArguments(initialMessage));
    // }
    if (initialMessage != null) {
      delegate?.onLaunch(initialMessage.data);
    }

    // Also handle any interaction when the app is in the background via a
    // Stream listener
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      // if (message.data['type'] == 'chat') {
      //   Navigator.pushNamed(context, '/chat',
      //       arguments: ChatArguments(message));
      // }
      delegate?.onResumeFCM(message.data);
    });

    // Foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      // if ((!MyRouteObserver.currentRootName?.contains(RouteList.chatScreen))
      //     || MyRouteObserver.currentRootName == null
      //     || MyRouteObserver.currentRootName?.isEmpty) {
      //   LocalPushService.showLocalNotificationForegroundAndroid(message);
      // }

      // Foreground Notifications
      injector<LocalPushService>()
          .showLocalNotificationForegroundAndroid(message);

      delegate?.onMessage(message.data);
      // print('Got a message whilst in the foreground!');
      // print('Message data: ${message.data}');
      //
      // if (message.notification != null) {
      //   print('Message also contained a notification: ${message.notification}');
      // }
    });

    // Any time the token refreshes, store this in the database too.
    FirebaseMessaging.instance.onTokenRefresh.listen((newToken) {
      delegate?.onTokenPushReceive(newToken);
    });
  }

  Future<void> requestPermission() async {
    await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: true,
      sound: true,
    );

    // Foreground Notifications
    await messaging.setForegroundNotificationPresentationOptions(
      alert:
          true, // Required to display a heads up notification - popup when foreground
      badge: true,
      sound: true,
    );
  }

  static Future pushToDevice(
      {required List<String> tokens,
      required String title,
      required String body}) async {
    var serverToken =
        'AAAA0tOc0dM:APA91bECPB5mKtbuiNEI9DdUL5PzjtyGtKDJsAoxKs8fvirYrDpRtq4LNq6zaxuAv0FGvDOKnkzm9PlyOZ8YQVjf_Dp0YuD-hi-HCG4AREJjqCAj5NOhVU1dHcy47AxgUiuTiGPEv2yx';
    var res = await http.post(
      Uri.parse('https://fcm.googleapis.com/fcm/send'),
      headers: <String, String>{
        'Content-Type': 'application/json',
        'Authorization': 'key=$serverToken',
      },
      body: jsonEncode(
        <String, dynamic>{
          'notification': <String, dynamic>{'title': title, 'body': body},
          // Required for background/quit data-only messages on iOS
          'contentAvailable': true,
          // Required for background/quit data-only messages on Android
          'priority': 'high',
          'data': <String, dynamic>{
            'click_action': 'FLUTTER_NOTIFICATION_CLICK',
            'id': '1',
            'status': 'done',
            'type': 'chat',
          },
          // if(tokens != null && tokens.length > 0)'to': tokens[0],
          'registration_ids': [...tokens]
        },
      ),
    );
    if (res.statusCode == 200) {
      Log.printSimpleLog(res.body);
      // Log.printSimpleLog(tokens[0]);
    } else {
      Log.printSimpleLog('${res.statusCode}');
    }
  }
}
